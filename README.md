# vea-api

> Back-end to support light-weight scheduling and test administration tool that connects Applicants, Test Administrators, Test Controllers, and the Certification Body.

## About

This project uses [Feathers](http://feathersjs.com). An open source web framework for building modern real-time applications.

## Getting Started

1. Make sure you have [NodeJS](https://nodejs.org/) and [npm](https://www.npmjs.com/) installed.
2. Install your dependencies

    ```
    npm install
    ```

3. Open an SSH tunnel to connect to the database. You will need to get some help
to know exactly the hosts & port to use, but the tunnel command should look
something like this:
    ```
    ssh ubuntu@************* -4 -L 29384:mpt-dev.cednqnegvay6.ca-central-1.rds.amazonaws.com:3306
    ```

4. Copy `config/development.example.json` to `config/development.json` and set
the DB port and password to match the SSH tunnel that you are using to connect
to the DB. You will need to do this twice: once for `mysql_write` and once for
`mysql_read`. `config/development.json` overrides the values in
`config/default.json`. Do not commit changes to `config/default.json` unless
you know exactly what you are doing!

5. Start your app

    ```
    npm run serve
    ```

## Testing

Simply run `npm test` and all your tests in the `test/` directory will be run.

## Scaffolding

Feathers has a powerful command line interface. Here are a few things it can do:

```
$ npm install -g @feathersjs/cli          # Install Feathers CLI

$ feathers generate service               # Generate a new Service
$ feathers generate hook                  # Generate a new Hook
$ feathers help                           # Show all commands
```

## Help

For more information on all the things you can do with Feathers visit [docs.feathersjs.com](http://docs.feathersjs.com).

### How are the database tables configured?

We were using the built-in feathers generator for all table services up until we established a read replica of the db, which tripped up the generator and required too much detailed work for each table construction. We've simplified this work, by using a small script which generates the table services.

1. Extract the list of tables here: `/db/table-list.json`
2. `id` is the table name in the database, object is used because we are likely to extend hooks here
3. Run the generator:
``` sh
node scripts/gen-db-boilerplate.js
```
1. See result in `/src/services/db/table-services.ts`

We continue to use the feathers generator for all other "custom" services, including anything directly accessible via the web on the `rest` API.

### Hooks

Hooks can be defined in the tables json (you will see some examples there). Currently, I am not seeing a need to parameterize the hooks, and that's a good way of keeping it simple. In the future, if we do need to feed parameters to the hooks, we could use a pipe delimiter.

Of course, we don't get type control while editing the JSON, but once you run the generator, it could be possible for us to simply maintain a list which would catch invalid inputs fairly quickly using typescript.

### `auths` table

The only excpetion to this process at the moment is the `auths` table, as it may be tightly wound up with the JWT middleware. The coupling may not be that bad, so it is possible to get back into this and pull this into the same structure.

### Vretta Coding Policies

- The physical MySQL dbschema is maintained in a separate repo (https://bubo.vretta.com/eqao/proc-cert-db).
- This repo uses the Knex database ORM plugin for feathers.
- This repo uses the `services/db` subfolder to reference each table interacted with by the API.
- DB table services should never be accessible from outside of the API, i.e., `dissallow('external')`
- If a method needs to called in more than one place, consider using a hook. Due to (possible) subtle differences in information stored for different account and role types, some boilerplate is encouraged to fascilitate ease of differention should it be required, but beyond that keep it DRY.
- All database tables should defined as interfaces. This is currently done manually, and so therefore needs to be verified during code reviews. Optional fields are those who can be excluded when a new row is being inserted (this includes fields that auto increment, have reasonable default values, or allow null).
- You will notice a significant use of `const` declarations. This is an emphasis on the use of immutability in most service calls.
- The recommended style is to declare a query field object (that is typed according to the corresponding table schema's interface) and use that for any `find`, `create`, or `patch`. This ensures that there is correspondence between the fields defined and the actual table fields. For all calls except for `create`, a `Partial<>` reference to the interface is used (since not all fields are required).
- The `db/_translations` is the only db-linked service that is open for external calls
- I have not yet found a great way to do parameter type validation, so whenever I need to do comparisons on values, I'm treating them as strings. For example: `(''+config.setupId) !== (''+mySetup.id)`
- The `db-limits.ts` file is intended to be kept in sync with the file of the same name in the client. This has to be done manually, but I don't expect it to be needed too often.

- `ssh <EMAIL>`


- new singular groups require api reboot in order to take effect (they get loaded into memory at the beginning)

# :rocket: Deploying API to Live
`release/eqao-main` to EQAO Grade, OSSLT, and MPT live sites.


### Syncing the API methods to the DB

`ts-node scripts/check-data-targets.ts`z


### filling up the database
INSERT INTO `mpt_dev`.`user_roles` (`role_type`, `uid`, `group_id`, `created_by_uid`) VALUES ('test_ctrl_meta_reg', '51', '24', '21');
INSERT INTO `mpt_dev`.`user_roles` (`role_type`, `uid`, `group_id`, `created_by_uid`) VALUES ('test_ctrl_lias_cert_body', '51', '24', '21');
INSERT INTO `mpt_dev`.`user_roles` (`role_type`, `uid`, `group_id`, `created_by_uid`) VALUES ('test_ctrl_lias_test_admin', '51', '24', '21');
INSERT INTO `mpt_dev`.`user_roles` (`role_type`, `uid`, `group_id`, `created_by_uid`) VALUES ('test_ctrl_data_retr', '51', '24', '21');
INSERT INTO `mpt_dev`.`user_roles` (`role_type`, `uid`, `group_id`, `created_by_uid`) VALUES ('test_ctrl_lias_internal', '51', '24', '21');
INSERT INTO `mpt_dev`.`user_roles` (`role_type`, `uid`, `group_id`, `created_by_uid`) VALUES ('debug', '51', '1', '21');

test for Nick
# Legacy Info

Everything after this sentence is outdated, and is still included in this file
only for reference purposes.

# Refreshing the server

Can currently use this to reset the server with freshly pulled code:
```
npm run compile
pm2 restart 0;
```

## tunnel
Tunnel in to the server side use

Write:
```
ssh ubuntu@************* -L 29384:mpt-dev.cednqnegvay6.ca-central-1.rds.amazonaws.com:3306
```

Read:
```
ssh ubuntu@************* -L 29385:mpt-dev-read.cednqnegvay6.ca-central-1.rds.amazonaws.com:3306
```

Pentest:
```
ssh ubuntu@************ -L 29386:mpt-xqc1-master.cednqnegvay6.ca-central-1.rds.amazonaws.com:3306
```

Then to have the locally running API use the tunnel, there are a couple of small modifications need to be applied to `default.json`, in the connection object:
```
"host" : "localhost",
"port" : "29384",
```

See step 2 here: https://bubo.vretta.com/snippets/29

## DB schema to interface

We are using schemats

You can install it like this
```
npm install -g schemats
```

Then run

```
schemats generate -c mysql://mptdev:ti4XMMmC1kFyHfRWHnNr@localhost:29384/mpt_dev -t users -o src/services/db/db-models.ts
```

This should work but doesn't right now. I suspect that it's because I am not connecting via SSL, but it is hard to be sure given the generic error that I am seeing. Manually updating the schemas for now...

This will give you the old db, which is obsolete

```
schemats generate -c mysql://api:<EMAIL>/app -o src/services/db/db-models.ts

```


to deploy to qc1, ssh into the main inspector then
```
ssh *********
```

while : ; do ssh eqao-db-aurora ; done

Force API rebuild on 20241228


