stages:
   - build_latest_api
   - deploy_test_api
   - test_latest_api_build
   - release_api_build
   - deploy_api
   - check_endpoints
   - finalize_deployment

# pull down S3 shell scripts and do any other tasks
# before running any CI/CD jobs
before_script:
  - . $CI_SCRIPT_BASE_DIR/bootstrap_cicd_env.sh

after_script:
  - . $CI_SCRIPT_BASE_DIR/process_cicd_notifications.sh

# Only permit this pipeline to run on branches with "release/" in the name

workflow:
  rules:
    - if: $CI_COMMIT_BRANCH =~ /^release\//

# Create function for setting CI scripts to be executable

.shell_script: &configure_ci_scripts
  - echo "Setting CI scripts to executable within ${CI_SCRIPT_BASE_DIR}..."
  - . $CI_SCRIPT_BASE_DIR/bootstrap_cicd_env.sh
# We can place additional variables here to control API build / deployment processes when the Gitlab pipeline runs

variables:

  # Directory containing bash scripts run during GitLab CI stages

  CI_SCRIPT_BASE_DIR: "./scripts/gitlab-ci-scripts/api-deploy-automation/bash"
  CI_SCRIPT_DIR: "./scripts/gitlab-ci-scripts/api-deploy-automation/bash/s3"

  # AWS related variables

  AWS_ECR_NM: "vrt-ansible-repository"                            # Name of ECR on AWS to pull container from
  AWS_ECR_CONTAINER_NM: "vrt-ansible"                             # Name of container on ECR to pull
  AWS_ECR_CONTAINER_TAG: "2023-08-11-ansible-py3.11"              # Which tag to pull from ECR for the container
  AWS_ECR_REGION: "ca-central-1"                                  # Region on AWS where ECR to pull container from is located
  AWS_LOCAL_CONTAINER_NM: "vrt-ansible:2023-08-11-ansible-py3.11" # Name / tag to assign container on Docker runner once pulled from

  # Specify Ansible variables here for the inventory file to use and playbook to call for updating API instances

  ANSIBLE_INVENTORY_NM: api_inventory-${CI_COMMIT_SHA}.yml
  ANSIBLE_PLAYBOOK: "./scripts/gitlab-ci-scripts/api-deploy-automation/ansible/update_api.yml"

  # Note: The generated Ansible inventory file includes the Git commit hash in its name to give it a unique name each
  #       time. This is because this file is passed to another stage as an artifact. If this file does not have a unique
  #       name, then an old artifact could be used by Ansible instead which would not include the most recent query of
  #       live API instance IDs.
  
  # bucket to pull CI/CD shell scripts from
  S3_CICD_BUCKET: "vea-api-cicd-scripts-abed"
