{"name": "proc-cert-api", "description": "Back-end to support light-weight scheduling and test administration tool that connects Applicants, Test Administrators, Test Controllers, and the Certification Body.", "version": "0.0.0", "homepage": "", "main": "src", "keywords": ["feathers"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {}, "directories": {"lib": "src", "test": "test/", "config": "config/"}, "engines": {"node": "^10.0.0", "npm": ">= 3.0.0"}, "scripts": {"test": "npm run compile && npm run mocha", "dev": "ts-node-dev --no-notify src/", "start": "node lib/", "serve": "npm run compile && node lib/ --trace-warnings", "production-serve": "isProduction=true npm run serve", "mocha": "ts-mocha \"test/**/*.ts\" --recursive --exit", "compile": "shx rm -rf lib/ && tsc --extendedDiagnostics"}, "types": "lib/", "dependencies": {"@aws-sdk/client-cloudfront": "^3.410.0", "@aws-sdk/cloudfront-signer": "^3.410.0", "@feathersjs/authentication": "^4.3.7", "@feathersjs/authentication-local": "^4.3.7", "@feathersjs/authentication-oauth": "^4.3.7", "@feathersjs/cli": "^4.5.0", "@feathersjs/configuration": "^4.3.7", "@feathersjs/errors": "^4.3.7", "@feathersjs/express": "^4.3.7", "@feathersjs/feathers": "^4.3.7", "@feathersjs/socketio": "^4.3.7", "@types/lodash": "^4.14.149", "@types/seedrandom": "^3.0.1", "async": "^3.1.0", "async-es": "^2.6.3", "aws-sdk": "^2.570.0", "axios": "^0.21.1", "bcrypt": "^5.0.1", "bluebird": "^3.7.2", "compression": "^1.7.4", "cors": "^2.8.5", "cron": "^1.8.2", "csvtojson": "^2.0.10", "entities": "^6.0.0", "exceljs": "^4.2.1", "express-fileupload": "^1.1.6-alpha.6", "feathers-hooks-common": "^4.20.7", "feathers-knex": "^8.0.1", "feathers-swagger": "^1.1.0", "helmet": "^3.21.2", "ioredis": "^5.2.2", "ip-cidr": "^2.1.5", "jwt-decode": "^2.2.0", "knex": "^0.95.15", "lodash": "^4.17.15", "lz-string": "^1.4.4", "markdown": "^0.5.0", "marked": "^0.7.0", "moment": "^2.24.0", "moment-timezone": "^0.5.31", "mysql2": "^1.7.0", "node-object-hash": "^2.3.10", "pdf-to-base64": "^1.0.3", "pdfkit": "^0.14.0", "qrcode": "^1.5.3", "qs": "^6.13.1", "random-name": "^0.1.2", "redlock": "^5.0.0-beta.2", "safe-json-stringify": "^1.2.0", "seedrandom": "^3.0.5", "serve-favicon": "^2.5.0", "short-uuid": "^4.2.0", "speakeasy": "^2.0.0", "sqlstring": "^2.3.2", "stripe": "^8.203.0", "turndown": "^7.0.0", "ua-parser-js": "^0.7.24", "uuid": "^8.3.2", "winston": "^3.7.2", "xlsx": "^0.16.9", "xml-js": "^1.6.11"}, "devDependencies": {"@types/bluebird": "^3.5.36", "@types/compression": "0.0.36", "@types/cors": "^2.8.5", "@types/helmet": "0.0.44", "@types/jsonwebtoken": "^8.3.2", "@types/mocha": "^5.2.7", "@types/pdfkit": "^0.13.4", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.17", "@types/random-name": "^0.1.0", "@types/serve-favicon": "^2.2.31", "@types/uuid": "^8.3.0", "axios": "^0.18.1", "fs-extra": "^9.1.0", "mocha": "^6.2.2", "mustache": "^3.1.0", "nodemon": "^1.19.4", "shx": "^0.3.3", "ts-mocha": "^6.0.0", "ts-node-dev": "^1.0.0-pre.43", "tslint": "^5.20.0", "typescript": "^4.4.3"}}