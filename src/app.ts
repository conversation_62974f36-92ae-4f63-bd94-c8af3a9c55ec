import path from 'path';
import favicon from 'serve-favicon';
import compress from 'compression';
import helmet from 'helmet';
import cors from 'cors';

import feathers from '@feathersjs/feathers';
import configuration from '@feathersjs/configuration';
import express from '@feathersjs/express';
import socketio from '@feathersjs/socketio';

import { Application } from './declarations';
import logger from './logger';
import middleware from './middleware';
import services from './services';
import appHooks from './app.hooks';
import channels from './channels';
import authentication from './authentication';
import { setupUploadListener } from './services/upload/upload.listener';
import { setupDownloadListener } from './services/download-data-frame/download.listener';
import { setupUpTstCtrl } from './constants/test-ctrl-constant'
// import updatePendingBookings from './scripts/check-pending-bookings';
// import markCreditsAsConsumedIfPastBufferStart from './scripts/mark-credits-as-consumed';
import { IncomingMessage } from 'http'
import veaDb from './sql/vea-db';
import veaRedisConn from './redis/redis';
import { scanningService } from './scanning';
export interface IRequestRawBody extends IncomingMessage {
  bodyRaw: Buffer,
  bodyEncoding: string
}
// import revokeWaitlistApplicantsIfPastBufferStart from './scripts/check-waitlists-for-autocancelling';

function setLogLevelFromConf (newLevel:string) {
  if (!newLevel) {
    return;
  }
  if (Object.keys(logger.levels).indexOf(newLevel) === -1) {
    return;
  }
  return logger.level = newLevel;
}


const app: Application = express(feathers());

// Load app configuration
app.configure(configuration());
setLogLevelFromConf(app.get('logLevel'));
// Enable security, CORS, compression, favicon and body parsing
app.use(helmet());
app.use(cors());
app.use(compress());
app.use(express.json({
  limit: app.get('httpReqJsonSizeLimit'),
  verify: (req: IRequestRawBody, res, bodyRaw, bodyEncoding) => {
    const rawEndpoints: string[] = ['/public/transactions/stripe'];
    if (req.url && rawEndpoints.includes(req.url)) {
      req.bodyRaw = bodyRaw;
      req.bodyEncoding = bodyEncoding;
    }
  }
}));
app.use(express.urlencoded({ limit: '20mb', extended: true }));
app.use(favicon(path.join(app.get('public'), 'favicon.ico')));

// Set up Plugins and providers
app.configure(express.rest())
.use(function(req, res, next) {
    if(req.feathers){
      req.feathers.ip = req.headers['x-forwarded-for'] || req.ip
      req.feathers.originalUrl = req.originalUrl;
      setupUpTstCtrl(req.headers.host)
    }
  next();
});

// app.configure(socketio(function(io) {io.sockets.setMaxListeners(0);}));
app.configure((app: Application) =>
  app.set('knexClientWrite', veaDb(app.get('mysql_write'), 'write'))
);
app.configure((app: Application) =>
  app.set('knexClientRead', veaDb(app.get('mysql_read'), 'read'))
);
app.configure((app: Application) => {
  let config = app.get('mysql_read_reporting');
  if (!config) {
    logger.warn(`config for 'mysql_read_reporting' db connection not found, falling back to 'mysql_read' config`);
    config = app.get('mysql_read');
  }
  app.set('knexClientReadReporting', veaDb(config, 'read'));
});

app.configure((app: Application) => {
  const redisConfig = app.get('redis');
  if (!redisConfig) {
    throw new Error('redis config not found, see https://www.notion.so/vretta/Redis-Configuration-64a23f44c10e4ca7821e4e807b32ed32?pvs=4');
  }
  const isDevMode = !!app.get('isDevMode');

  app.set('redis', veaRedisConn(redisConfig, isDevMode))
});

app.configure(scanningService);

// Configure other middleware (see `middleware/index.js`)
app.configure(middleware);
// Set up our services (see `services/index.js`)
app.configure(services);

setupUploadListener(app);
setupDownloadListener(app);

// Set up event channels (see channels.js)
app.configure(channels);

app.configure(authentication);
// Configure a middleware for 404s and the error handler
app.use(express.notFound());
app.use(express.errorHandler({ logger } as any));

app.hooks(appHooks);


// updatePendingBookings.start();
// markCreditsAsConsumedIfPastBufferStart.start();
// revokeWaitlistApplicantsIfPastBufferStart.start();

app.service('public/cron/track-memory-usage').create(
  { memoryUsage: 0, status: "running" },
  {}
);

export default app;
