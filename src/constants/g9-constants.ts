export const boardTechReadinessFlagsRequired = [
  'board_tech_redi_1',
  'board_tech_redi_txt_2',
  //'board_tech_redi_3',
  //'board_tech_redi_4',
  //'board_tech_redi_5',
  'board_tech_read_check_dev',
  'board_tech_redi_6',
  'board_tech_redi_7',
  'board_tech_redi_9',
  'board_tech_redi_8',
  'board_tech_redi_10',
  'board_tech_redi_11',
  'board_tech_redi_12',
  'board_tech_redi_13',
  'board_tech_redi_14',
  'board_tech_redi_15',
  'board_tech_redi_16',
  'board_tech_redi_17',
];

export const boardTechReadinessFlags = boardTechReadinessFlagsRequired.concat([

])

export const primaryTechReadinessFlagsRequired = [
  'tech_redi_training_primary',
  //'tech_redi_school_board',
  'tech_redi_school_board_safe_sender_pj',
  'tech_redi_students_primary',
  'tech_redi_accomm_pj_primary',
  'tech_redi_classrooms_pj_primary',
  'tech_redi_teacher_acc_pj_primary',
  'tech_redi_device_1_pj_primary',
  'tech_redi_school_board_check_scanner_pj_primary'
];

export const juniorTechReadinessFlagsRequired = [
  'tech_redi_training_junior',
  //'tech_redi_school_board',
  'tech_redi_school_board_safe_sender_pj',
  'tech_redi_students_junior',
  'tech_redi_accomm_pj_junior',
  'tech_redi_classrooms_pj_junior',
  'tech_redi_teacher_acc_pj_junior',
  'tech_redi_device_1_pj_junior',
  'tech_redi_school_board_check_scanner_pj_junior'
];

export const techReadinessFlagsRequired = [
  //'tech_redi_school_board',
  'tech_redi_school_board_safe_sender',
  // 'tech_redi_students',
  // 'tech_redi_accomm',
  'tech_redi_classrooms',
  'tech_redi_teacher_acc',
  'tech_redi_device_1',
];

export const privatePrimaryTechReadinessFlagsRequired = [
  "tech_redi_training_primary",
  "tech_redi_school_board_safe_sender_pj",
  "tech_redi_payments_review_pj_primary",
  "tech_redi_payments_pj_primary",
  "tech_redi_classrooms_pj_primary",
  "tech_redi_teacher_acc_pj_primary",
  "tech_redi_students_pj_primary",
  "tech_redi_accomm_pj_primary",
  "tech_redi_device_primary",
  "tech_redi_bandwidth_primary",
  "tech_redi_network_1_primary",
  "tech_redi_network_2_primary",
  "tech_redi_network_3_primary",
  "tech_redi_network_4_primary",
  "tech_redi_network_5_primary",
  "tech_redi_bandwidth_2_primary",
  "tech_redi_internal_network_primary",
  "tech_redi_outgoing_1_primary",
  "tech_redi_outgoing_2_primary",
  "tech_redi_incident_primary"
]

export const privatePrimaryExcludePaymentflagRequire = [
  "tech_redi_payments_review_pj_primary",
  "tech_redi_payments_pj_primary",
]


export const privateJuniorTechReadinessFlagsRequired = [
  "tech_redi_training_junior",
  "tech_redi_school_board_safe_sender_pj",
  "tech_redi_payments_review_pj_junior",
  "tech_redi_payments_pj_junior",
  "tech_redi_classrooms_pj_junior",
  "tech_redi_teacher_acc_pj_junior",
  "tech_redi_students_pj_junior",
  "tech_redi_accomm_pj_junior",
  "tech_redi_device_junior",
  "tech_redi_bandwidth_junior",
  "tech_redi_network_1_junior",
  "tech_redi_network_2_junior",
  "tech_redi_network_3_junior",
  "tech_redi_network_4_junior",
  "tech_redi_network_5_junior",
  "tech_redi_bandwidth_2_junior",
  "tech_redi_internal_network_junior",
  "tech_redi_outgoing_1_junior",
  "tech_redi_outgoing_2_junior",
  "tech_redi_incident_junior"
]

export const privateJuniorExcludePaymentflagRequire = [
  "tech_redi_payments_review_pj_junior",
  "tech_redi_payments_pj_junior",
]

export const privateTechReadinessFlagsRequired = [
  "tech_redi_safe_sender",
  "tech_redi_domain_exclusion",
  "tech_redi_payments_review",
  "tech_redi_payments",
  "tech_redi_groupings",
  "tech_redi_teachers",
  // "tech_redi_students",
  // "tech_redi_accomm",
  "tech_redi_device",
  "tech_redi_bandwidth",
  "tech_redi_network_1",
  "tech_redi_network_2",
  "tech_redi_network_3",
  "tech_redi_network_4",
  "tech_redi_network_5",
  "tech_redi_bandwidth_2",
  "tech_redi_internal_network",
  "tech_redi_outgoing_1",
  "tech_redi_outgoing_2",
  "tech_redi_incident"
]

export const privateExcludePaymentflagRequire = [
  "tech_redi_payments_review",
  "tech_redi_payments",
]

export const privateOssltTechReadinessFlagsRequired = [
  "tech_redi_safe_sender",
  "tech_redi_domain_exclusion_osslt",
  "tech_redi_payments_review_osslt",
  "tech_redi_payments_osslt",
  "tech_redi_groupings_osslt",
  "tech_redi_teachers_osslt",
  "tech_redi_students_osslt",
  "tech_redi_accomm_osslt",
  "tech_redi_device_osslt",
  "tech_redi_bandwidth_osslt",
  "tech_redi_network_1_osslt",
  "tech_redi_network_2_osslt",
  "tech_redi_network_3_osslt",
  "tech_redi_network_4_osslt",
  "tech_redi_network_5_osslt",
  "tech_redi_bandwidth_2_osslt",
  "tech_redi_internal_network_osslt",
  "tech_redi_outgoing_1_osslt",
  "tech_redi_outgoing_2_osslt",
  "tech_redi_incident_osslt"
]

export const privateOssltExcludePaymentflagRequire = [
  "tech_redi_payments_review_osslt",
  "tech_redi_payments_osslt",
]

export const ossltTechReadinessFlagsRequired = [
  //'tech_redi_school_board',
  'tech_redi_school_board_safe_sender',
  'tech_redi_students_osslt',
  'tech_redi_accomm_osslt',
  'tech_redi_classrooms_osslt',
  'tech_redi_teacher_acc_osslt',
  'tech_redi_device_1_osslt',
];

export const techReadinessFlags = techReadinessFlagsRequired.concat([
  'devices_seb',
  'devices_seb_config',
  'devices_kiosk'
])

export const techReadiPrimaryGoMode = 'tech_redi_go_mode_primary';
export const techReadiJuniorGoMode = 'tech_redi_go_mode_junior';
export const techReadiGoMode = 'tech_redi_go_mode';
export const techReadiOSSLTGoMode = 'tech_redi_go_mode_osslt';

export const primaySuffix = '_primary';
export const juniorSuffix = '_junior';
export const ossltSuffix = '_osslt';

export enum ADMIN_CHECK_TYPE {
  PRIMARY_ADMIN = 'PRIMARY_ADMIN',
  JUNIOR_ADMIN = 'JUNIOR_ADMIN',
  G9_ADMIN = 'G9_ADMIN',
  OSSLT_ADMIN = 'OSSLT_ADMIN',
}

