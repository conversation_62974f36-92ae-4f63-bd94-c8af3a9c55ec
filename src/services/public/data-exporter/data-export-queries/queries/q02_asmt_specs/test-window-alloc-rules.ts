import { IExportQueryDef } from "../../types/type";

interface ITwtarConfig {
    include_sample_assessments: boolean,
    include_questionnaires: boolean,
    only_secured_twtdar: boolean,
    twtar_type_slugs: string[],
    only_human_marked_twtdar: boolean,
}

export const SQL_02_ASMT_SPECS_TEST_WINDOW_ALLOC_RULES:IExportQueryDef = {
    requiredInputs: [
        'tw_ids',
    ],
    optionalInputs: [
        'twtar_type_slugs',
        'include_sample_assessments', // config only
        'include_questionnaire', // config only
        'only_secured_twtdar', // config only
        'only_human_marked_twtdar',
    ],
    queryGen: (config:ITwtarConfig) => `
        select twtar.id twtar_id
            , twtar.is_active
            , twtar.test_window_id
            , twtar.type_slug
            , twtar.form_code
            , twtar.component_slug
            , twtar.is_secured
            , twtar.is_questionnaire
            , twtar.is_sample
            , twtar.is_field_test
            , twtar.is_active_for_qa
            , twtar.is_swap_risk
            , twtar.lang
            , ifnull(twtar.tqr_ovrd_td_id, twtar.test_design_id) td_id
            , twtar.test_design_id alloc_td_id
            , twtar.tqr_ovrd_td_id override_td_id
            , twtar.long_name
            , twtar.test_duration
            , twtar.selection_order
            , twtar.is_school_allowed_strict
            , twtar.is_classroom_common_form
            , twtt.caption_short
            , twtt.course_code
            , twtt.cover_security_msg
            , twtt.is_simple_cover
            , twtt.foreign_component_code
            , twtt.is_perusal_allow
            , twtt.is_download_results
            , twtt.is_local_score
            , twtt.resource_td_id
            , twtt.resource_caption
            , ac.course_code_foreign
            , ac.course_name_short
            , ac.course_name_full
        from test_window_td_alloc_rules twtar
        left join test_window_td_types twtt
            on twtt.type_slug = twtar.type_slug
            and twtt.test_window_id is null
            and twtt.is_revoked = 0
        left join assessment_courses ac
            on ac.course_code = twtt.course_code
        where twtar.test_window_id in (:tw_ids)
          ${ config.include_sample_assessments ? '' : 'and twtar.is_sample = 0' }
          ${ config.include_questionnaires ? '' : 'and twtar.is_questionnaire = 0' }
          ${ config.only_secured_twtdar ? 'and twtar.is_secured = 1' : '' }
          ${ config.twtar_type_slugs && config.twtar_type_slugs.length > 0 ? 'and twtar.type_slug in (:twtar_type_slugs)' : '' }
          ${ config.only_human_marked_twtdar ? 'and twtar.is_marking_req = 1' : '' }
   `
}


