import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbDateNow } from '../../../../util/db-dates';
import { currentUid } from '../../../../util/uid';
import EqaoDataInstance from '../../../../util/eqao-data-instance';
import { camelify, snakeify } from '../../../../util/caseify';
import { dbRawReadSingle } from '../../../../util/db-raw';
import { mapValues } from 'lodash';
import { generateS3DownloadUrl } from '../../../upload/upload.listener';

export enum PsychRunStatus {
  WAITING = 'WAITING',
  RUNNING = 'RUNNING',
  COMPLETE = 'COMPLETE',
  ERROR = 'ERROR'
}
interface Data {
  exportId: number,
  exportApiBase?: string,
  failureReason?: string,
  status?: PsychRunStatus,
  pid?: number,
  startedOn?: any,
  urls?: any
}

interface ServiceOptions {}

export class DataPsychRun implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;
  instance: EqaoDataInstance;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
    this.instance = new EqaoDataInstance(app);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    const results =  camelify(await this.app.service('db/write/data-psych-runs').get(id)) as Data;
    if (!results.urls) {
      results.urls = {};
    } else {
      results.urls = JSON.parse(results.urls);
    }
    results.urls = mapValues(results.urls, (value) => {
      return generateS3DownloadUrl(value.replace('s3://storage.mathproficiencytest.ca/',''))
    })
    return results;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params: Params): Promise<Data> {
    if (Array.isArray(data)) {
      throw new Errors.BadRequest('only one export run can be created per http request');
    }

    const { exportId, exportApiBase } = data;
    const currentUID = await currentUid(this.app, params);
    const dataInstanceIP = await this.instance.getInstanceIP();
    const forceExportId = params?.query?.forceExportId;

    let psychRun;
    if (forceExportId) {
      psychRun = await this.app.service('db/read/data-psych-runs').get(forceExportId);
    } else {
      psychRun = await this.app.service('db/write/data-psych-runs').create({
        export_id: exportId,
        export_api_base: exportApiBase || `http://${dataInstanceIP}:3030`,
        created_on: dbDateNow(this.app),
        created_by_uid: currentUID
      });
      
      const latestConfig = await dbRawReadSingle(this.app, [], `
        SELECT * FROM psych_pipeline_configurations 
        ORDER BY ID DESC 
        LIMIT 1
      `);
      const psychHistory = await this.app.service('db/write/psych-config-history').create({
        psych_run_id: psychRun.id,
        psych_pipeline_configurations_id: latestConfig.id
      });
    }

    return psychRun.id;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Partial<Data>, params?: Params): Promise<Data> {
    if (!id || !data) {
      throw new Errors.BadRequest;
    }
    delete (data as any).id
    return await this.app.service('db/write/data-psych-runs').patch(id, snakeify(data))
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }
}
