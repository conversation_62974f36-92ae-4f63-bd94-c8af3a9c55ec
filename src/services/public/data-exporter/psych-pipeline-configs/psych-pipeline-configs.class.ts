import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbDateNow } from '../../../../util/db-dates';
import { currentUid } from '../../../../util/uid';
import EqaoDataInstance from '../../../../util/eqao-data-instance';
import { camelify, snakeify } from '../../../../util/caseify';
import { dbRawRead } from '../../../../util/db-raw';

interface Data {
  testWindowId: number,
  assessmentType?: string,
  reportingSubject?: number,
  cutPoints?: string,
  cutPointsAlt?: string,
  dotScores?: string,
  dotScoresAlt?: string,
  cttFlags?: string,
  removeFromScoring?: string,
  removeFromEquating?: string,
  adaptData?: string,
  calibrationSample?: string,
  dummyResponsesZero?: string,
  dummyResponsesSecond?: string,
  dummyN?: string,
  ssItems?: string,
  duplicatedItems?: string,
  fieldItems?: string
}

interface ServiceOptions {}

export class PsychPipelineConfigs implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;
  instance: EqaoDataInstance;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
    this.instance = new EqaoDataInstance(app);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return dbRawRead(this.app, [], `
      select * 
      from psych_pipeline_configurations
      ORDER BY id DESC
    `);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return camelify(await this.app.service('db/read/psych-pipeline-configs').get(id)) as Data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params: Params): Promise<Data> {
    const newConfig = await this.app.service('db/write/psych-pipeline-configs').create(snakeify(data));

    return newConfig;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Partial<Data>, params?: Params): Promise<Data> {
    // throw new Errors.NotImplemented();
    if (!id || !data) {
      throw new Errors.BadRequest;
    }
    delete (data as any).id
    return await this.app.service('db/write/psych-pipeline-configs').patch(id, snakeify(data))
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }
}
