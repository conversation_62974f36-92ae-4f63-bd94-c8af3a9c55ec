import { Id, NullableId, <PERSON><PERSON>ated, Para<PERSON>, ServiceMethods } from '@feathersjs/feathers';
import { stringList } from 'aws-sdk/clients/datapipeline';
import { DBD_U_ROLE_TYPES } from '../../../../constants/db-extracts';
import { defaultDomain } from '../../../../constants/mail-constants';
import { Application } from '../../../../declarations';
import {Errors} from "../../../../errors/general";
import { AccountType } from '../../../../types/account-types';
import { normalizeDomain } from '../../../../util/domain-whitelist';
import { IUserRole } from '../../../db/schemas/user-roles.schema';
import {IUGroupSingular} from "../../../db/schemas/u_groups_singular.schema";
import { Knex } from 'knex';
import { dbDateOffsetDays } from '../../../../util/db-dates';
import { renderInvitationCode } from '../../../../util/secret-codes';
import { dbRawRead } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';
import { IRoleDef } from '../../../auth/invitation/invitation.class';
import logger from '../../../../logger';

interface Data {}

export interface IBoardItInviteEmail {
  FIRST_NAME: string,
  LAST_NAME: string,
  BOARD_NAME: string,
  LANG_CODE: string,
  EMAIL_ENCODED: string,
  INVITATION_CODE: string,
  [index:string] : string,
}



interface ServiceOptions {}
export interface INewUserInfo {
  first_name?: string,
  last_name?: string,
  email?: string,
  isAutoEmail?: boolean,
  langCode?: string,
  domain?: string,
  role_type?: string,
}
interface INewDistrictAdmin {
  foreign_id: number,
  name: string,
  brd_lang: string,
  brd_type: string,
  address: string,
  city: string,
  province: string,
  postal_code: string,
  phone_number: number,
  fax_number: number,
  print_order?: number,
  lockdown_file_kiosk: string
}
export interface INewAccount{
   schoolDistrict:INewDistrictAdmin,
   user:INewUserInfo
}
export class District implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {

    // const existingDistricts = <Paginated<any>> await this.app
    //   .service('db/read/school-districts')
    //   .find({ query: {$limit:5000} });
    // if (existingDistricts.total > 0){
    //   return existingDistricts;
    // }

    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    const uid = userInfo.uid;

    if (params && params.query) {
      const districts = <Paginated<any>> await this.app.service('db/read/user-role-w-group-type').find({
        query: {
          $limit: 1,
          uid,
          role_type: 'schl_dist_admin',
          is_revoked: 0
        }
      });
      if (districts.data.length > 0) {
        return [districts.data[0]];
      } else {
        return [];
      }
    }
    throw new Errors.BadRequest();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create (data: INewAccount[], params?: Params): Promise<Data> {
    // return <any> this.grantDistAdminRoleAndEmail(<any> data, params);
    // return <any> this.inviteNewBoardAdmins(<any> data, params);
    // return <any> this.createRangeFinderAccounts();
    throw new Errors.MethodNotAllowed();
    // this.sendInvitations();
    // await this.createDistAdminAccount(
    //   1190, 21, 'en', 'http://eqao.vretta.com',
    //   {
    //     first_name: '',
    //     last_name: '',
    //     email: '',
    //   }
    // )
    return [];
  }


  async getInvitationRecords(){
    return dbRawRead(this.app, [], `
      select sd.id as sd_id, sd.brd_lang as lang, sd.name as sd_name, ur.uid, u.first_name, u.last_name, u.contact_email, i.id as invite_id, i.secret_key , i.expire_on, i.is_auto_email, i.is_revoked
      from school_districts sd
      join user_roles ur on ur.group_id = sd.group_id and ur.role_type = 'schl_dist_admin' and ur.is_revoked != 1
      join users u on ur.uid = u.id
      left join u_invites i on i.uid = ur.uid
      where i.is_auto_email != 1
        and i.is_revoked != 1
      group by sd.id, ur.role_type
    ;`);
  }

  async sendInvitations(allowedEmailList?:string[]){
    const res = [];
    let records = await this.getInvitationRecords();
    if (allowedEmailList){
      records = records.filter(r => allowedEmailList.indexOf(r.contact_email) > -1 );
    }
    for (let i=0; i<records.length; i++){
      logger.silly('sending invitation %d', i);
      const record = records[i];
      const langCode = record.lang;
      const domain = 'eqao-api.vretta.com'; // there is some confusion around domain and whitelabel at the moment...
      let contact_email = record.contact_email;
      // contact_email = '<EMAIL>'; // debug
      const invitationCode = renderInvitationCode(<number>record.invite_id, record.secret_key);
      const EMAIL_ENCODED = encodeURIComponent(contact_email);
      const emailTemplateParams:IBoardItInviteEmail = {
        FIRST_NAME: record.first_name,
            LAST_NAME: record.last_name,
            BOARD_NAME: record.sd_name,
            LANG_CODE: langCode,
            EMAIL_ENCODED,
            INVITATION_CODE: invitationCode
      }
      const mailRes = await this.app
        .service('auth/invitation')
        .sendEmail(
          contact_email,
          'subj_email_acct_invite_sd_admin',
          'email_acct_invite_sd_admin',
          emailTemplateParams,
          invitationCode,
          langCode,
          domain
        );
        await this.app
          .service('db/write/u-invites')
          .patch(record.invite_id, {
            is_auto_email: 1,
          })
        res.push(mailRes);
    }
    return res;
  }

  async ensureInvitations(){
    const offsetDays = 8;
    const records = await this.getInvitationRecords();
    const invites = [];
    for (let i=0; i<records.length; i++){
      logger.silly('ensuring invitation %d', i);
      const record = records[i];
      if(record.secret_key){
        await this.app
          .service('db/write/u-invites')
          .patch(record.invite_id, {
            expire_on: dbDateOffsetDays(this.app, offsetDays),
          })
      }
      else{
        const invite = await this.app
          .service('auth/invitation')
          .createInvitation({
            uid: record.uid,
            created_by_uid: 21,
            isAutoEmail: false,
            contact_email: record.contact_email,
            invit_meta:'{}',
            offsetDays,
          });
        invites.push(invite);
      }
    }
    return invites;
  }

// <<<<<<< HEAD
  async generateNewBoardsFlat(data:any[], params?:any){
    const structuredData = data.map(record => {
      return {
        user: {
          first_name: record.first_name,
          last_name: record.last_name,
          email: record.email,
          password: record.password,
          isAutoEmail: false,
          langCode: record.brd_lang,
        },
        schoolDistrict: {
          foreign_id: record.district_id,
          name: record.district_name,
          brd_lang: record.brd_lang,
          address: 'UNKNOWN',
          city: 'UNKNOWN',
          province: 'UNKNOWN',
          postal_code: 'UNKNOWN',
          phone_number: 'UNKNOWN',
          fax_number: 'UNKNOWN',
          print_order: 1,
          lockdown_file_kiosk: ''

        }
      }
    })
    return this.generateNewBoards(structuredData, params)
  }

  async generateNewBoards(data:any[], params?:any){
// =======
  // async grantDistAdminRoleAndEmail(data:{BrdMident:string, BrdName:string, Fname:string, Lname:string, Email:string}[], params?:any){
  //   const newRecords:any = [];
  //   const created_by_uid = await currentUid(this.app, params);
  //   for (let i=0; i<data.length; i++){
  //     const item = data[i];
  //     const existingDistricts = <Paginated<any>> await this.app
  //     .service('db/read/school-districts')
  //     .find({
  //       query: {
  //         foreign_id: item.BrdMident,
  //       }
  //     });
  //     if (existingDistricts.total === 0){
  //       throw new Errors.GeneralError('NO_DIST'); // don't create another record
  //     }
  //     const existingAuths = <Paginated<any>> await this.app
  //     .service('db/read/auths')
  //     .find({
  //       query: {
  //         email: item.Email,
  //       }
  //     });
  //     if (existingAuths.total === 0){
  //       throw new Errors.GeneralError('NO_AUTH'); // don't create another record
  //     }
  //     const uid = existingAuths.data[0].uid;
  //     const district = existingDistricts.data[0];
  //     newRecords.push(item.BrdMident+'+'+uid);
  //     const DOMAIN = 'eqao-api.vretta.com'; // normalizeDomain(domain || defaultDomain);

  //     let FIRST_NAME:string, LAST_NAME:string;
  //     if (!item.Lname){
  //       const nameSplit = (item.Fname || '').split(' ');
  //       LAST_NAME = nameSplit.pop() || '';
  //       FIRST_NAME = nameSplit.join(' ');
  //     }
  //     else{
  //       FIRST_NAME = item.Fname || '';
  //       LAST_NAME = item.Lname || '';
  //     }
  //     const langCode = district.brd_lang || 'en';

  //     // add roles
  //     const roles = this.getDistRoles(district.group_id);
  //     for (let j=0; j<roles.length; j++){
  //       const role = roles[j];
  //       await this.app
  //         .service('auth/user-role-actions')
  //         .assignUserRoleToGroup({
  //           uid,
  //           role_type: role.role_type,
  //           group_id: role.group_id,
  //           created_by_uid,
  //         });
  //     }

  //     // send email
  //     const emailTemplateParams:IBoardItInviteEmail = {
  //       DOMAIN,
  //       LANG_CODE: langCode,
  //       BOARD_NAME: item.BrdName,
  //       EMAIL: item.Email || '',
  //       EMAIL_ENCODED: encodeURIComponent(item.Email || ''),
  //       INVITATION_CODE: '', // not relevant
  //       FIRST_NAME,
  //       LAST_NAME,
  //       FIRST_NAME_ENCODED: encodeURIComponent(FIRST_NAME || ''),
  //       LAST_NAME_ENCODED: encodeURIComponent(LAST_NAME || ''),
  //     }
  //     const translation = this.app.service('public/translation');
  //     const mailCore = this.app.service('mail/core');
  //     const whitelabel = DOMAIN || defaultDomain;
  //     await mailCore.sendEmail({
  //       whitelabel,
  //       emailAddress: item.Email,
  //       subject: await translation.getOneBySlug('subj_email_acct_invite_board_it', langCode),
  //       emailTemplate: await translation.getOneBySlug('email_acct_invite_board_it_returning', langCode),
  //       parameterMapping: emailTemplateParams
  //     });
  //     logger.silly('email: %s', item.Email)
  //   }
  // }

  // async inviteNewBoardAdmins(data:{BrdMident:string, BrdName:string, Fname:string, Lname:string, Email:string}[], params?:any){
// >>>>>>> origin/release/eqao-main
    const created_by_uid = await currentUid(this.app, params);

    const newRecords:any = [];
    return Promise.all(
      data.map(async (item,idx) => {

        const {
          isAutoEmail,
          role_type,
          domain,
        } = item.user;

        if (!item.schoolDistrict.print_order){
          item.schoolDistrict.print_order = 1;
        }
        item.schoolDistrict.brd_lang = item.user.langCode || 'en'
        // const langCode = item.user.langCode || 'en';
        const newDistrict = await this.createNewDistrict(item.schoolDistrict);
        if (!newDistrict){
          return;
        }

        const existingDistricts = <Paginated<any>> await this.app
          .service('db/read/school-districts')
          .find({
            query: {
              foreign_id: item.BrdMident,
            }
          });

        if (existingDistricts.total === 0){
          throw new Errors.GeneralError('NO_DIST'); // don't create another record
        }

        const district = existingDistricts.data[0];

        newRecords.push(item.BrdMident+'+'+item.Email);

        await this.createDistAdminAccount(
          district.group_id,
          created_by_uid,
          district.brd_lang || 'en',
          null,
          {
            first_name: item.Fname,
            last_name: item.Lname,
            email: item.Email
          },
          item.BrdName,
        )

      }
    )).then(() => {
      return <any> {newRecords}
    });
  }

  async createNewDistrict(schoolDistrictDef:INewDistrictAdmin, isExpectDuplicates:boolean=false){
    const existingDistricts = <Paginated<any>> await this.app
      .service('db/read/school-districts')
      .find({
        query: {
          foreign_id: schoolDistrictDef.foreign_id
        }
      });
    if (existingDistricts.total > 0){
      if (isExpectDuplicates){
        return existingDistricts.data[0];
      }
      return; // don't create another record
    }

    // get groupid of the mpt_test_controller group
    const boardGroup = await this.app.service('db/write/u-groups').create({
      group_type: 'school_district',
      description: 'School Board'
    });

    const district = await this.app.service('db/write/school-districts').create({
      ... schoolDistrictDef,
      group_id: boardGroup.id
    });

    return district;
  }

  async createRangeFinderAccounts(){
    const offsetDays = 5;
    const users:any[] = [
      // {first_name: 'Charles', last_name:'Anifowose', email:'<EMAIL>', langCode:'fr', group_id:41449},
    ];
    for (let i=0; i<users.length; i++){
      const user = users[i];
      const roles = [
        {
          role_type: DBD_U_ROLE_TYPES.mrkg_rafi,
          group_id: 1,
        },
        {
          role_type: DBD_U_ROLE_TYPES.mrkg_rafi,
          group_id: user.group_id,
        },
      ];
      const DOMAIN = normalizeDomain('eqao-api.vretta.com' || defaultDomain);
      const sanitizeName = (str:string) => str.split(' ').join('_').split('\t').join('_');
      const emailTemplateParams:any = {
        DOMAIN,
        LANG_CODE: user.langCode || 'en',
        EMAIL: user.email || '',
        EMAIL_ENCODED: encodeURIComponent(user.email || ''),
        INVITATION_CODE: '', // filled in later
        FIRST_NAME: sanitizeName(user.first_name),
        LAST_NAME: sanitizeName(user.last_name),
      }
      const invite = await this.app
      .service('auth/invitation')
      .create({
        account_type: AccountType.SCOR_RAFI,
        roles,
        offsetDays,
        created_by_uid: 21,
        first_name: user.first_name,
        last_name: user.last_name,
        contact_email: user.email,
        isAutoEmail:true,
        langCode: emailTemplateParams.LANG_CODE,
        domain: DOMAIN,
        emailSubjectSlug: 'subj_email_acct_invite_rafi',
        emailTemplateSlug: 'email_acct_invite_rafi',
        emailTemplateParams,
      });
    }
    return {success: 1}
  }

// <<<<<<< HEAD
  async createDistAdminUserRoles(uid:number, sd_group_id:number, created_by_uid:number, role_type = 'schl_dist_it_admin' ){
    const roles = this.setupDistAdminUserRoles(sd_group_id, role_type);
    for (let i=0; i<roles.length; i++){
      const role = roles[i];
      const {role_type, group_id} = role;
      await this.app
        .service('auth/user-role-actions')
        .assignUserRoleToGroup({
          uid,
          role_type,
          group_id,
          created_by_uid
        })
    }
  };

  setupDistAdminUserRoles(group_id:number, role_type = 'schl_dist_it_admin'){
    let roles:IRoleDef[] = [];
    switch(role_type){
      case 'schl_disct_curr_ele':
        roles = [
          { role_type: DBD_U_ROLE_TYPES.schl_disct_curr_ele, group_id },
        ];
        break;
      case 'schl_disct_curr_sec':
        roles = [
          { role_type: DBD_U_ROLE_TYPES.schl_disct_curr_sec, group_id },
        ];
        break;
      case 'school_district_curr':
        roles = [
          { role_type: DBD_U_ROLE_TYPES.school_district_curri, group_id },
        ];
        break;
      case 'schl_dist_it_admin':
      default:
        roles = [
          { role_type: DBD_U_ROLE_TYPES.schl_dist_admin, group_id },
          { role_type: DBD_U_ROLE_TYPES.schl_dist_it_admin, group_id },
        ];
        break;
    }
    return roles;
  }

  // async createDistAdminAccount(boardGroupId:number, created_by_uid:number, langCode:string, domain:string|null, user:{first_name?:string, last_name?:string, email?:string, password?:string}, BOARD_NAME:string, isAutoEmail=false, offsetDays=7){
  //   const roles = this.setupDistAdminUserRoles(boardGroupId)
  async createDistAdminAccount(boardGroupId:number, created_by_uid:number, langCode:string, domain:string|null, user:{first_name?:string, last_name?:string, email?:string, password?:string}, BOARD_NAME:string, isAutoEmail=false, offsetDays=7, role_type = 'schl_dist_it_admin'){
    const roles = this.setupDistAdminUserRoles(boardGroupId, role_type)

    const DOMAIN = 'eqao-api.vretta.com'; // normalizeDomain(domain || defaultDomain);

    let FIRST_NAME:string, LAST_NAME:string;
    if (!user.last_name){
      const nameSplit = (user.first_name || '').split(' ');
      LAST_NAME = nameSplit.pop() || '';
      FIRST_NAME = nameSplit.join(' ');
    }
    else{
      FIRST_NAME = user.first_name || '';
      LAST_NAME = user.last_name || '';
    }

    const emailTemplateParams:IBoardItInviteEmail = {
      // DOMAIN, // todo: implement domain feed
      LANG_CODE: langCode || 'en',
      BOARD_NAME,
      EMAIL: user.email || '',
      EMAIL_ENCODED: encodeURIComponent(user.email || ''),
      INVITATION_CODE: '', // filled in later
      FIRST_NAME,
      LAST_NAME,
      FIRST_NAME_ENCODED: encodeURIComponent(FIRST_NAME || ''),
      LAST_NAME_ENCODED: encodeURIComponent(LAST_NAME || ''),
    }

    let account_type = AccountType.DIST_ADMIN;
    let emailSubjectSlug = 'subj_email_acct_invite_sd_admin'
    let emailTemplateSlug = 'email_acct_invite_sd_admin'

    const invite = await this.app
      .service('auth/invitation')
      .create({
        account_type,
        roles,
        created_by_uid,
        first_name: user.first_name,
        last_name: user.last_name,
        contact_email: user.email,
        isAutoEmail,
        langCode,
        offsetDays,
        domain: DOMAIN,
        emailSubjectSlug,
        emailTemplateSlug,
        emailTemplateParams,
      });
    logger.silly('dist admin invite', invite);
    const {invitationCode} = <any> invite;
    if (user.password && (user.email&&user.first_name&&user.last_name) ){
      const createdUser = await this.app.service('public/auth/test-admin').createAdminFromInvite({
        langCode,
        invitationCode,
        email: user.email,
        password: user.password,
        firstName: user.first_name,
        lastName: user.last_name,
        phoneNumber: ''
      })
      return createdUser;
    }
    return invite;
  }

  async replaceAccounts(data:Data[]){
    const db:Knex = this.app.get('knexClientRead');
    const getData = async (props:any[], query:string) => {
      const res = await db.raw(query, props);
      return <any[]> res[0];
    }

    return Promise.all( data.map( async (contact:any) => {
      const {BrdMident, BrdName, Email, Fname, Lname} = contact;
      const userRecords = await getData([BrdMident], `
        select u.id, sd.name, ur.role_type, u.first_name, u.last_name, u.contact_email
        from school_districts sd
        join user_roles ur on ur.group_id = sd.group_id and ur.role_type = 'schl_dist_admin' and ur.is_revoked != 1
        join users u on ur.uid = u.id
        where sd.foreign_id = ?
      ;`);
      const userRecord = userRecords[0];
      if (userRecord){
        const id = userRecord.id;
        await this.app
          .service('db/write/users')
          .patch(id, {
            first_name: Fname,
            last_name: Lname,
            contact_email: Email,
          });
        return id;
      }
      return null;
    }))
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
