import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import moment from 'moment';
import { INVITATION_SECRET_KEY_LEN } from '../../../../constants/db-constants';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbDateNow, dbDateOffsetDays } from '../../../../util/db-dates';
import { generateSecretCode, renderInvitationCode } from '../../../../util/secret-codes';
import { IUInvites } from '../../../db/schemas/u_invites.schema';
import { merge } from 'lodash';

interface Data { }

interface ServiceOptions { }

interface IDistrictAdminInvite {
  uid: number,
  created_by_uid: number,
  email: string,
  lang: 'en' | 'fr',
  district_group_id: number,
}

export class Invite implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor(options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find(params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get(id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create(data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update(id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch(id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove(id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }

  async createInvite(data: IDistrictAdminInvite): Promise<Data> {
    const { email, uid, created_by_uid, lang, district_group_id } = data;
    if (!email || !uid || !created_by_uid || !lang || !district_group_id) throw new Errors.BadRequest("INVALID_PARAMETERS");

    if (!email.includes("@")) {
      throw new Errors.BadRequest('INVALID_EMAIL')
    }

    const newAdmin = await this.app.service('db/read/users').get(uid);
    if (!newAdmin) throw new Errors.BadRequest("ADMIN_DOES_NOT_EXIST");

    const createdByAdmin = await this.app.service('db/read/users').get(created_by_uid);
    if (!createdByAdmin) throw new Errors.BadRequest("ADMIN_DOES_NOT_EXIST");

    const districtRecord = <any[]>await this.app.service('db/read/school-districts').find({
      query: {
        group_id: district_group_id,
      },
      paginate: false,
    });
    if (districtRecord.length === 0) throw new Errors.BadRequest("DISTRICT_DOES_NOT_EXIST");
    const school = districtRecord[0];

    const domain = 'bced-api.vretta.com';
    const createdByAdminName = createdByAdmin.first_name + ' ' + createdByAdmin.last_name;
    const emailSubjectSlug = 'email_subject_fsa_school_admin_invite'; // todo
    const emailTemplateSlug = 'email_body_fsa_school_admin_invite'; // todo
    const emailTemplateParams = {
      LANG_CODE: lang,
      EMAIL_ENCODED: encodeURIComponent(email || ''),
    };

    const pendingInvites = <any[]>await this.app.service('db/read/u-invites').find({
      query: {
        uid: uid,
        is_revoked: 0,
      },
      paginate: false,
    });
    if (pendingInvites.length > 0) {
      await this.revokeInvites(pendingInvites);
    }

    const offsetDays = 8;
    const invit_meta = JSON.stringify({ emailSubjectSlug, emailTemplateSlug, emailTemplateParams, lang });
    const newInvitation = await this.createInvitation({
      uid,
      created_by_uid,
      isAutoEmail: true,
      contact_email: email,
      invit_meta,
      offsetDays
    })
    const {
      invitationCode,
      id,
      created_on,
      expire_on
    } = <any>newInvitation;

    await this.app
      .service('db/write/u-invites')
      .patch(id, {
        expire_on: dbDateOffsetDays(this.app, offsetDays),
      })

    const EMAIL_ENCODED = encodeURIComponent(email);
    const mailRes = await this
      .sendEmail(
        email,
        emailSubjectSlug,
        {
          YEAR: '2021/22',
        },
        emailTemplateSlug,
        {
          FIRST_NAME: newAdmin.first_name.trim(),
          LAST_NAME: newAdmin.last_name.trim(),
          ESC_FIRST_NAME: encodeURI(newAdmin.first_name.trim()),
          ESC_LAST_NAME: encodeURI(newAdmin.last_name.trim()),
          SCHOOL_NAME: school.name,
          SCHOOL_CODE: (school.foreign_id as number).toString().padStart(8, '0'),
          YEAR: '2021/22',
          START_DAY: '4',
          END_DAY: '12',
          SCORE_ENTRY_END_DAY: '26',
          MANUAL_NAME: '2021/22 FSA Administration Manual',
          // SCHOOL_ADMIN_NAME: createdByAdminName,
          LANG_CODE: lang || 'en',
          EMAIL_ENCODED,
          INVITATION_CODE: invitationCode,
          PHONE_NUMBER: '**************',
          SUPPORT_EMAIL: '<EMAIL>',
          TIMESTAMP: moment().utc().toISOString(),
        },
        invitationCode,
        lang,
        domain
      );
    const invitation = await this.app
      .service('db/write/u-invites')
      .patch(id, {
        is_auto_email: 1,
      })

    return {
      ...invitation,
      firstName: newAdmin.first_name,
      lastName: newAdmin.last_name,
      invit_id: id,
    };
  }

  async revokeInvites(pendingInvites: any) {
    return Promise.all(
      pendingInvites.map(async (invite: any) => {
        await this.app
          .service('db/write/u-invites')
          .patch(invite.id, {
            is_revoked: 1,
            revoked_on: dbDateNow(this.app),
          })
      })
    )
  }

  async createInvitation(config: { uid: number, created_by_uid?: number, isAutoEmail?: boolean, contact_email?: string, invit_meta?: string, offsetDays: number },) {
    const { uid, created_by_uid, isAutoEmail, contact_email, invit_meta, offsetDays } = config;
    const secret_key = generateSecretCode(INVITATION_SECRET_KEY_LEN);
    const invitationCreateFields: Partial<IUInvites> = {
      uid,
      secret_key,
      expire_on: dbDateOffsetDays(this.app, offsetDays),
      is_auto_email: isAutoEmail ? 1 : 0,
      created_by_uid,
      invit_email: contact_email,
      invit_meta,
    }
    const invitationRecord: IUInvites = await this.app
      .service('db/write/u-invites')
      .create(invitationCreateFields);
    const invitationCode = renderInvitationCode(<number>invitationRecord.id, invitationRecord.secret_key);
    const { created_on, expire_on, id } = invitationRecord;
    return {
      id,
      invitationCode,
      secret_key,
      created_on,
      expire_on
    }
  }

  async sendEmail(
    emailAddress: string,
    emailSubjectSlug: string,
    emailSubjectParams: { [index: string]: string | undefined },
    emailTemplateSlug: string,
    emailTemplateParams: { [index: string]: string | undefined },
    invitationCode: string,
    langCode: string,
    domain?: string,
  ) {
    const translation = this.app.service('public/translation');
    const mailCore = this.app.service('mail/core');
    // TODO preexisting whitelabel?
    const whitelabel = domain;
    return mailCore.sendEmail({
      whitelabel,
      emailAddress,
      // subject: "Invitation to access your account for the EQAO E-Assessment",
      // emailTemplate:"Test invitation body",
      subject: {
        template: await translation.getOneBySlug(emailSubjectSlug, langCode),
        parameterMapping: emailSubjectParams,
      },
      emailTemplate: await translation.getOneBySlug(emailTemplateSlug, langCode),
      parameterMapping: merge({}, emailTemplateParams, { INVITATION_CODE: invitationCode })
    });
  }
}
