import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead } from '../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class OssltReport implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    if (params) {
      const { schl_dist_group_id, testWindowId, clientDomain} = (<any>params).query;
      const allowBypassDomain = this.app.service('public/school-admin/reports').allowBypassDomain();
      const isBypassDomain = allowBypassDomain.indexOf(clientDomain) > -1
      return await this.gerReport('schoolBoard', schl_dist_group_id, testWindowId, isBypassDomain);
    }
    throw new Errors.BadRequest();
  }

  async gerReport(entry:string, group_id:string, testWindowId:string, isBypassDomain = false){
    const result = await dbRawRead(this.app, [testWindowId, testWindowId, group_id], 
      ` select distinct
               schl.foreign_id as SchMident
             , schl.name as SchName
             , sc.name as Grouping 
             , um1.value as StudentOEN
             , um2.value as SASN
             , us.first_name as FirstName
             , us.last_name as LastName
             , ta.started_on as HasStarted
             , ta.is_submitted as HasSubmitted
             , ta.closed_on as ta_closed_on 
             , ta.started_on as StartedOn
             , sr.id as HasReport
             , sr.overall as overall_result
             , sr.scale_score as OSSLTScaleScore
             , sr.is_data_insufficient
             , um3.value as NonParticipationStatus
             , um4.value as EligibilityStatus
             , um5.value as isLinear
             , sr.is_absent
             , sr.is_pending
             , sr.is_withheld
             , ur.is_revoked as ur_is_revoked
             , sc.is_active as sc_is_active
             , ts.is_cancelled as ts_is_cancelled
             , null as Note
          from schools schl
          join school_classes sc on sc.schl_group_id = schl.group_id and sc.group_type = 'EQAO_G10'
          join school_semesters ss on ss.id = sc.semester_id
          join test_windows tw on tw.id = ss.test_window_id ${isBypassDomain?``:`and tw.show_report_to_Board = 1`} and tw.id = ?
          join user_roles ur on ur.group_id = sc.group_id
          join user_metas um1 on um1.uid = ur.uid and um1.key_namespace = 'eqao_sdc' and um1.key = 'StudentOEN'
     left join user_metas um2 on um2.uid = ur.uid and um2.key_namespace = 'eqao_sdc' and um2.key = 'SASN'
     left join user_metas um3 on um3.uid = ur.uid and um3.key_namespace = 'eqao_sdc_g10' and um3.key = 'NonParticipationStatus'
     left join user_metas um4 on um4.uid = ur.uid and um4.key_namespace = 'eqao_sdc_g10' and um4.key = 'EligibilityStatus'
     left join user_metas um5 on um5.uid = ur.uid and um5.key_namespace = 'eqao_sdc_g10' and um5.key = 'Linear'
          join users us on us.id = ur.uid
     left join school_class_test_sessions scts on scts.school_class_id = sc.id and scts.slug = 'OSSLT_OPERATIONAL'
     left join test_sessions ts on ts.id = scts.test_session_id and ts.test_window_id = ?
     left join test_attempts ta on ta.test_session_id = ts.id and ta.uid = ur.uid and ta.twtdar_order = 0
     left join student_reports sr 
      on sr.uid = ur.uid 
      and sr.attempt_id = ta.id 
      and sr.is_revoked = 0
      and sr.is_isr = 1
      and sr.is_reporting = 1
     left join school_student_asmt_info_signoffs ssais on ssais.schl_group_id = schl.group_id and ssais.tw_type_slug = 'EQAO_G10L' and ssais.is_revoked != 1 and ssais.test_window_id = ts.test_window_id
          ${entry === 'schoolBoard'?` where schl.schl_dist_group_id = ?`:``}
          ${entry === 'schoolAdmin'?` where schl.group_id = ?`:``}
          and  ssais.id is not null
    ;`);

    result.forEach (report => {
      let resultOrder = [];
      resultOrder[0] =  {target:'is_withheld',            targetValue:'1', returnValue:'10'}
      resultOrder[1] =  {target:'is_pending',             targetValue:'1', returnValue:'0'}
      resultOrder[2] =  {target:'is_data_insufficient',   targetValue:'1', returnValue:'11'}
      resultOrder[3] =  {target:'overall_result',         targetValue:'1', returnValue:'1'}
      resultOrder[4] =  {target:'is_absent',              targetValue:'1', returnValue:'3'}
      resultOrder[5] =  {target:'overall_result',         targetValue:'0', returnValue:'2'}
      resultOrder[6] =  {target:'NonParticipationStatus', targetValue:'3', returnValue:'4'}
      resultOrder[7] =  {target:'NonParticipationStatus', targetValue:'2', returnValue:'5'}
      resultOrder[8] =  {target:'NonParticipationStatus', targetValue:'1', returnValue:'6'}
      resultOrder[9] =  {target:'HasSubmitted',           targetValue:'1', returnValue:'0'} // has submit but no report

      for (let index =0; index<resultOrder.length;index++){
        const target = resultOrder[index].target 
        const targetValue = resultOrder[index].targetValue
        const stateValue =  resultOrder[index].returnValue
        if(report[target] && +report[target] === +targetValue){
          report.Result = stateValue;
          return
        }
      } 
      report.Result = '3' // hardcoded default value set to absent. Basically we shoult run to this line
    })

    //round 1: put those has report and not revoked student in the returnResult
    let returnResult :any[]= [];
    result.forEach( (r:any) => { 
      if(r.HasReport !== null && +r.ur_is_revoked != 1 && +r.sc_is_active != 0 && +r.ts_is_cancelled != 1){
        returnResult.push(r)
      }
    })

    //round 2: put those has repot and revoked student in the returnResult if they are not in there
    result.forEach( (r:any) => { 
      if(r.HasReport !== null && (+r.ur_is_revoked == 1 || +r.sc_is_active == 0 || +r.ts_is_cancelled == 1)){
        const rr = returnResult.find( rr => rr.StudentOEN == r.StudentOEN)
        if(!rr){
          returnResult.push(r)
        }  
      }
    })

    //round 3: put those has no repot and not revoked student and has attempt started
    result.forEach( (r:any) => { 
      if(r.HasReport == null && (+r.ur_is_revoked != 1 && +r.sc_is_active != 0 && +r.ts_is_cancelled != 1) && r.HasSubmitted == 1){
        const rr = returnResult.find( rr => rr.StudentOEN == r.StudentOEN)
        if(!rr){
          returnResult.push(r)
        }  
      }
    })

    //round 4: put those has no repot and not revoked student in the returnResult if they are not in there
    result.forEach( (r:any) => { 
      if(r.HasReport == null && (+r.ur_is_revoked != 1 && +r.sc_is_active != 0 && +r.ts_is_cancelled != 1)){
        const rr = returnResult.find( rr => rr.StudentOEN == r.StudentOEN)
        if(!rr){
          returnResult.push(r)
        }  
      }
    })

    //sort by StudentOEN
    returnResult.sort((a:any,b:any) => +a.StudentOEN - +b.StudentOEN);
    return returnResult;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.BadRequest();
  }
}
