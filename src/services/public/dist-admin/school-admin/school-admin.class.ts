import { Id, NullableId, <PERSON><PERSON>ated, Para<PERSON>, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { DBD_U_ROLE_TYPES } from '../../../../constants/db-extracts';
import { currentUid } from '../../../../util/uid';
import { AccountType } from '../../../../types/account-types';
import { defaultDomain } from '../../../../constants/mail-constants';
import dataFileHooks from '../../test-cert/data-file/data-file.hooks';
import { Errors } from '../../../../errors/general';
import { Knex } from 'knex';
import { dbDateOffsetDays } from '../../../../util/db-dates';
import { renderInvitationCode } from '../../../../util/secret-codes';
import userInfoCoreHooks from '../../auth/user-info-core/user-info-core.hooks';
import { dbRawRead } from '../../../../util/db-raw';
import { randInt } from '../../../../util/random';
import {checkDomain} from '../../../../util/domain-whitelist';
import logger from '../../../../logger';
import { FLAGS, isABED } from '../../../../util/whiteLabelParser';

interface Data {}
interface IInvitationRecord {
  s_id:number,
  s_group_id:number,
  sd_group_id:number,
  sd_foreign_id:string,
  s_foreign_id:string,
  s_name:string,
  lang:string,
  role_type:string,
  uid:string,
  first_name:string,
  last_name:string,
  contact_email:string,
  invite_id:string,
  secret_key:string,
  expire_on:string,
}
interface INewSchoolAdmin {
  schl_group_id:number,
  first_name:string,
  last_name:string,
  lang:string,
}

// based on 151_cand_sch_list_address
interface ISchoolAdminInfo {
  BrdMident?: string,
  SchName	?:string,
  SchMident:string,
  PrincipalFName:string,
  PrincipalLName:string,
  PrincipalEmail:string,
}


interface ServiceOptions {}

export class SchoolAdmin implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create (data: any, params?: Params): Promise<Data> {
    // return this.sendSchoolAdminEmailByProjectId(data.projectId, data.createdByUid);
    // return this.convertUserIdentityByUidAndSendInvite(data);
    // return this.sendSchoolAdminReminderEmails(data);
    // return <any> this.ensureInvitations(<any> data);
    // this.sendSchoolAdminEmailByBoardForeignId(['28002','28029','29068','28070','29106','29009'])
    return [];
    // return this.sendSchoolAdminEmailByUids(data);
    // account_type: AccountType.SCHOOL_ADMIN,
  }

  async sendSampleEmails(data:any){
    const schoolAdminRecord = {
      lang: 'en',
      contact_email: data.email,
      invite_id: '000',
      secret_key: '****************',
      s_name: 'Sample School',
      first_name: 'Danielle',
      last_name: 'Eagleson',
      s_admin_name: 'Anand Karat'
    }
    return [
      await this.sendTeacherEmail(schoolAdminRecord),
      await this.sendTeacherEmail({... schoolAdminRecord, lang:'fr'}),
    ];
  }

  async convertUserIdentityByUidAndSendInvite(data:Array<{email:string,first_name:string,last_name:string,uid:string}>){
    const res = [];
    const db:Knex = this.app.get('knexClientRead');
    const getData = async (props:any[], query:string) => {
      const res = await db.raw(query, props);
      return <any[]> res[0];
    }

    for (let i=0; i<data.length; i++){
      const info = data[i];
      await this.app.service('db/read/users').patch(info.uid, {
        first_name: info.first_name,
        last_name: info.last_name,
        contact_email: info.email,
      });
      const records = await getData([info.uid], `
        select s.name as s_name
             , s.lang as lang
             , u.first_name
             , u.last_name
             , u.contact_email
             , i.id as invite_id
             , i.secret_key
        from schools s
        left join user_roles ur
              on ur.group_id = s.group_id
              and ur.role_type = 'schl_admin'
              and ur.is_revoked != 1
        left join users u on ur.uid = u.id
        left join u_invites i on i.uid = ur.uid
        where ur.uid = ?
          and i.is_revoked != 1
      ;`);
      const outbox = await this.sendSchoolAdminEmailByUid( +info.uid,{
        ... records[0],
        first_name: info.first_name,
        last_name: info.last_name,
        contact_email: info.email,
      })
      res.push(outbox)
    }
    return res;
  }

  async sendSchoolAdminEmailByBoardForeignId(boardForeignIds:string[], created_by_uid:number=0){
    const records = await dbRawRead(this.app, [boardForeignIds], `
        select s.name as s_name
             , s.lang as lang
             , sd.name as sd_name
             , sd.foreign_id as brd_mident
             , s.foreign_id as sch_mident
             , u.first_name
             , u.last_name
             , u.contact_email
             , u.is_claimed
             , i.used_on
             , i.id as invite_id
             , i.secret_key
        from schools s
        join school_districts sd
          on s.schl_dist_group_id = sd.group_id
          and sd.foreign_id IN (?)
        join user_roles ur
              on ur.group_id = s.group_id
              and ur.role_type = 'schl_admin'
              and ur.is_revoked != 1
        join users u on ur.uid = u.id
        left join u_invites i on i.uid = ur.uid
         and i.is_revoked != 1
        group by u.id
      ;`);
    // const unusedRecords = records.filter(r => r.used_on == null);
    // const recordsToBeMailed = unusedRecords.filter(r => r.contact_email);
    const recordsToBeMailed = records.filter(r => r.contact_email);;
    return await Promise.all(recordsToBeMailed.map(async record => {
      if (record.is_claimed == 1){
        return this.sendSchoolAdminReminderEmail(record);
      }
      else{
        return this.sendSchoolAdminEmail(created_by_uid, {
          ... record
        });
      }
    }));
  }

  async sendSchoolAdminEmailByProjectId(projectId:number, created_by_uid:number=0){
    const records = await dbRawRead(this.app, [projectId], `
        select s.name as s_name
             , s.lang as lang
             , sd.name as sd_name
             , sd.foreign_id as brd_mident
             , s.foreign_id as sch_mident
             , u.first_name
             , u.last_name
             , u.contact_email
             , i.used_on
             , i.id as invite_id
             , i.secret_key
        from schools s
        join school_districts sd
          on s.schl_dist_group_id = sd.group_id
        join school_classes sc
          on sc.schl_group_id = s.group_id
        join school_semesters ss
          on ss.id = sc.semester_id
          and ss.foreign_scope_id = ?
        left join user_roles ur
              on ur.group_id = s.group_id
              and ur.role_type = 'schl_admin'
              and ur.is_revoked != 1
        left join users u on ur.uid = u.id
        left join u_invites i on i.uid = ur.uid
         and i.is_revoked != 1
        where u.is_claimed != 1
          and u.contact_email not in ('<EMAIL>')
        group by u.id
      ;`);
    const unusedRecords = records.filter(r => r.used_on == null);
    const recordsToBeMailed = unusedRecords.filter(r => r.contact_email);
    return await Promise.all(recordsToBeMailed.map(async record => {
      return this.sendSchoolAdminEmail(created_by_uid, {
        ... record
      });
    }));
  }

  async sendSchoolAdminEmailByUids(uids:number[], prefill?:any, created_by_uid:number=0){
    const records = await dbRawRead(this.app, [uids], `
        select s.name as s_name
             , s.lang as lang
             , u.first_name
             , u.last_name
             , u.contact_email
             , i.id as invite_id
             , i.secret_key
        from schools s
        left join user_roles ur
              on ur.group_id = s.group_id
              and ur.role_type = 'schl_admin'
              and ur.is_revoked != 1
        left join users u on ur.uid = u.id
        left join u_invites i on i.uid = ur.uid
        where ur.uid IN (?)
        and i.is_revoked != 1
      ;`);
    return Promise.all(records.map(async record => {
      return this.sendSchoolAdminEmail(created_by_uid, {
        ... (prefill || {}),
        ... record
      });
    }))
  }

  async sendSchoolAdminEmailByUid(uid:number, prefill?:any, created_by_uid:number=0){
    return this.sendSchoolAdminEmailByUids([uid], prefill, created_by_uid)
  }

  async sendSchoolAdminReminderEmails(allowedEmailList:string[]){
    const records = await dbRawRead(this.app, [allowedEmailList], `
      select s.foreign_id, s.name as s_name, u.is_claimed, u.contact_email, s.lang, u.first_name, u.last_name
      from schools s
      join user_roles ur on ur.group_id = s.group_id and ur.role_type like 'schl_admin'
      join users u on u.id = ur.uid
      join auths a on a.uid = ur.uid and a.email IN (?)
    `)
    const invites = [];
    for (let i=0; i<records.length; i++){
      const record = records[i];
      const invite = await this.sendSchoolAdminReminderEmail(record);
      invites.push(invite);
    }
    return invites;
  }

  async sendSchoolAdminReminderEmail(record:any){
    const langCode = record.lang;
    const domain = 'api-eassessment.vretta.com'; // server

    // const domain = 'eqao-api.vretta.com'; // there is some confusion around domain and whitelabel at the moment...
    let contact_email = record.contact_email;
    const invitationCode = renderInvitationCode(<number>record.invite_id, record.secret_key);
    const EMAIL_ENCODED = encodeURIComponent(contact_email);
    return this.app
      .service('auth/invitation')
      .sendEmail(
        contact_email,
        'subj_email_acct_invite_school_admin_nbed',
        'email_acct_invite_school_admin_reminder',
        {
          FIRST_NAME: record.first_name,
          LAST_NAME: record.last_name,
          SCHOOL_NAME: record.s_name,
          LANG_CODE: langCode,
          INVITATION_CODE: invitationCode
        },
        invitationCode,
        langCode,
        domain
      );
  }

  async sendSchoolAdminReinviteEmail(config:{record:{s_group_id:number, sd_group_id:number, lang:string, s_name:string}, account:{PrincipalFName:string, PrincipalLName:string, PrincipalEmail:string, password?:string}, offsetDays:number, created_by_uid:number, emailLinkDomain?:string, isAutoEmail?:boolean}){
    // const domain = 'eqao-api.vretta.com'; // server
    const domain = 'api-eassessment.vretta.com'; // server

    const {record, account, offsetDays, created_by_uid, emailLinkDomain, isAutoEmail} = config;
    const DOMAIN = checkDomain(emailLinkDomain, this.app.get('isDevMode'));
    account.PrincipalEmail = account.PrincipalEmail.trim();
    const langCode = ((''+record.lang).toLowerCase() === 'fr') ? 'fr' : 'en';
    const templateParams = {
      DOMAIN,
      FULL_NAME: account.PrincipalFName,
      SCHOOL_NAME: record.s_name,
      LANG_CODE: record.lang,
      EMAIL: account.PrincipalEmail,
      FIRST_NAME: encodeURIComponent(account.PrincipalFName),
      LAST_NAME: encodeURIComponent(account.PrincipalLName),
      EMAIL_ENCODED: encodeURIComponent(account.PrincipalEmail || ''),
    }
    const mailer = await this.app
      .service('auth/invitation')
      .sendEmail(
        account.PrincipalEmail,
        'subj_email_acct_invite_school_admin_nbed',
        'email_acct_invite_school_admin_reminder',
        templateParams,
         '',
        langCode,
        domain
      );
    return mailer;
  }

  async sendTeacherEmail(record:any){
    const langCode = record.lang;   
    const domain = 'api-eassessment.vretta.com'; // server
    // const domain = 'eqao-api.vretta.com'; // there is some confusion around domain and whitelabel at the moment...
    let contact_email = record.contact_email;
    const invitationCode = renderInvitationCode(<number>record.invite_id, record.secret_key);
    const EMAIL_ENCODED = encodeURIComponent(contact_email);
    return await this.app
      .service('auth/invitation')
      .sendEmail(
        contact_email,
        'subj_email_acct_invite_teacher',
        'email_acct_invite_teacher',
        {
          FIRST_NAME: record.first_name,
          LAST_NAME: record.last_name,
          SCHOOL_NAME: record.s_name,
          SCHOOL_ADMIN_NAME: record.s_admin_name,
          LANG_CODE: langCode,
          EMAIL_ENCODED,
          INVITATION_CODE: invitationCode
        },
        invitationCode,
        langCode,
        domain
      );
  }

  async sendSchoolAdminEmail(created_by_uid:number, record:{uid:number, lang:string, contact_email:string, invite_id:number, secret_key:string, s_name:string, first_name:string, last_name:string}){
    const langCode = record.lang;
    const domain = 'api-eassessment.vretta.com'; // server
    // const domain = 'eqao-api.vretta.com'; // there is some confusion around domain and whitelabel at the moment...
    let contact_email = record.contact_email;
    let invitationCode = '';
    const EMAIL_ENCODED = encodeURIComponent(contact_email);
    const emailSubjectSlug = 'subj_email_acct_invite_school_admin_nbed';
    const emailBodyTemplateSlug = 'email_acct_invite_school_admin_nbed';
    const offsetDays = 7;
    if (record.invite_id){
      await this.app
        .service('db/write/u-invites')
        .patch(record.invite_id, {
          is_auto_email: 1,
          expire_on: dbDateOffsetDays(this.app, offsetDays),
        });
      invitationCode = renderInvitationCode(<number>record.invite_id, record.secret_key);
    }
    else{
      const invitSummary = await this.app
        .service('auth/invitation')
        .createInvitation({
          uid: record.uid,
          created_by_uid,
          isAutoEmail: false,
          contact_email,
          invit_meta: '',
          offsetDays,
        });
      invitationCode = invitSummary.invitationCode;
    }
    const templateParams = {
      FIRST_NAME: record.first_name,
      LAST_NAME: record.last_name,
      SCHOOL_NAME: record.s_name,
      LANG_CODE: langCode,
      EMAIL_ENCODED,
      INVITATION_CODE: invitationCode
    }
    if (contact_email){
      const mailer = await this.app
        .service('auth/invitation')
        .sendEmail(
          contact_email,
          emailSubjectSlug,
          emailBodyTemplateSlug,
          templateParams,
          invitationCode,
          langCode,
          domain
        );
      return {
        record,
        mailer,
      }
    }
  }

  async getInvitationRecords() : Promise<IInvitationRecord[]> {
    return dbRawRead(this.app, [], `
        select s.id as s_id, s.group_id as s_group_id, sd.group_id as sd_group_id, sd.foreign_id as sd_foreign_id,  s.foreign_id as s_foreign_id, s.name as s_name, sd.brd_lang as lang, ur.role_type, ur.uid, u.first_name, u.last_name, u.contact_email, i.id as invite_id, i.secret_key , i.expire_on
        from schools s
        join school_districts sd
          on sd.group_id = s.schl_dist_group_id
        and sd.is_active = 1
        left join user_roles ur
              on ur.group_id = s.group_id
              and ur.role_type = 'schl_admin'
              and ur.is_revoked != 1
        left join users u on ur.uid = u.id
        left join u_invites i on i.uid = ur.uid
        where i.is_revoked != 1
            and i.is_auto_email != 1
    ;`);
  }

  async ensureInvitations(accounts:ISchoolAdminInfo[], created_by_uid=0){

    const offsetDays = 8;
    const records = await this.getInvitationRecords();
    const invitationsBySchoolId:Map<string, IInvitationRecord> = new Map();

    records.forEach(record => {
      invitationsBySchoolId.set(''+record.s_foreign_id, record);
    })

    const invites = [];

    for (let i=0; i<accounts.length; i++){
      logger.silly('ensuring invitation %d', i);
      const account = accounts[i];
      const record = invitationsBySchoolId.get(''+account.SchMident);
      if (record){
        invites.push(account);
      }
      else {
        const schoolRecords = await dbRawRead(this.app, [account.SchMident], `
          select group_id as s_group_id
               , schl_dist_group_id as sd_group_id
               , name as s_name
               , lang
          from schools
          where foreign_id = ?
        ;`)
        const record = schoolRecords[0];
        if (!record){
          console.warn('Cannot find school')
        }
        else{
          const invite = this.app.service('public/dist-admin/school-admin').createSchoolAdminInvite({record, account, offsetDays, created_by_uid})
          invites.push(invite);
        }
      }
    }
    logger.silly('created invites', { invites });
    return invites;
  }



  async createSchoolAdminUserRoles(uid:number, s_group_id:number, sd_group_id:number, created_by_uid:number){
    const roles = await this.setupSchoolAdminUserRoles(s_group_id, sd_group_id);
    for (let i=0; i<roles.length; i++){
      const role = roles[i];
      const {role_type, group_id} = role;
      await this.app
        .service('auth/user-role-actions')
        .assignUserRoleToGroup({
          uid,
          role_type,
          group_id,
          created_by_uid
        })
    }
  };

  async setupSchoolAdminUserRoles(s_group_id:number, sd_group_id:number){
    const role_type = DBD_U_ROLE_TYPES.schl_admin;
    const roles = [
      { role_type, group_id: s_group_id, },
      { role_type, group_id: sd_group_id, },
    ];
    return roles;
  }



  async createSchoolAdminInvite(config:{record:{s_group_id:number, sd_group_id:number, lang:string, s_name:string}, 
  account:{PrincipalFName:string, PrincipalLName:string, PrincipalEmail:string, password?:string}, 
  offsetDays:number, created_by_uid:number, emailLinkDomain?:string, domain?: string, isAutoEmail?:boolean})
  {
    // #TODO:  create a db constant to serve server ?
    // const domain = 'eqao-api.vretta.com'; // server
    //const DOMAIN = 'eqao.vretta.com'; // client
    //emailSubjectSlug: 'subj_email_acct_invite_school_admin_bc',
    //emailTemplateSlug: 'email_acct_invite_school_admin_bc',

    const domain: string = isABED(config.domain as FLAGS) ? config.domain! : 'api-eassessment.vretta.com' as string; // server
    const subject = isABED(config.domain as FLAGS) ? 'abed_schooladmin_invite_subject'! : 'subj_email_acct_invite_school_admin_nbed';
    const content = isABED(config.domain as FLAGS) ? 'abed_schooladmin_invite_email'! : 'email_acct_invite_school_admin_nbed';

    const {record, account, offsetDays, created_by_uid, emailLinkDomain, isAutoEmail} = config;
    const DOMAIN = checkDomain(emailLinkDomain, this.app.get('isDevMode'));
    const role_type = DBD_U_ROLE_TYPES.schl_admin;
    const roles = [
      { role_type, group_id: record.s_group_id, },
      { role_type, group_id: record.sd_group_id, },
    ];
    account.PrincipalEmail = account.PrincipalEmail.trim();
    let account_type = AccountType.SCHOOL_ADMIN;
    const invite = await this.app
      .service('auth/invitation')
      .create({
        account_type,
        roles,
        created_by_uid,
        first_name: account.PrincipalFName,
        last_name: account.PrincipalLName,
        contact_email: account.PrincipalEmail,
        isAutoEmail,
        langCode: record.lang.toLocaleLowerCase(),
        offsetDays,
        domain,
        emailSubjectSlug: subject,
        emailTemplateSlug: content,
        emailTemplateParams: 
        {
          DOMAIN,
          FULL_NAME: account.PrincipalFName,
          SCHOOL_NAME: record.s_name,
          LANG_CODE: record.lang,
          EMAIL: account.PrincipalEmail,
          FIRST_NAME: encodeURIComponent(account.PrincipalFName),
          LAST_NAME: encodeURIComponent(account.PrincipalLName),
          EMAIL_ENCODED: encodeURIComponent(account.PrincipalEmail || ''),
        }
      });

    const {invitationCode} = <any> invite;

    if (account.password){
      const createdUser = await this.app.service('public/auth/test-admin').createAdminFromInvite(
      {
        langCode: record.lang.toLocaleLowerCase(),
        invitationCode,
        email: account.PrincipalEmail,
        password: account.password,
        firstName: account.PrincipalFName,
        lastName: account.PrincipalLName,
        phoneNumber: ''
      })
      return createdUser;
    }
    return invite;
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
    return {}
    // return this.createSchoolAdminInvite({
    //   record:{s_group_id:9189, sd_group_id:6841, lang:'en', s_name:'Secondaire de Penticton (9393004)'},
    //   account:{PrincipalFName:'Sample', PrincipalLName:'Admin', PrincipalEmail:'charles.anifowose+bced-sa+'+randInt(1,9999)+'@vretta.com'},
    //   offsetDays:7,
    //   created_by_uid:21
    // })
  }
}
