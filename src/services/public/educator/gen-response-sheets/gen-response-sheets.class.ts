import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { AxiosInstance } from 'axios';
import axios from 'axios';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead, dbRawReadReporting, dbRawReadSingle } from '../../../../util/db-raw';
import QRCode from 'qrcode'
import PDFDocument from 'pdfkit';
import { currentUid } from '../../../../util/uid';
import { IAuthPreviewParams, ITwttInfo, ICustomAsset } from './model/types';
import { SQL_GET_TWTT_INFO, SQL_GET_TWTT_INFO_BACKUP } from './model/sql';
import { FLAGS, isABED } from '../../../../util/whiteLabelParser';
import { SQL_QUESTION_REQUIRING_SCAN } from '../session/model/sql';
import { IQuestionScan } from '../session/model/types';
import { STRMAXLEN_CLASSNAME, STRMAXLEN_STUDENT_NAME, STRMAXLEN_TASK_CAPTION } from './model/constants';

/* DEPRECATED; see config.json `scanningServiceConfig` */
// export const EQAO_SCANNING_API = 'http://localhost:5000';
// export const EQAO_SCANNING_API = 'http://eqao-pdf-scanning-1133926475.ca-central-1.elb.amazonaws.com:5000';

interface Data {}

interface ServiceOptions {}


export class GenResponseSheets implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;
  scanningService: AxiosInstance;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
    this.scanningService = app.get('scanningService');
  }

  async generatePdf(studentData: any, isIndividual: boolean, isSasn: boolean) {
    const res = await this.scanningService.post(`/generate`, { studentData, isIndividual, isSasn });
    if (isIndividual) {
      const studentPdfs = res.data.student_sheets;
      return [{ studentPdfs }];
    } else {
      const responsePdf = res.data.base64;
      return [{ responsePdf }];
    }
  }

  public renderStudentInfoQuery(testSessionId: number, asmtSlug: string, isSasn: number) {
    // < 0.1 sec
    return `
        SELECT distinct us.id as uid
        , us.first_name 
        , us.last_name
        , schl.foreign_id as school_mident
        , sc.name as class_name
        , sc.access_code
        , tw.date_start as school_year_start
        , tw.date_end as school_year_end
        , um.value as student_ASN
        , tw.id as test_window_id
        , twtdar.type_slug as test_code
        , twtdar.lang
        , twtt.caption_short as subject_name
        , twtdar.user_metas_filter
        , twtdar.test_design_id as test_design_id
        , ta.test_session_id as test_session_id
      from users us 
      join school_classes sc
      left join school_classes_guest scg
        on scg.invig_sc_group_id = sc.group_id
        and scg.is_revoked != 1
      left join school_classes sc_guest
        on scg.guest_sc_group_id = sc_guest.group_id
        and sc_guest.is_active = 1
      join user_roles ur
        on ur.uid = us.id
        and ur.is_revoked != 1
        and ur.role_type = 'schl_student'
        and ((sc.group_id = ur.group_id) or (sc_guest.group_id = ur.group_id))
      join user_metas um on um.uid = ur.uid
      join school_class_test_sessions scts on sc.id = scts.school_class_id and scts.test_session_id = ${testSessionId}
      join school_semesters ss on ss.id = sc.semester_id 
      join test_attempts ta on ta.uid = us.id and ta.test_session_id = ${testSessionId}
      join test_windows tw on tw.id = ss.test_window_id and tw.is_active = 1
      join test_window_td_alloc_rules twtdar on twtdar.test_window_id = tw.id and twtdar.id = ta.twtdar_id
      join test_window_td_types twtt ON twtdar.type_slug = twtt.type_slug
      join schools schl on schl.group_id = sc.schl_group_id
      where us.id in (?)
      and sc.is_active = 1
      and um.key = 'StudentIdentificationNumber'
      and twtdar.type_slug = '${asmtSlug}'
    ;`
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    // use this function to generate response sheets for a class from invig view
    if(!params?.query) {
      throw new Errors.BadRequest('MISSING_QUERY_PARAMS');
    }

    const {testSessionId, uids, asmtSlug, scanSlugs, isIndividual, isSasn, whitelabelContext} = params.query;

    if (!testSessionId || !uids || !asmtSlug || !isIndividual || !isSasn) {
      throw new Errors.GeneralError('ERR_MISSING_PARAMS');
    }

    let studentUids;
    if (!Array.isArray(uids)) {
      studentUids = Object.values(uids);
    } else {
      studentUids = uids;
    }

    const studentDataUnsorted = await dbRawRead(this.app, [studentUids], this.renderStudentInfoQuery(testSessionId, asmtSlug, parseInt(isSasn)));

    const studentData: any[] = [];
    studentUids.forEach((uid: any) => {
      const student = studentDataUnsorted.find((stu) => +stu.uid === +uid);
      if (student) studentData.push(student);
    })

    const translation = this.app.service('public/translation');

    const getLang = async (asmtSlug:string) => {
      const twttRecord =  await dbRawRead(this.app, [asmtSlug], `
      select twtt.lang
      from test_window_td_types twtt
      where twtt.type_slug = ?
      ;`)
      return twttRecord[0].lang
    }

    const getTranslation = async (questionSlug: string, umFilter: string) => {
      const lang = await getLang(asmtSlug)
      let slug = '';
      if (questionSlug === 'SESSION_A') {
        slug = 'pj_lang_session_A';
      } else if (questionSlug === 'SESSION_B') {
        slug = 'pj_lang_session_B';
      } else if (questionSlug === 'SESSION_C') {
        slug = 'pj_lang_session_C';
      }
      // else if (questionSlug === 'SESSION_D') {
      //   slug = 'pj_lang_session_D';
      // }
      return await translation.getOneBySlug(slug, lang)
    }
    const test_design_id = [...new Set(studentData.map((student) => student.test_design_id))];
    const questionForScan = await dbRawRead(this.app,{test_design_id}, SQL_QUESTION_REQUIRING_SCAN)
    const sectionDict = questionForScan.reduce((acc: { [x: string]: {questionSlug: string, questionTitle: string, customAsset: string}}, question: IQuestionScan) => {
      if(question.resp_sheet_config){
        const parsedConfig = JSON.parse(question.resp_sheet_config);
        acc[question.item_id] = {questionTitle: parsedConfig?.taskNumber, questionSlug: question.batch_alloc_policy_item_slug, customAsset: parsedConfig?.customAsset};
      }
      return acc;
    }, {});
    // let pdf_gen_data: any = [];

    const pdfGenData: any = {};
    for (const student of studentData) {
      const yearStart = new Date(student.school_year_start).getFullYear();
      const yearEnd = new Date(student.school_year_end).getFullYear();
      student.school_year_start = yearStart;
      student.school_year_end = yearEnd;
      // const sessionTitles = ['SESSION_A', 'SESSION_B', 'SESSION_C'];
      const sessionTitles = scanSlugs as string[];

      await Promise.all(sessionTitles.map(async (title, i) => {
        
        const temp_data = {
          ...student,
            order: i,
            // todo: where does STRMAXLEN_TASK_CAPTION go?
            caption: this.limitCharacterCount(sectionDict[title].questionTitle, STRMAXLEN_TASK_CAPTION),
            task_name: sectionDict[title].questionSlug,
            questionId: sessionTitles[i],
            customAsset: sectionDict[title].customAsset,
            full_name: this.limitCharacterCount(`${student['first_name']} ${student['last_name']}`.toUpperCase(), STRMAXLEN_STUDENT_NAME),
            student_login_key : 'ASN', //temp for ABED
            class_name : this.limitCharacterCount(student.class_name.toUpperCase(), STRMAXLEN_CLASSNAME), // not required for display but keeping it for future use
            access_code: student.access_code,
            subject_name: this.limitCharacterCount(student.subject_name, 35), // limit character to 35
            whitelabelContext
        };
        if (!pdfGenData[student.uid]) {
          pdfGenData[student.uid] = [];
        }
        pdfGenData[student.uid].push(temp_data);
      }))
    }

    Object.keys(pdfGenData).forEach(studentId => {
      pdfGenData[studentId].sort((a:any, b:any) => a.order - b.order);
    });

    // const sessionARec = pdf_gen_data.filter((stu: any) => stu.question_slug === 'SESSION_A');
    // const sessionBRec = pdf_gen_data.filter((stu: any) => stu.question_slug === 'SESSION_B');
    // const sessionCRec = pdf_gen_data.filter((stu: any) => stu.question_slug === 'SESSION_C');
    // const sessionDRec = pdf_gen_data.filter((stu: any) => stu.question_slug === 'SESSION_D');

    const genData: any[]= Object.values(pdfGenData).flatMap(studentData => studentData);

    // Generate separate files for single student print, or one file for bulk class print
    if (isIndividual === 'true'){
      const pdfDataList: any[]= [];
      for (const singleSheetData of genData){
        const pdfData = await this.generatePdfResponseSheets([singleSheetData], parseInt(isSasn) === 1)
        pdfDataList.push({
          uid: singleSheetData.uid,
          session_slug: singleSheetData.questionId,
          responsePdf: pdfData.responsePdf
        })
      }
      return pdfDataList
    } else {
      // const pdfData =  await this.generatePdf(gen_data, isIndividual === 'true', parseInt(isSasn) === 1);
      const pdfData = await this.generatePdfResponseSheets(genData, parseInt(isSasn) === 1)
      return [pdfData]
    }

  }
  
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    // use this function to generate preview response sheet
    if(!params?.query) {
      throw new Errors.BadRequest('MISSING_QUERY_PARAMS');
    }
    const uid = await currentUid(this.app, params);
    const {studentName, asn, schoolMident, classCode, customAsset, taskNumber, batch_alloc_policy_item_slug, whitelabelContext, accessCode, lang} = params.query as IAuthPreviewParams;
    let twttInfo: ITwttInfo = await dbRawReadSingle(this.app, {id}, SQL_GET_TWTT_INFO)
    if(!twttInfo) {
      const twttInfos: ITwttInfo[] = await dbRawReadReporting(this.app, {id}, SQL_GET_TWTT_INFO_BACKUP);
      if (twttInfos.length){
        twttInfo = twttInfos[0];
      }
      else {
        throw new Errors.BadRequest('ERROR_GETTING_TWTT_INFO');
      }
    }
    const studentFirstName = studentName.split(' ')[0];
    const studentLastName = studentName.split(' ').slice(1).join(' ');
    const studentInfo =  {
      full_name: this.limitCharacterCount(studentFirstName + ' ' + studentLastName),
      student_ASN: asn,
      subject_name: this.limitCharacterCount(twttInfo.caption_short, 35),
      test_code : twttInfo.type_slug,
      school_mident: schoolMident, // not required for display but keeping it for future use
      class_name: this.limitCharacterCount(classCode), // not required for display but keeping it for future use
      access_code: accessCode,
      caption: taskNumber.substring(0,4), // Limiting to 4 characters.
      customAsset,
      uid,
      task_name: batch_alloc_policy_item_slug,
      whitelabelContext,
      lang
    }

    const pdfData = await this.generatePdfResponseSheets([studentInfo], false);
    return pdfData;
  }
  
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }
  
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }
  
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }
  
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async generatePdfResponseSheets(studentData: any, isSasn: boolean) {

    const translation = this.app.service('public/translation');
    
    // Create a new PDF document
    const doc = new PDFDocument();

    // Create a buffer to store the PDF
    let buffers:any = [];
    doc.on('data', buffers.push.bind(buffers));
    doc.on('end', () => {
        buffers = Buffer.concat(buffers);
    });

    for (let i = 0; i < studentData.length; i++) {
        const student = studentData[i];

        if (i > 0) {
            doc.addPage();
        }

        // Get constant content on response sheet by language
        const ABED_FOOTER_IMG_LINK = await translation.getOneBySlug('abed_gen_resp_sheet_footer_url', student.lang)
        const LBL_STUDENT_NAME = await translation.getOneBySlug('abed_gen_resp_sheet_student_name', student.lang)
        const LBL_ASN = await translation.getOneBySlug('abed_gen_resp_sheet_asn', student.lang)
        const LBL_SECURITY_STATEMENT = await translation.getOneBySlug('abed_gen_resp_sheet_security_statement', student.lang)

        const lbl_responseSheet = await translation.getOneBySlug('abed_gen_resp_sheet_resp_sheet', student.lang)
        const lbl_accessCode = await translation.getOneBySlug('abed_gen_resp_sheet_access_code', student.lang)
        const LBL_WRITTEN_RESPONSE_ANSWER_SHEET = `${lbl_responseSheet} | ${lbl_accessCode}: ${student.access_code.toUpperCase().substr(0, 4)}-${student.access_code.toUpperCase().substr(4, 8)}`

        const lbl_textInsideBox = await translation.getOneBySlug('abed_gen_resp_sheet_text_inside_box', student.lang)
        const LBL_TEXT_INSIDE_BOX = translation.fillProps(lbl_textInsideBox, {question_caption: student.caption});

        // Set some styling options
        const margin = 40;
        const lineHeight = 14;
        const stuInfoboxWidth = 240;
        const stuInfoboxHeight = 30;
        const padding = 5;
        const marginAfterSessionCode = 80; // SessionCaption width + spacing between session caption and student info box


        // Draw the large session letter
        const questionCaption = student.caption;
        const questionCaptionWidth = doc.fontSize(36).font("Helvetica-Bold").widthOfString(questionCaption)
        const questionCaptionoffSetX = questionCaptionWidth > 16 ? 0 : questionCaptionWidth; // push content to the left based on the width of string.

        // doc.fontSize(58).font("Helvetica-Bold").fillColor('black').text(sessionLetter, margin, margin);
        doc.fontSize(36).font("Helvetica-Bold").fillColor('black').text(questionCaption, margin + questionCaptionoffSetX, margin);


        const firstColX = margin + marginAfterSessionCode;
        const secondColX = firstColX + stuInfoboxWidth + 10;
        const firstRowY = margin;
        
        // Draw gray boxes for the background
        doc.rect(firstColX, firstRowY, stuInfoboxWidth, stuInfoboxHeight).fill('#D9D9D9').stroke();
        doc.rect(secondColX, firstRowY, (stuInfoboxWidth / 2) - 20, stuInfoboxHeight).fill('#D9D9D9').stroke();
        // keep it incase we want to add them later on 
        // doc.rect(firstColX, secondRowY, stuInfoboxWidth, stuInfoboxHeight).fill('#D3D3D3').stroke();
        // doc.rect(secondColX, secondRowY, stuInfoboxWidth, stuInfoboxHeight).fill('#D3D3D3').stroke();

        const maskedStuNum = this.maskStudentNumber(student.student_ASN);
        const studentName= student.full_name;
        // Add header inside each box
        doc.fillColor('black').fontSize(8).font("Helvetica")
          .text(LBL_STUDENT_NAME, firstColX + padding, firstRowY + padding, { width: stuInfoboxWidth - 2 * padding })
          .text(LBL_ASN, secondColX + padding, firstRowY + padding, { width: (stuInfoboxWidth / 2) * padding });
          // keep it incase we want to add them later on 
            // .text(`Class Code: ${student.class_name}`,secondColX + padding, secondRowY + padding, { width: stuInfoboxWidth - 2 * padding }) 
            // .text(`School Mident: ${student.school_mident}`, firstColX + padding, secondRowY + padding, { width: stuInfoboxWidth - 2 * padding });
        
        // Add value inside each box
        doc.fillColor('black').fontSize(12).font("Helvetica-Bold")
          .text(studentName, firstColX + padding, firstRowY + padding + 10, { width: stuInfoboxWidth - 2 * padding })
          .text(student.student_ASN, secondColX + padding, firstRowY + padding + 10, { width: (stuInfoboxWidth / 2) * padding });

        
        // Generate QR code
        const qrCodeText = `${student.uid}-${student.task_name}`;
        const qrCodeDataUrl = await QRCode.toDataURL(qrCodeText);

        // Add the QR code image to the PDF
        const qrCodeImageHeight = 85;
        const qrCodeImageWidth = 85;
        const qrCodeX = doc.page.width - margin - qrCodeImageWidth;
        const qrCodeY = margin / 1.5;
        doc.image(qrCodeDataUrl, qrCodeX, qrCodeY, { width: qrCodeImageWidth, height: qrCodeImageHeight });

        // Add session information and prompt
        const sessionInfoWidth = doc.fontSize(14).font('Helvetica-Bold').widthOfString(` ${student.subject_name}`);
        const sessionInfoHeight = doc.fontSize(14).font('Helvetica-Bold').heightOfString(` ${student.subject_name}`, {width: sessionInfoWidth});
        // const sessionInfoX = firstColX + 5;
        const sessionInfoX = firstColX / 2 + qrCodeX / 2 - sessionInfoWidth / 2; // find mid point
        doc.fontSize(14).font('Helvetica-Bold').text(`${student.subject_name}`, sessionInfoX, margin + 3 * lineHeight);
        
        const answerSheetWidth = doc.fontSize(14).font('Helvetica-Bold').widthOfString(LBL_WRITTEN_RESPONSE_ANSWER_SHEET);
        const answerSheetHeight = doc.fontSize(14).font('Helvetica-Bold').heightOfString(LBL_WRITTEN_RESPONSE_ANSWER_SHEET, {width: answerSheetWidth});
        const answerSheetInfoX = firstColX / 2 + qrCodeX / 2 - answerSheetWidth / 2; // find mid point
        doc.fontSize(14).font('Helvetica-Bold').text(LBL_WRITTEN_RESPONSE_ANSWER_SHEET, answerSheetInfoX, margin + 4.5 * lineHeight);

        // security statement
        if(isABED(student.whitelabelContext as FLAGS)){
          const securityStatementX = firstColX + 5;
          // const copyrightImagePath = 'src/services/public/educator/gen-response-sheets/abed-copyright-and-security.png';
          const copyrightTextWidth = (doc.page.width / 2) + 30;
          // const copyRightImageHeight = 80;
          const rectY = margin + 6 * lineHeight;
          // const copyRightTextHeight = doc.heightOfString(SECURITY_STATEMENT, {width: copyrightTextWidth});
          // doc.image(copyrightImagePath, firstColX, rectY, {width: copyrightImageWidth, height:copyRightImageHeight});
          doc.fontSize(10).font('Helvetica').text(LBL_SECURITY_STATEMENT, securityStatementX, rectY, {width: copyrightTextWidth});
          //.font('Helvetica-Bold').text(RESP_SHEET_TEXT, securityStatementX, rectY + copyRightTextHeight - 25, {width: copyrightTextWidth})
        }

        const boxX = margin;
        const boxY = margin + 10 * lineHeight;
        const boxWidth = doc.page.width - margin * 2;
        const boxHeight = 540;
        const numberOfLines = 7;

        const imagePath = 'src/services/public/educator/individual-report/scan-marker.png';
        const imageHeight = 15;
        const imageWidth = 15;
        doc.image(imagePath, boxX, boxY - (imageHeight * 2), { width: imageWidth, height: imageHeight });
        doc.image(imagePath, boxX + boxWidth - imageWidth, boxY - (imageHeight * 2), { width: imageWidth, height: imageHeight });

        // add asset if present

        async function addImageToPDF(customAsset:ICustomAsset) {
          const { url, offsetX, offsetY, scale } = customAsset;
          try {
            const encodedUrl = encodeURI(url);
            const response = await axios.get(encodedUrl, { responseType: 'arraybuffer' });
            const buffer = await Buffer.from(response.data, 'binary');
            // const assetHeight = 250;
            // const assetWidth = 250;
            await doc.image(buffer, boxX + +offsetX, boxY + +offsetY + lineHeight, {scale: scale/100}); // add scale to the image, user controls this and they decide what they want.
          } catch (error: any) {
            if(error?.response && error?.response.status === 403){
              throw new Errors.Forbidden('ERROR_LOADING_ASSET');
            }
            else {
              throw new Errors.GeneralError('ERROR_ADDING_ASSET');
            }
          }
        }
        
        // Add asset if present
        doc.fontSize(10).font('Helvetica').text(LBL_TEXT_INSIDE_BOX, boxX + 5, boxY + 5);
        if (student.customAsset.url) {
          await addImageToPDF(student.customAsset);
        }
        // Draw a box for the response area
        doc.rect(boxX, boxY, boxWidth, boxHeight).stroke();

        // // Add response area instructions
        // doc.font("Helvetica").fontSize(9).text('Write your response on the lines below.', boxX + padding * 2, boxY + padding * 2);

        // Draw lines within the box for writing
        // const lineMargin = 10;
        // const extraTop = 20;
        // doc.strokeColor('#D3D3D3');
        // for (let i = 1; i <= numberOfLines; i++) {
        //     const y = boxY + extraTop + i * lineHeight * 2.25;
        //     doc.moveTo(boxX + lineMargin, y)
        //         .lineTo(boxX + boxWidth - lineMargin, y)
        //         .stroke();
        // }

        // doc.font("Helvetica").fontSize(9).text('Remember to go back to your device and click the button "Click here" when you have finished this question and then click "Submit".', boxX + padding * 2, boxY + boxHeight - 25, {
        //     width: 500,
        //     align: 'left'
        // });
        // doc.font('Helvetica-Bold').fontSize(18).text('Do not write in this area.', margin, boxY + boxHeight + 40, { align: 'center' });

        if(isABED(student.whitelabelContext as FLAGS)){ // add footer for ABED
          const footerImgLink = encodeURI(ABED_FOOTER_IMG_LINK);
          const response = await axios.get(footerImgLink, { responseType: 'arraybuffer' });
          const buffer = Buffer.from(response.data, 'binary');
          const footerImgHeight = 50;
          doc.image(buffer, margin, boxY + boxHeight + 10, { width: boxWidth, height: footerImgHeight})
        }
    }

    // Finalize the PDF
    doc.end();

    // Wait until the PDF is completely generated
    await new Promise((resolve) => doc.on('end', resolve));

    // Convert the buffer to a base64 string
    const base64String = buffers.toString('base64');

    return { responsePdf: base64String };
  }

  maskStudentNumber(studentNumber:string) {
      let maskedStudentNumber = "";
      for (let i = 0; i < studentNumber.length; i++) {
          if (i >= 1 && i <= 5) {
              maskedStudentNumber += "*";
          } else {
              maskedStudentNumber += studentNumber[i];
          }
      }
      return maskedStudentNumber;
  }

  // limit character count(14) for display elements
  limitCharacterCount(str:string, limit = 16) {
    if (str.length > limit) {
      return str.substring(0, limit) + '...';
    }
    return str;
  }
}
