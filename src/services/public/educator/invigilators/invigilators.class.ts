import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { currentUid } from '../../../../util/uid';
import { dbDateNow } from '../../../../util/db-dates';
import { dbRawRead } from '../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class Invigilators implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: any, params?: Params): Promise<Data> {
    if (!params || !params.query ){
      throw new Errors.BadRequest();
    }
    const uid = await currentUid(this.app, params);
    const group_id = params.query.school_class_group_id
    const { invig_uid } = data
    const invigilatorRecord = await this.createAdditionalInvigilator(invig_uid, group_id, uid); 
    return invigilatorRecord;
  }

  async createAdditionalInvigilator(invig_uid:any, group_id:any, uid:any) {
    //revoke existing invigilators
    await this.revokeExistingInvigilator(invig_uid, group_id, uid)

    const role_type = 'schl_teacher_invig';
    let invigilatorRecord;
    const invigilatorEntry ={
      role_type,
      uid:invig_uid,
      group_id,
      created_by_uid:uid
    }

    invigilatorRecord = await this.app
      .service('db/write/user-roles')
      .create(invigilatorEntry);
    
    return invigilatorRecord
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    if (!params || !params.query || !params.query.data){
      throw new Errors.BadRequest();
    }
    const uid = await currentUid(this.app, params);
    const invig_uid = params.query.data.invig_uid
    const group_id = params.query.school_class_group_id
    if(invig_uid && group_id){
      await this.revokeExistingInvigilator(invig_uid, group_id, uid)
    }  
    return [];
  }

  async revokeExistingInvigilator(uid:any, group_id:any, revoked_by_uid:any){
    const existingRecords =  await dbRawRead(this.app, [uid, group_id], `
      select *
        from user_roles ur
       where ur.uid = ?
         and ur.group_id = ?
         and ur.is_revoked != 1
         and ur.role_type = 'schl_teacher_invig'
    ;`);

    const patchRecord ={
      is_revoked: 1,
      revoked_on: dbDateNow(this.app),
      revoked_by_uid
    } 

    await Promise.all(existingRecords.map(async record =>{
      await this.app
       .service('db/write/user-roles')
       .patch(record.id, patchRecord);
    }))
  }
}
