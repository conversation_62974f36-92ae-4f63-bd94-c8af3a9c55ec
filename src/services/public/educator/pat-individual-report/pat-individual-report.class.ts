import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import PDFDocument from 'pdfkit';
import { Errors } from '../../../../errors/general';
import { dbRawRead } from '../../../../util/db-raw';
import moment from 'moment';

interface Data {}

interface ServiceOptions {}

export class PatIndividualReport implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    const testWindowId = +id;
    if (!testWindowId || !params || !params.query) {
      throw new Errors.BadRequest("MISSING REQUIRED ID OR PARAMS")
    }

    const { is_bulk, schl_group_id, uid, lang } = params.query

    let studentAttempts;

    if (is_bulk == 1) {
      studentAttempts = await dbRawRead(this.app, [+testWindowId, +schl_group_id], `
        select 
          tw2.id
        , tw2.type_slug as tw_type_slug
        , tw2.date_end as tw_date
        , twtar.type_slug
        , twtar.long_name 
        , ta.uid
        , s.name as school_name
        , ta.id as test_attempt_id
        , sd.name as sd_name
        , um_sin.value as student_gov_id
        , u.first_name
        , u.last_name
        from test_windows tw
        join test_windows tw2
            ON tw2.type_slug = tw.type_slug 
            and tw2.academic_year = tw.academic_year
            and tw2.is_classroom_assessment = 0 
            and tw2.is_qa = 0 
            and tw2.is_bg = 0
        join schools s
        join school_semesters ss
        	on ss.test_window_id = tw.id
        join test_window_td_alloc_rules twtar on twtar.test_window_id = tw2.id
          AND twtar.is_secured = 1
        join school_class_test_sessions scts 
    	    on scts.slug  = twtar.type_slug 
        join school_classes sc 
          on sc.id = scts.school_class_id 
          and sc.semester_id = ss.id 
          and sc.schl_group_id = s.group_id 
        JOIN school_districts sd
          on sd.group_id = s.schl_dist_group_id
        join test_attempts ta 
          on ta.test_session_id = scts.test_session_id 
          and ta.started_on is not null
        JOIN users u 
          on u.id = ta.uid
        LEFT JOIN user_metas um_sin 
          ON um_sin.uid = u.id 
          AND um_sin.key in ('StudentIdentificationNumber', 'TestTakerIdNumber')
        JOIN user_roles ur on ur.uid = u.id
          AND ur.role_type = "schl_student"
          AND ur.is_revoked = 0
          AND ur.is_removed = 0
        WHERE tw.id = ? and s.group_id = ?
        GROUP BY ta.id
      `)
      
    }
    else {
      studentAttempts = await dbRawRead(this.app, [+uid, +testWindowId], `
        select 
          tw.id
        , tw2.title
        , tw2.title_persistent
        , tw2.date_end
        , twtar.id as twtar_id
        , twtar.type_slug
        , twtar.long_name 
        , um_dob.value dob
        , um_sin.value student_gov_id
        , ta.uid
        , u.first_name
        , u.last_name
        , s.name as school_name
        , sd.name as sd_name
        , ta.id as test_attempt_id
        from test_windows tw
        join test_windows tw2
            ON tw2.type_slug = tw.type_slug and tw2.academic_year = tw.academic_year
        join test_window_td_alloc_rules twtar on twtar.test_window_id = tw2.id
          AND twtar.is_secured = 1
        JOIN test_attempts ta on ta.twtdar_id = twtar.id
            AND ta.is_invalid = 0 and ta.is_closed = 1
        JOIN users u on u.id = ta.uid
        LEFT JOIN user_metas um_dob 
          ON um_dob.uid = u.id 
          AND um_dob.key = 'DateofBirth'
        LEFT JOIN user_metas um_sin 
          ON um_sin.uid = u.id 
          AND um_sin.key in ('StudentIdentificationNumber', 'TestTakerIdNumber')
        JOIN user_roles ur on ur.uid = u.id
            AND ur.role_type = "schl_student"
            AND ur.is_revoked = 0
          AND ur.is_removed = 0
        JOIN school_classes sc on sc.group_id = ur.group_id
        JOIN schools s on s.group_id = sc.schl_group_id
        JOIN school_districts sd on sd.group_id = sc.schl_dist_group_id
        WHERE u.id = ? AND tw.id = ?
        GROUP BY ta.id
        ORDER BY u.last_name, u.first_name

      `)
    }

    
    // const pdfBase64 = await this.createPdf(studentAttempts, testWindowId, lang)
      return {
        id,
        pdfBase64: '',
      }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }

}
