// Initializes the `public/educator/student/session-extra-time` service on path `/public/educator/student/session-extra-time`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { SessionExtraTime } from './session-extra-time.class';
import hooks from './session-extra-time.hooks';

// Add this service to the service type index
declare module '../../../../../declarations' {
  interface ServiceTypes {
    'public/educator/student/session-extra-time': SessionExtraTime & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/educator/student/session-extra-time', new SessionExtraTime(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/educator/student/session-extra-time');

  service.hooks(hooks);
}
