import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { dbRawRead } from '../../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class Session implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  async getTsUmMeta(test_session_id: number){
    const tsAsmtTypes:{test_window_id:number, asmt_type_slug:string}[] = await dbRawRead(this.app, {test_session_id}, `
      select ts.test_window_id
           , twtt.type_slug asmt_type_slug
      from test_sessions ts 
      join school_class_test_sessions scts 
        on scts.test_session_id = ts.id
      join test_window_td_types twtt 
        on twtt.type_slug = scts.slug 
        and twtt.test_window_id is null
        and twtt.is_revoked = 0
      where ts.id = :test_session_id
    `)
    if (tsAsmtTypes.length == 0){
      throw new Errors.NotFound('TS_NO_ASMT_SLUG')
    }
    return tsAsmtTypes[0];
  }
  async patchAttemptProp(prop:string, propVal:number | string, student_uid:number, params?:Params){
    const {test_session_id, attempt_id} = params?.query || {};
    if (!(student_uid && test_session_id)){ // attempt_id is optional, but more efficient when provided
      throw new Errors.BadRequest('MISSING_STU_SESSION_INFO')
    }
    let target_attempt_ids = await this.getAttemptIds(student_uid, test_session_id, attempt_id);
    // designed for single students who each have about 1 to 3 attemptin a test session
    const payload = {
      [prop]: propVal,
    }
    for (let attempt_id of target_attempt_ids){
      const attemptRecord = await this.app
        .service('db/write/test-attempts')
        .patch(attempt_id, payload);
      if (attemptRecord.test_session_id !== test_session_id){
        console.log('Session mismatch'); // todo:ERROR_HANDLING this should be treated more seriously, ticket to expand on this: ____
      }
    }
    return {
      target_attempt_ids,
      payload
    }
  }

  public async getAttemptIds(uid:number, test_session_id:number, attempt_id?:number){
    // assumes calling request is already secured
    if (attempt_id){
      return [attempt_id]
    }
    const attempts = <any[]> await this.app.service('db/read/test-attempts').find({
      query: {
        $select: ['id'],
        uid,
        test_session_id,
        is_invalid: 0,
      },
      paginate: false
    })
    const attempt_ids:number[] = attempts.map(a => a.id);
    if (attempt_ids.length == 0){
      throw new Errors.NotFound('STU_SESS_ATTEMPTS_NOT_FOUND')
    }
    return attempt_ids;
  }

  private getStudentForm(uid:number){
    this.app.service('db/read/school')
  }

  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
