// Initializes the `subsession-presets` service on path `/public/educator/subsession-presets`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { SubsessionPresets } from './subsession-presets.class';
import hooks from './subsession-presets.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/educator/subsession-presets': SubsessionPresets & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/educator/subsession-presets', new SubsessionPresets(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/educator/subsession-presets');

  service.hooks(hooks);
}
