import { K<PERSON> } from 'knex';
import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../declarations';
import { Errors } from '../../../errors/general';

interface Data {
  readReadable: CheckResult,
  readWritableCheck: CheckResult,
  writeReadable: CheckResult,
  writeWritable: CheckResult
}

interface ServiceOptions {}

interface CheckResult {
  passed: boolean,
  data?: string,
  err?: any,
  expecteUnwritable?: boolean
}

export class Health implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data> {

    const readDb:Knex = this.app.get('knexClientRead');
    const writeDb:Knex = this.app.get('knexClientWrite');
    const results = {
      readReadable: await this.checkReadable(readDb),
      readWritableCheck: await this.checkWritable(readDb, params),
      writeReadable: await this.checkReadable(writeDb),
      writeWritable: await this.checkWritable(writeDb, params)
    }

    results.readWritableCheck.expecteUnwritable = this.app.get('expectReadReplica')
    if (results.readWritableCheck.expecteUnwritable) {
      results.readWritableCheck.passed = !results.readWritableCheck.passed;
    }

    if (Object.values(results).find(res => !res.passed) !== undefined) {
      throw new Errors.GeneralError(JSON.stringify(results));
    }

    return results;
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async checkReadable(db:Knex): Promise<CheckResult> {
    try {
      const res = await db.raw(`SELECT DATE_FORMAT(NOW(), '%Y-%m-%dT%TZ');`);
      return {
        passed: true,
        data: res[0]
      };
    } catch (err) {
      return {
        passed: true,
        err: err
      };
    }
  }

  async checkWritable(db:Knex, params?: Params, invertResult?:boolean): Promise<CheckResult> {
    try {
      const res = await db.raw(`
        INSERT INTO \`log\` (
          created_on,
          created_by_uid,
          slug,
          \`data\`,
          user_agent,
          ip
        ) VALUES (
          CURRENT_TIMESTAMP,
          326,
          'HEALTH_CHECK',
          '{}',
          :user_agent,
          :ip
        );`,
        {
          user_agent: params?.headers?.['user-agent'] || null,
          ip: params?.ip || null
        });
        return {
          passed: true,
          data: res[0]
        };
    } catch (err) {
      return {
        passed: false,
        err: err
      };
    }
  }
}
