import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead } from '../../../../util/db-raw';
import { dbDateNow } from '../../../../util/db-dates';

interface Data {}

interface ServiceOptions {}

export class KnowledgeBase implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    const {context_slug} = params?.query!;
    return dbRawRead(this.app, {context_slug}, `
      select * 
      from support_knowledge_base
      where is_revoked = 0 and ${params?.query?.context_slug ? `context_slug = :context_slug` : `context_slug is null`} 
      ;
    `)
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
  
  async verifyUserRole(params?: Params){
    if(!params){
      throw new Errors.Forbidden("USER_ROLE_NOT_FOUND");
    }
    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    const uid = userInfo.uid;
    // Confirm user roles
    const userRole = await dbRawRead(this.app, {uid}, `
      select * from 
        user_roles 
      where uid = :uid
        and role_type like "test_ctrl%"
        and is_revoked = 0
        and is_removed = 0
      ;
    `)
    
    if (userRole.length <= 0){
      throw new Errors.Forbidden("USER_ROLE_NOT_FOUND");
    }
    return uid;
  }
}
