// Initializes the `public/landing/knowledge-base` service on path `/public/landing/knowledge-base`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { KnowledgeBase } from './knowledge-base.class';
import hooks from './knowledge-base.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/landing/knowledge-base': KnowledgeBase & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/landing/knowledge-base', new KnowledgeBase(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/landing/knowledge-base');

  service.hooks(hooks);
}