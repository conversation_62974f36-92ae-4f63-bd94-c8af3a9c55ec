// Initializes the `public/landing/practice-tests` service on path `/public/landing/practice-tests`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { PracticeTests } from './practice-tests.class';
import hooks from './practice-tests.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/landing/practice-tests': PracticeTests & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/landing/practice-tests', new PracticeTests(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/landing/practice-tests');

  service.hooks(hooks);
}
