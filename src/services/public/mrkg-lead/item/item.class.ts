import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import * as Errors from "@feathersjs/errors";
import {DATETIME} from "../../../../types/db-types";
import {ITestWindow} from "../../../db/schemas/test_windows.schema";
import { Knex } from 'knex';
import { JsonWebTokenError } from 'jsonwebtoken';

interface Data {}

interface ServiceOptions {}

interface IMarkingExemplarStage {
  id: number;
  raw: string;
  profile_id?: number;
  uid: number;
  created_on: DATETIME;
  created_by_uid: number;
  is_archived: number;
}

export class Item implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<any> {
    if (params && params.query) {
        const item_id = params.query.item_id;
        if(item_id) {
            const db:Knex = this.app.get('knexClientRead');

            let rubricRaw = await db.raw(`select * from marking_rubric_entries where rubric_id = (select rubric_id from marking_rubric_items where item_id = ?);`, [item_id]);
            let rubric:any = {};
            for(let entry of rubricRaw[0]) {
              rubric[entry.score] = {
                rubricMarkup:entry.markdown,
                rationaleOptions:JSON.parse(entry.rationale_options)
              };
            }

            let documentsRaw = await db.raw(`select * from marking_documents where id in (select document_id from marking_document_items where item_id = ?);`, [item_id]);
            let documents:any[] = [];
            for(let entry of documentsRaw[0]) {
              documents.push({
                name:entry.name,
                url:entry.url,
                type:entry.doc_type,
                show:false
              });
            }

            let res = await db.raw('select mid.*, ac.language from marking_item_data mid left join assessment_components ac on ac.id = mid.assessment_component_id where mid.id = ?', [item_id])


            if(res[0].length == 1) {
              res[0][0].rubric = rubric;
              res[0][0].documents = documents;
            }

            return {data:res[0]};
        }
        else if(params.query.uid && params.query.marking_session_id) {
          const db:Knex = this.app.get('knexClientRead');
          let itemIDs = await db.raw(`select item_ids from marking_pairs where id in (select pair_id from marking_pairs_users where uid = ?) and marking_session_id = ?;`, [params.query.uid, params.query.marking_session_id]);
          let items = [];
          if(!itemIDs[0][0]) {
            return [];
          }
          for(let itemID of JSON.parse(itemIDs[0][0].item_ids)) {
            let itemRaw = await db.raw(`select * from marking_item_data where id = ?;`, [itemID]);

            if(!itemRaw[0] || !itemRaw[0][0]) {
              return itemRaw[0];
            }
            let item = itemRaw[0][0];

            let setRecord = <any> await this.app.service('db/read/marking-exemplar-sets').find({
              query: {
                item_id:item.id,
                exemplar_set_type_id:1
              }
            })
            item.training_complete = setRecord.data[0].training_complete;

            items.push(item);


          }

          return items;

        }
        else if(params.query.uid) {
          const db:Knex = this.app.get('knexClientRead');
          let itemIDs = await db.raw(`select item_ids from marking_pairs where id in (select pair_id from marking_pairs_users where uid = ?);`, [params.query.uid]);
          let items = [];
          if(!itemIDs[0][0]) {
            return [];
          }
          for(let itemID of JSON.parse(itemIDs[0][0].item_ids)) {
            let itemRaw = await db.raw(`select * from marking_item_data where id = ?;`, [itemID]);

            if(!itemRaw[0] || !itemRaw[0][0]) {
              return itemRaw[0];
            }
            let item = itemRaw[0][0];

            let setRecord = <any> await this.app.service('db/read/marking-exemplar-sets').find({
              query: {
                item_id:item.id,
                exemplar_set_type_id:1
              }
            })
            item.training_complete = setRecord.data[0].training_complete;

            items.push(item);


          }

          return items;

        }
        else {
          const $limit = 5000;
          return await this.app.service('db/read/marking-item-data').find({
            query: {
              $limit,
              $sort: {
                id: -1
              }
            }
          });
        }


    }
    throw new Errors.BadRequest('INVALID_PARAMS');
  }

  async get (id: Id, params?: Params): Promise<Data> {
    return await this.app.service('db/read/marking-item-data').get(id);
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: any, params?: Params): Promise<Data> {
    if(data.finishedOnSiteTraining) {
      const db:Knex = this.app.get('knexClientRead');
      let setRecord = <any> await this.app.service('db/read/marking-exemplar-sets').find({
        query: {
          item_id:id,
          exemplar_set_type_id:1,
          $limit:1
        }
      });
      let set_id = setRecord.data[0].id;

      let set = await db.raw('select * from marking_exemplar_set_responses inner join marking_exemplars_selected_responses on marking_exemplar_set_responses.taqr_id = marking_exemplars_selected_responses.taqr_id where marking_exemplar_set_responses.set_id = ?',
      [set_id]);

      for(let res of set[0]) {
        let resRecord = await this.app.service('db/read/marking-responses').get(res.taqr_id);
        let setTags = JSON.parse(resRecord.setTags);
        if(!setTags.includes('training')) setTags.push('training');
        await this.app.service('db/write/marking-responses').patch(res.taqr_id, {
          leaderAssignment:res.stageTwoSelection,
          setTags:JSON.stringify(setTags)
        });
      }
      // Assign all markers to the set if they are assigned to the item
      await this.assignAllMarkersToSet(id, +set_id);
    }
    if(data.finishedReliability) {
      const db:Knex = this.app.get('knexClientRead');
      let setRecord = <any> await this.app.service('db/read/marking-exemplar-sets').find({
        query: {
          item_id:id,
          exemplar_set_type_id:2,
          $limit:1
        }
      });
      let set_id = setRecord.data[0].id;

      let set = await db.raw('select * from marking_exemplar_set_responses inner join marking_exemplars_selected_responses on marking_exemplar_set_responses.taqr_id = marking_exemplars_selected_responses.taqr_id where marking_exemplar_set_responses.set_id = ?',
      [set_id]);

      for(let res of set[0]) {
        let resRecord = await this.app.service('db/read/marking-responses').get(res.taqr_id);
        let setTags = JSON.parse(resRecord.setTags);
        if(!setTags.includes('reliability')) setTags.push('reliability');
        await this.app.service('db/write/marking-responses').patch(res.taqr_id, {
          leaderAssignment:res.stageTwoSelection,
          setTags:JSON.stringify(setTags)
        });
      }
    }

    return await this.app.service('db/write/marking-item-data').patch(id, data);

  }

  async assignAllMarkersToSet(item_id : NullableId, set_id : Number) {
    if (!item_id || !set_id) {
      throw new Error("assignAllMarkersToSet: Please provide a valid item_id, set_id");
    }

    const markersItemService = this.app.service('public/mrkg-lead/markers-assigned-item')
    const db:Knex = this.app.get('knexClientRead');
    const getItemMarkersQuery =
      `
      SELECT u.id, mimt.marking_exemplar_set
      FROM marking_pairs mp
      JOIN marking_pairs_users mpu on mpu.pair_id = mp.id
      JOIN users u on u.id = mpu.uid
      LEFT JOIN marking_item_marker_tasks mimt on mimt.uid=u.id
        AND mimt.marking_exemplar_set = ?
        AND mimt.is_removed = 0
      WHERE mp.item_ids like '%?%';
      `;
    let results = await db.raw(getItemMarkersQuery, [+set_id, +item_id]);
    results = results[0];

    const marking_session_id = await this.getMarkingSessionId(set_id);
    const alreadyAssignedMarkers = new Set();

    for (let marker of results) {
      if (+marker.marking_exemplar_set == +set_id) {
        alreadyAssignedMarkers.add(marker.id);
      }
    }

    for (let marker of results) {
      if (!alreadyAssignedMarkers.has(marker.id)) {
        const query = {
          uid: marker.id,
          marking_exemplar_set: set_id,
          marking_window_item_id: item_id,
          marking_window_id: marking_session_id,
        };

        await markersItemService.create({}, { query });
        alreadyAssignedMarkers.add(marker.id);
      }
    }
  }

  async getMarkingSessionId(set_id : Number) {
    const db:Knex = this.app.get('knexClientRead');
    const getItemMarkersQuery =
      `
      SELECT marking_window_id
      FROM marking_exemplar_sets
      WHERE id = ?;
      `;
    let results = await db.raw(getItemMarkersQuery, [+set_id]);

    return results[0][0].marking_window_id;
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
