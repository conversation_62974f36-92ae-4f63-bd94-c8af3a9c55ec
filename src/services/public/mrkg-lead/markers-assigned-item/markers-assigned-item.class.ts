import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Knex } from 'knex';
import { dbDateNow } from '../../../../util/db-dates';
import { Application } from '../../../../declarations';

interface Data {}

interface ServiceOptions {}

export class MarkersAssignedItem implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if(!params || !params.query || !params.query.item_id) {
      throw new Error("Please provide params.query.item_id");
    }

    const item_id = params.query.item_id;
    const db:Knex = this.app.get('knexClientRead');
    const sqlQuery =
      `
      SELECT u.id, u.first_name, u.last_name
      FROM marking_pairs mp
        join marking_pairs_users mpu on mpu.pair_id = mp.id
        join users u on u.id = mpu.uid
      WHERE mp.item_ids like '%?%';
      `
    let usersAssignedToItem = await db.raw(sqlQuery, [+item_id]);
    return usersAssignedToItem[0];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    if(!params || !params.query || !params.query.uid
        || !params.query.marking_exemplar_set) {
      throw new Error("Please provide params.query.uid and params.query.marking_exemplar_set");
    }

    const uid = params.query.uid;
    const marking_exemplar_set = params.query.marking_exemplar_set;

    const db:Knex = this.app.get('knexClientRead');
    const sqlQuery =
      `
      SELECT * from marking_item_marker_tasks
      WHERE marking_exemplar_set = ? AND uid = ?;
      `
    let assignment = await db.raw(sqlQuery, [+marking_exemplar_set, +uid]);
    return assignment[0];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    if(!params || !params.query || !params.query.marking_window_item_id
        || !params.query.uid || !params.query.marking_window_id
        || !params.query.marking_exemplar_set) {
      throw new Error("Please provide the correct parameters");
    }


    const uid = params.query.uid;
    const marking_exemplar_set = params.query.marking_exemplar_set;
    const marking_window_id = params.query.marking_window_id;
    const marking_window_item_id = params.query.marking_window_item_id;

    const db:Knex = this.app.get('knexClientRead');
    const sqlQuery =
      `
      SELECT uid
      FROM marking_item_marker_tasks
      WHERE uid = ? AND marking_window_item_id = ?
        AND marking_window_id = ? AND marking_exemplar_set = ?
      `
    const valuesToSearch = [+uid, +marking_window_item_id, +marking_window_id, +marking_exemplar_set];
    const results = await db.raw(sqlQuery, valuesToSearch);

    if (results[0].length > 0) {
      throw new Error("The specified marker is already assigned to this training set");
    }

    const highestOrderForMarker = await this.getHighestOrder(uid, marking_window_item_id, marking_window_id);
    const endTime = await this.getEndTimeForSession(marking_window_id);

    await this.app.service('db/write/marking-item-marker-tasks').create(
      {uid,
       marking_window_item_id,
       marking_window_id,
       marking_exemplar_set,
       order: highestOrderForMarker + 1,
       task_group_name: 'Training',
       caption: 'Training',
       task_group_num: 1,
       end_time: endTime,
       start_time: dbDateNow(this.app),
      }
    );
    return {uid,
      marking_window_item_id,
      marking_window_id,
      marking_exemplar_set,
      task_group_name: 'Training',
      caption: 'Training',
      task_group_num: 1,
      };
  }

  async getHighestOrder(uid : Number, marking_window_item_id : Number, marking_window_id : Number) {
    let highestOrder = 0;
    const db:Knex = this.app.get('knexClientRead');
    const sqlQuery =
      `
      SELECT max(mimt.order) as max_order
      FROM marking_item_marker_tasks as mimt
      WHERE uid = ? and marking_window_item_id = ? and marking_window_id = ? and mimt.is_removed = 0
      GROUP BY uid, marking_window_item_id, marking_window_id;
      `
    const valuesToSearch = [+uid, +marking_window_item_id, +marking_window_id]
    const results = await db.raw(sqlQuery, valuesToSearch);

    if (results[0].length > 0) {
      highestOrder = Math.max(+results[0][0].max_order, highestOrder);
    }

    return highestOrder;
  }

  async getEndTimeForSession(marking_window_id : Number) {
    const db:Knex = this.app.get('knexClientRead');
    const sqlQuery =
      `
      SELECT endDate
      FROM marking_session_data as msd
      WHERE msd.id = ?;
      `
    const valuesToSearch = [+marking_window_id]
    const results = await db.raw(sqlQuery, valuesToSearch);

    return results[0][0].endDate;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    if(!params || !params.query || !params.query.uid
      || !params.query.marking_exemplar_set) {
        throw new Error("Please provide params.query.uid and params.query.marking_exemplar_set");
    }

    const uid = params.query.uid;
    const marking_exemplar_set = params.query.marking_exemplar_set;

    const db:Knex = this.app.get('knexClientRead');
    const sqlQuery =
      `
      SELECT id
      FROM marking_item_marker_tasks
      WHERE uid = ? AND marking_exemplar_set = ?;
      `
    const valuesToSearch = [+uid, +marking_exemplar_set]
    const results = await db.raw(sqlQuery, valuesToSearch);

    const assignmentToRemove = results[0][0].id;

    await this.app.service('db/write/marking-item-marker-tasks').remove(assignmentToRemove);

    return { assignmentToRemove };
  }
}
