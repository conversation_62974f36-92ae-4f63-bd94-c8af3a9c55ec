import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Knex } from 'knex';
import { Application } from '../../../../declarations';
import logger from '../../../../logger';
import { currentUid } from '../../../../util/uid';

interface Data {}

interface ServiceOptions {}

export class MarkingExemplarSets implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<any> {

    if(!params || !params.query) return [];

    const uid = await currentUid(this.app, params);
    let user = await this.app.service('db/read/users').get(uid);
    const db:Knex = this.app.get('knexClientRead');


    let itemRaw = await db.raw(`
    select name, leadersRef from marking_item_data where id = ?;
      `, [+params.query.item_id]);

    let leadersRef = JSON.parse(itemRaw[0][0].leadersRef);
    if(!leadersRef.includes(uid) && user.account_type != 'mrkg-coord') {
      return 0;
    }

    let query;
    if(params.query.only_training) {
      query = {
        marking_window_id:params.query.marking_session_id,
        item_id:params.query.item_id,
        $or: [
          {exemplar_set_type_id: 1},
          {exemplar_set_type_id: 3}
        ],
        $limit:10000
      }
    }
    else {
      query = {
        marking_window_id:params.query.marking_session_id,
        item_id:params.query.item_id,
        $limit:10000
      }
    }

    let setRecords = <any> await this.app.service('db/read/marking-exemplar-sets').find({ query });


    for(let record of setRecords.data) {
      let assignedMarkers = await db.raw(`select u.id, u.first_name, u.last_name, u.img_url from marking_item_marker_tasks mimt
      join users u on u.id = mimt.uid where mimt.marking_window_id = ? and mimt.marking_exemplar_set = ? and mimt.is_removed = 0 group by mimt.uid`, [+params.query.marking_session_id, +record.id]);

      record.assignedMarkers = assignedMarkers[0];
      let itemRecord = <any> await this.app.service('db/read/marking-item-data').get(params.query.item_id);
      logger.silly(itemRecord);
      record.ready = record.exemplar_set_type_id === 1 ? itemRecord.finishedOnSiteTraining : itemRecord.finishedReliability;
      record.leadersRef = itemRecord.leadersRef;
      record.itemName = itemRecord.name;
    }

    return setRecords;


  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return this.app.service('db/read/marking-exemplar-sets').get(id);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: any, params?: Params): Promise<Data> {
    if(!id) throw new Error('Please provide an id. (set id)');
    if(!params) return [];

    await this.app.service('db/write/marking-exemplar-sets').patch(id, data);
    data.set_id = id;
    return await this.app.service('public/mrkg-lead/calculate-reliability-release').patch(null, data, params);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
