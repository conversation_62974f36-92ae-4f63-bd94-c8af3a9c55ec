import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import * as Errors from "@feathersjs/errors";
import { currentUid } from '../../../../util/uid';
import { Knex } from 'knex';

interface Data {}

interface IActiveBatchInfo {
  batchID:number,
  positions?: any[],
  responses?: {
    data: Array<{
      id: number,
      [key:string]: any,
    }>
  }
}
interface ServiceOptions {}

export class CurrentBatch implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<any> {
    if (params && params.query) {
      const marking_session_id = +params.query.marking_session_id;
      const item_id = +params.query.item_id;
      const uid = await currentUid(this.app, params);

      const batchID = +params.query.batchID; // not sure why we are passing this in, it gets overridden in all cases
      //We are not always passing this, if we dont know the batch id we pass marking_session_id and item id to get the newest batch
      if(marking_session_id && uid && item_id) {
        const activeBatchInfo:IActiveBatchInfo = <any> await this.ensureActiveBatch(uid, marking_session_id, item_id, batchID);
        if (activeBatchInfo && activeBatchInfo.batchID && activeBatchInfo.batchID !== -1){
          await this.app.service('public/mrkg-mrkr/current-batch-position').ensureItemMarkerBatchStatusRecord(uid, item_id, activeBatchInfo.batchID);
        }
        return activeBatchInfo;
      }
      else if(batchID) {
        return await this.ensureActiveBatch(uid, marking_session_id, item_id, batchID);
      }

    }
    return [];
  }

  async ensureActiveBatch(uid:number, marking_session_id:number, item_id:number, batchID:number) : Promise<IActiveBatchInfo | null> {
    const db:Knex = this.app.get('knexClientRead');
    if (marking_session_id && item_id && uid) {


      const pairs = <any> await this.app.service('db/read/marking-pairs').find({
        query: {
          marking_session_id,
          $limit: 1000
        }
      });
      const releventPairs = pairs.data.filter((p:any) => JSON.parse(p.item_ids).includes(item_id));
      const pairIDs = releventPairs.map((p:any) => p.id);
      const pairUsers = <any> await this.app.service('db/read/marking-pairs-users').find({
        query: {
          pair_id :{
            $in: pairIDs
          },
          $limit: 1000,
        }
      });

      let pid:any;

      for(let i = 0; i < pairUsers.data.length; i++) {
        if(pairUsers.data[i].uid == uid) {
          pid = pairUsers.data[i].pair_id;
        }
      }


      const batchRecords = <any> await this.app.service('db/read/marking-claimed-batches').find({
        query: {
          $limit: 1,
          pid,
          marking_window_item_id: item_id,
          is_marked:0
        }
      });


      if(batchRecords.data.length > 0) {
        if(!batchRecords.data[0].ready) {
          throw new Errors.GeneralError('CANNOT_OBTAIN_RESPONSES');
        }
        batchID = batchRecords.data[0].id;
      }
      else {
        batchID = await this.createNewBatch(batchRecords, uid, item_id, marking_session_id, pid);
      }

      if(batchID == -1) {
        return {batchID, responses:undefined};
      }
      if(!batchID){
        // return ("ERROR: PLEASE REFRESH") // why not an error?
        throw new Errors.GeneralError('CANNOT_OBTAIN_RESPONSES');
      };
      //return it
      const batchResponses = <any> await this.app.service('db/read/marking-claimed-batch-responses').find({
        query: {
          $limit: 1000,
          claimed_batch_id: batchID
        }
      });

      const responses = await db.raw('select marking_responses.* from marking_claimed_batch_responses inner join marking_responses on marking_claimed_batch_responses.taqr_id = marking_responses.id where marking_claimed_batch_responses.claimed_batch_id = ?', [+batchID]);

      //map batchResponses to taqr_id
      const ids = batchResponses.data.map((resp:any) => resp.taqr_id );

      // const responses = <any> await this.app.service('db/read/marking-responses').find({
      //   query: {
      //     $limit: 1000,
      //     id: {
      //       $in: ids
      //     }
      //   }
      // })

      const scoreRecords = <any> await this.app.service('db/read/marking-response-scores').find({
        query: {
          $limit: 10000,
          taqr_id: {
            $in: ids
          }
        }
      });

      for(let response of responses[0]) {
        response.scores = scoreRecords.data.filter((score:any) => score.taqr_id == response.id);
        if(response.response_type == 'SCAN' && response.raw) {
          response.raw = JSON.parse(response.raw);
        }
      }

      return  {batchID, responses:{data:responses[0]}};

    }
    else if(batchID){
      const batchResponses = <any> await this.app.service('db/read/marking-claimed-batch-responses').find({
        query: {
          $limit: 1000,
          claimed_batch_id: batchID
        }
      });

      //map batchResponses to taqr_id
      const ids = batchResponses.data.map((resp:any) => resp.taqr_id );

      const responses = await db.raw('select marking_responses.* from marking_claimed_batch_responses inner join marking_responses on marking_claimed_batch_responses.taqr_id = marking_responses.id where marking_claimed_batch_responses.claimed_batch_id = ?', [+batchID]);

      // const responses = <any> await this.app.service('db/read/marking-responses').find({
      //   query: {
      //     $limit: 1000,
      //     id: {
      //       $in: ids
      //     }
      //   }
      // })

      const scoreRecords = <any> await this.app.service('db/read/marking-response-scores').find({
        query: {
          $limit: 10000,
          taqr_id: {
            $in: ids
          }
        }
      });

      for(let response of responses[0]) {
        if(response.response_type == 'SCAN' && response.raw) {
          response.raw = JSON.parse(response.raw);
        }
        response.scores = scoreRecords.data.filter((score:any) => score.taqr_id == response.id);
      }
      return {batchID, responses:{data:responses[0]}};
    }

    return {batchID:0, responses:{data:[]}};
  }

  private async createNewBatch(batch:any, uid:number, itemID:number, marking_session_id:number, pid:number){

      // insert into marking_claimed_batches (item_id, pid)
      // take the next x unmarked responses (20)
    let item = await this.app.service('db/read/marking-item-data').get(itemID);
    let batchSize = item.batch_size;

      // insert them into marking_claimed_batch_responses (id from claimed_batches, window_response_id == taqr_id, is_marked == 0
    let activeUnscoredReliability = await this.getActiveUnscoredReliability(uid, itemID, pid);

    let responses;
    if(activeUnscoredReliability){
      responses = <any> await this.app.service('db/read/marking-responses').find({
        query: {
          $limit: batchSize - 1,
          item_id:itemID,
          marking_session_id,
          setTags: '[]',
          claimed:0,
          exclude_from_pool:0
        }
      });
      responses.data.splice(Math.floor(Math.random()*(responses.data.length - 1)), 0, activeUnscoredReliability);
    }
    else {
      responses = <any> await this.app.service('db/read/marking-responses').find({
        query: {
          $limit: batchSize,
          item_id:itemID,
          marking_session_id,
          setTags: '[]',
          claimed:0,
          exclude_from_pool:0
        }
      });
    }


    if(responses.data.length == 0) {
      return -1;
    }

    let res = await this.app.service('db/write/marking-claimed-batches').create({
      marking_window_item_id:itemID,
      pid,
      ready:0
    });

    //check if more than one was created
    //if the fastest write was not mine, stop here. let the client know they should refresh.
    const batchCheck = <any> await this.app.service('db/read/marking-claimed-batches').find({
      query: {
        $limit: 10000,
        pid,
        marking_window_item_id:itemID,
        is_marked:0
      }
    });


    if(batchCheck.data.length == 2) {
      //more than one was created

      //if(one of the created batches is bigger than mine), i won the race
      //I can delete the other one and continue as normal.
      if(batchCheck.data[0].id > res.data[0].id) {
        await this.app.service('db/write/marking-claimed-batches').remove(batchCheck.data[0].id);
      }
      else if(batchCheck.data[1].id > res.data[0].id) {
        await this.app.service('db/write/marking-claimed-batches').remove(batchCheck.data[1].id);
      }
      //else if(one of the created batches is smaller than mine), i lost the race.
        //I can quit here and let the client know to refresh.
      else if(batchCheck.data[0].id < res.data[0].id || batchCheck.data[1] < res.data[0].id) {
        return false;
      }

    }

    //insert into batch
    let insertedCount = 0;
    for(let response of responses.data) {
      //claim the response
      await this.app.service('db/write/marking-responses').patch(response.id, {
        claimed: 1
      });

      const respResp = await this.app.service('db/write/marking-claimed-batch-responses').create({
        claimed_batch_id:res.id,
        window_response_id:response.id,
        taqr_id:response.id,
        is_marked:0
      })

      insertedCount++;
      if(insertedCount == responses.data.length) {
        await <any> await this.app.service('db/write/marking-claimed-batches').patch(res.id, {
          ready:1
        })
        return res.id;
      }
    }

  }

  private async getActiveUnscoredReliability(uid:number, item_id:number, pid:number) {
    const db:Knex = this.app.get('knexClientRead');
    let now = new Date()
    let reliabilityResponses = await db.raw(`select mrsr.release_start, mrsr.release_end, mr.*
    from marking_exemplar_sets mes
    join marking_item_marker_tasks mimt on mimt.marking_exemplar_set = mes.id
    left outer join marking_reliability_set_responses mrsr on mrsr.set_id = mes.id
    inner join marking_responses mr on mrsr.taqr_id = mr.id
    where mes.item_id = ?
    and mes.exemplar_set_type_id = 2
    and mr.setTags like '%reliability%'
    and mimt.is_removed = 0
    and mimt.uid in (select uid from marking_pairs_users where pair_id = ?)`, [+item_id, +pid]);
    for(let resp of reliabilityResponses[0]) {
      if(resp.release_start && now > new Date(resp.release_start) && resp.release_end && now < new Date(resp.release_end)) {
        let scores = await db.raw('select * from marking_response_scores where taqr_id = ? and uid = ?;', [+resp.id, +uid]);
        if(scores[0].length == 0) {
          return resp;
        }
      }
    }

    return null;
  }


  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return await this.app.service('db/write/marking-claimed-batches').patch(id, data);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
