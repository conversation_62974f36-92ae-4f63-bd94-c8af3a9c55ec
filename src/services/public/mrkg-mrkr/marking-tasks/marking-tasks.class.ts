import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { currentUid } from '../../../../util/uid';

interface Data {}

interface ServiceOptions {}

export class MarkingTasks implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if(!params || !params.query) {
      throw new Error("Please provide query parameters.");
    }
    return await this.app.service('db/read/marking-item-marker-tasks').find({query:{
      ...params.query,
      is_removed: 0
    }});
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if(!params) return data;

    const uid = await currentUid(this.app, params);
    let taskRecords = await <any> this.app.service('db/read/marking-item-marker-tasks').find({
      query: {
        uid,
        marking_exemplar_set:id,
        is_removed: 0
      }
    })

    if(taskRecords.data.length != 1) throw new Error("Found <> 1 tasks to update.");

    return await this.app.service('db/write/marking-item-marker-tasks').patch(taskRecords.data[0].id, data);

  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
