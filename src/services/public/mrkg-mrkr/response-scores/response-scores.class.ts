import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { dbDateNow } from '../../../../util/db-dates';

interface Data {}

interface ServiceOptions {}

export class ResponseScores implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if(!params || !params.query) {
      return [];
    }
    return await this.app.service('db/read/marking-response-scores').find({
      query: {
        taqr_id:params.query.taqr_id,
        $limit:10000
      }
    })
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: any, params?: Params): Promise<Data> {
    let res;
    if(!id) {
      res = await this.app.service('db/write/marking-response-scores').create({
        taqr_id:data.score.taqr_id,
        uid:data.score.uid,
        score:data.score.score,
        score_b:data.score.score_b,
        score_c:data.score.score_c,
        original_score:data.score.original_score,
        timestamp:data.score.timestamp,
        rationale:data.score.rationale,
        reportOption:data.score.reportOption,
        reportReason:data.score.reportReason,
        checkpoint:data.score.checkpoint,
        sent_to_leader:data.score.sent_to_leader
      });
    }
    else {
      res = await this.app.service('db/write/marking-response-scores').patch(id, {
        taqr_id:data.score.taqr_id,
        uid:data.score.uid,
        score:data.score.score,
        original_score:data.score.original_score,
        score_b:data.score.score_b,
        score_c:data.score.score_c,
        timestamp:data.score.timestamp,
        rationale:data.score.rationale,
        reportOption:data.score.reportOption,
        reportReason:data.score.reportReason,
        checkpoint:data.score.checkpoint,
        sent_to_leader:data.score.sent_to_leader,
        last_touched_on: dbDateNow(this.app)
      });
    }
    
    return res;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
