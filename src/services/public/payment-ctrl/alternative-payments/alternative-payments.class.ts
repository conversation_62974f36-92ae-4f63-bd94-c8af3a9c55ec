import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead, dbRawWrite } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';
import { dbDateNow } from '../../../../util/db-dates';

interface Data {
  [key:string]: any,
}

interface ServiceOptions {}

export enum AlternativeAction{
  CONTACT_EQAO = 'CONTACT_EQAO',
  APPROVE = 'APPROVE',
  REFUND = 'REFUND',
  CANCEL = 'CANCEL',
  PENDING = 'PENDING',
  EDIT_TRANSACTION = 'EDIT_TRANSACTION'
}

export class AlternativePayments implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    const alternatives = await dbRawRead(this.app, [], `
       select tsp.id as test_session_purchase_id
            , tsp.id as invoice_number
            , 'Alternative' as purchase_method
            , aps.status as payment_status
            , tsp.purchase_trans_id as purchase_trans_num
            , tsp.purchase_refund_id as purchase_refund_num
            , tsp.approved_on as purchased_on -- by eqao purchase date is the approved day not order date
            , sc.name as class_name
            , schl.name as schl_name
            , schl.foreign_id as schl_mident
            , sc.group_type as assessment_name
            , tsp.test_window_id as admin_window
            , tsp.num_student_attempts_purchased as num_student_attempts_purchased
            , count(sap.id) as num_stu_attempt_used
            , tsp.purchased_total as purchase_price
            , tsp.refuneded_total as recovery_value
            , tsp.approved_on
            , concat(u.first_name,' ',u.last_name) as approved_by
            , tsp.is_refunded
            , tsp.refunded_on
            , concat(u2.first_name,' ',u2.last_name) as refunded_by
            , tsp.refunded_student_num as refunded_student_count
          from test_session_purchases tsp
          join alternative_payment_status aps on aps.id = tsp.alternative_status
          join school_classes sc on sc.group_id = tsp.class_group_id
          join schools schl on schl.group_id = tsp.schl_group_id
     left join student_attempt_purchases sap on sap.ts_purchase_id = tsp.id and sap.is_revoked != 1 and sap.assigned_ta_id is not null and sap.used_on is not null
          join assessment_prices ap on ap.test_window_id = tsp.test_window_id and ap.is_revoked = 0
     left join users u on u.id = tsp.approved_by_uid
     left join users u2 on u2.id = tsp.refunded_by_uid
         where tsp.purchase_method_id = 2 -- alternative purchase method id is 2
           and tsp.is_revoked != 1
      group by tsp.id
      order by tsp.id desc    
    ;`)
    return alternatives
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create(data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async updatePurchaseInvoice(purchaseInvoiceId: number, data: any) {
    await this.app
    .service('db/write/test-session-purchases')
    .patch(purchaseInvoiceId, data)
    
    if(data.alternative_status && data.alternative_status !== 4) {
      let refundData = {}
      if(data.alternative_status === 5) {
        refundData = {
          is_refunded: 1,
          refunded_on: dbDateNow(this.app),
          refunded_by_uid: data.refunded_by_uid
        }
      }
      const sapl= await this.updateStudentAttempt({
        payment_status: data.alternative_status,
        ...refundData
      }, {
        ts_purchase_id: purchaseInvoiceId,
        is_revoked: 0,
        is_used: 0,
      })
      console.log('sapl', sapl)
    }

    return this.app
    .service('db/write/purchase-invoices-log')
    .create({
      invoice_id: purchaseInvoiceId,
      updated_on: dbDateNow(this.app),
      payment_status_id: data.alternative_status,
      is_approved: data.is_approved,
      is_refunded: data.is_refunded
    })
  }
  
  async updateStudentAttempt(patchData: any, where: any) {
    const student_attempt_purchases = <Paginated<any>> await this.app
      .service('db/read/student-attempt-purchases')
      .find({
        query: where
    })
    const sapPromises = [];
    const sapLogPromises = [];
    for(const sap of student_attempt_purchases.data) {
      sapPromises.push(this.app
        .service('db/write/student-attempt-purchases')
        .patch(sap.id, patchData)
      );
      sapLogPromises.push(this.app
        .service('db/write/purchased-attempts-log')
        .create({
          purchased_attempt_id: sap.id,
          updated_on: dbDateNow(this.app),
          is_used: patchData.is_used,
          is_refunded: patchData.is_refunded
      }))
    }
    return Promise.all(sapPromises);
  }

  private async sendAlternativePaymentEmail(testSessionPurchaseId: number, action: AlternativeAction, data: any) {
    const tsp = (await dbRawRead(this.app, [data.test_session_purchase_id], `
      SELECT 	tsp.schl_group_id,
              tsp.id,
              tsp.test_window_id,
              tsp.purchase_trans_id,
              tsp.purchase_by_uid,
              tsp.purchased_total,
              tsp.refuneded_total,
              tsp.purchase_refund_id,
              sc.name as session_name,
              schl.foreign_id as schl_mident
      FROM test_session_purchases tsp
      JOIN school_classes sc on sc.group_id = tsp.class_group_id
      JOIN schools schl on schl.group_id = sc.schl_group_id 
      WHERE 	tsp.id = ? 
      AND		  tsp.is_revoked != 1
      LIMIT   1;
    `))[0]
    const emailData = {
      schl_group_id: tsp.schl_group_id, 
      purchase_by_uid: tsp.purchase_by_uid,
      invoiceId: tsp.id,
      transaction_number: data.transtionId || tsp.purchase_trans_id,
      schoolName: data.schoolName,
      assessmentName: data.assessmentName,
      administrationWindow: tsp.test_window_id,
      sessionNames: [tsp.session_name],
      numOfStudents: data.numOfStudents,
      totalCost: tsp.purchased_total,
      totalRefund: data.total_refund,
      refundStudentNum: data.refundStudentNum,
      refundTransactionNumber: data.purchase_refund_id,
      purchase_method_id: 2,
      schl_mident: tsp.schl_mident,
      action
    };
    await this.app.service('public/school-admin/session-purchase').sendInvoiceEmail(emailData);
  }

  async approveAlternativePayment(data: any, approved_by_uid: number) {
    await this.updatePurchaseInvoice(data.test_session_purchase_id, {
      purchase_trans_id: data.transtionId,
      alternative_status: 3, //3 is approve
      is_approved: 1,
      approved_by_uid,
      approved_on: dbDateNow(this.app),
    })

    const knex = this.app.service('db/read/test-session-purchases').knex;
    const returnValues = await knex('test_session_purchases as tsp').select({
        'purchase_trans_num': 'tsp.purchase_trans_id',
        'payment_status': 'aps.status',
        'approved_on': 'tsp.approved_on',
        'approved_by': knex.raw('CONCAT(u.first_name, \' \', u.last_name)') 
      }).join('alternative_payment_status as aps', {
        'aps.id': 'tsp.alternative_status'
      }).leftJoin('users as u', {
        'u.id': 'tsp.approved_by_uid'
    }).where('tsp.id', data.test_session_purchase_id)
    return returnValues[0]
  }

  private async contactEqaoPayment(data: any, approved_by_uid: number) {
    await this.updatePurchaseInvoice(data.test_session_purchase_id, {
        alternative_status: 2, // 2 is contact_eqao
    })
    const returnValues = await this.app.service('db/read/test-session-purchases').knex('test_session_purchases')
      .select({
        'payment_status': 'aps.status',
      }).join('alternative_payment_status as aps',
        {'aps.id': 'test_session_purchases.alternative_status'}
      ).leftJoin('users as u', 
        {'u.id': 'test_session_purchases.approved_by_uid'
      }).where({
        'test_session_purchases.id': data.test_session_purchase_id
    });
    return returnValues[0]
  }

  private async refundAlternativePayment(data: any, refunded_by_uid: number) {
    await this.updatePurchaseInvoice(data.test_session_purchase_id, {
        alternative_status: 5, // 5 is refund
        purchase_refund_id: data.purchase_refund_id,
        refuneded_total: data.total_refund,
        is_refunded: 1,
        refunded_on: dbDateNow(this.app),
        refunded_by_uid,
        refunded_student_num: data.refundStudentNum
    })
        
    const knex = this.app.service('db/read/test-session-purchases').knex;
    const returnValues = await knex('test_session_purchases as tsp').select({
        'recovery_value': 'tsp.refuneded_total',
        'is_refunded': 'tsp.is_refunded',
        'refunded_on': 'tsp.refunded_on',
        'refunded_by': knex.raw('CONCAT(u.first_name, \' \', u.last_name)'),
        'purchase_refund_id': 'tsp.purchase_refund_id',
        'refunded_student_num': 'tsp.refunded_student_num'
      }).leftJoin('users as u', {
        'u.id': 'tsp.refunded_by_uid'
    }).where('tsp.id', data.test_session_purchase_id)
    return returnValues[0];
  }

  private async cancelAlternativePayment(data: any, cancelled_by_uid: number) {
    await this.updatePurchaseInvoice(data.test_session_purchase_id, {
      alternative_status: 4, // 4 is canceled
      is_approved: 0,
    })

    await this.updateStudentAttempt({
      payment_status: 4,
      is_revoked: 1,
      revoked_on: dbDateNow(this.app),
      revoked_by_uid: cancelled_by_uid
    }, {
      assigned_ta_id: null,
      used_on: null,
      ts_purchase_id: data.test_session_purchase_id
    })

    const returnValues = await dbRawRead(this.app, [data.test_session_purchase_id], `
      select aps.status as payment_status
        from test_session_purchases tsp
        join alternative_payment_status aps on aps.id = tsp.alternative_status
   left join users u on u.id = tsp.approved_by_uid   
        where tsp.id = ?  
    ;`)
    return returnValues[0]
  }

  private async editAlternativeTransaction(data: any) {
    await this.updatePurchaseInvoice(data.test_session_purchase_id, {
        purchase_trans_id: data.purchase_trans_id,
        purchase_refund_id: data.purchase_refund_id
    })

    return {
      purchase_trans_id: data.purchase_trans_id,
      purchase_refund_id: data.purchase_refund_id
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: any, params?: Params): Promise<Data> {
    if (!params || !params.query) {
      throw new Errors.BadRequest();
    }
    const created_by_uid = await currentUid(this.app, params);

    if(data.Action === AlternativeAction.EDIT_TRANSACTION) {
      return this.editAlternativeTransaction(data)
    }

    if(data.Action in AlternativeAction) {
      this.sendAlternativePaymentEmail(data.test_session_purchase_id, data.Action, data);
    }
    switch(data.Action) {
      case AlternativeAction.APPROVE:
        return this.approveAlternativePayment(data, created_by_uid);
      case AlternativeAction.CONTACT_EQAO:
        return this.contactEqaoPayment(data, created_by_uid);
      case AlternativeAction.REFUND:
        return this.refundAlternativePayment(data, created_by_uid);
      case AlternativeAction.CANCEL:
        return this.cancelAlternativePayment(data, created_by_uid);
      default:
        throw new Errors.BadRequest()
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }
}
