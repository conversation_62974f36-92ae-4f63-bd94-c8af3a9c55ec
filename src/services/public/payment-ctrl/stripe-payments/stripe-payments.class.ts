import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead, dbRawWrite } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';
import { dbDateNow } from '../../../../util/db-dates';

interface Data {}

interface ServiceOptions {}

export enum StripeAction{
  REFUND = 'REFUND'
}

export class StripePayments implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    const stripes = await db<PERSON><PERSON><PERSON><PERSON>(this.app, [], `
      select tsp.id as test_session_purchase_id
           , tsp.id as invoice_number
           , 'Stripe' as purchase_method
           , (CASE WHEN aps.translation_slug IS not null
             THEN aps.translation_slug
             ELSE "lbl_rejected"
             END) payment_status   
           , tsp.stripe_id as stripe_id
           , tsp.purchase_refund_id as purchase_refund_num
           , tsp.ordered_on as purchased_on
           , sc.name as class_name
           , schl.name as schl_name
           , schl.foreign_id as schl_mident
           , sc.group_type as assessment_name
           , tsp.test_window_id as admin_window
           , tsp.num_student_attempts_purchased as num_student_attempts_purchased
           , count(sap.id) as num_stu_attempt_used
           , tsp.purchased_total as purchase_price
           , tsp.refuneded_total as recovery_value
           , tsp.approved_on
           , concat(u.first_name,' ',u.last_name) as approved_by
           , tsp.is_refunded
           , tsp.refunded_on
           , concat(u2.first_name,' ',u2.last_name) as refunded_by
           , tsp.refunded_student_num as refunded_student_count
        from test_session_purchases tsp
        join school_classes sc on sc.group_id = tsp.class_group_id
        join schools schl on schl.group_id = tsp.schl_group_id
   left join student_attempt_purchases sap on sap.ts_purchase_id = tsp.id and sap.is_revoked != 1 and sap.assigned_ta_id is not null and sap.used_on is not null
        join assessment_prices ap on ap.test_window_id = tsp.test_window_id and ap.is_revoked = 0
   left join users u on u.id = tsp.approved_by_uid
   left join users u2 on u2.id = tsp.refunded_by_uid
   left join alternative_payment_status aps on aps.id = tsp.alternative_status
       where tsp.is_revoked != 1
         and tsp.purchase_method_id = 1 -- stripe purchase method id is 1
    group by tsp.id
    order by tsp.id desc  
    ;`)
    return stripes
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  private async sendStripePaymentEmail(testSessionPurchaseId: number, action: StripeAction, data: any) {
    const tsp = (await dbRawRead(this.app, [data.test_session_purchase_id], `
      SELECT 	tsp.schl_group_id,
              tsp.id,
              tsp.test_window_id,
              tsp.stripe_id,
              tsp.purchase_by_uid,
              tsp.purchased_total,
              tsp.refuneded_total,
              tsp.purchase_refund_id,
              sc.name as session_name,
              schl.foreign_id as schl_mident
      FROM test_session_purchases tsp
      JOIN school_classes sc on sc.group_id = tsp.class_group_id
      JOIN schools schl on schl.group_id = sc.schl_group_id 
      WHERE 	tsp.id = ? 
      AND		  tsp.is_revoked != 1
      LIMIT   1;
    `))[0]
    const emailData = {
      schl_group_id: tsp.schl_group_id, 
      purchase_by_uid: tsp.purchase_by_uid,
      invoiceId: tsp.id,
      transaction_number: data.stripe_id || tsp.stripe_id,
      schoolName: data.schoolName,
      assessmentName: data.assessmentName,
      administrationWindow: tsp.test_window_id,
      sessionNames: [tsp.session_name],
      numOfStudents: data.numOfStudents,
      totalCost: tsp.purchased_total,
      totalRefund: data.total_refund,
      refundStudentNum: data.refundStudentNum,
      refundTransactionNumber: data.purchase_refund_id,
      purchase_method_id: 1,
      action
    };
    await this.app.service('public/school-admin/session-purchase').sendInvoiceEmail(emailData);
  }

  private async refundStripePayment(data: any, refunded_by_uid: number) {
    const stripeId = (await dbRawRead(this.app, [data.test_session_purchase_id], `
      SELECT tsp.stripe_id
      FROM test_session_purchases tsp
      WHERE tsp.id = ?
      AND tsp.is_revoked != 1
      LIMIT 1;
    `))[0].stripe_id;
    if(!stripeId) throw new Errors.NotFound('Stripe Id not found');

    const refund = await this.app.service('public/transactions/stripe').completePartialRefund(refunded_by_uid, stripeId, data.total_refund);
    
    // After the stripe refund is pass do the following
    await this.app
      .service('public/payment-ctrl/alternative-payments')
      .updatePurchaseInvoice(data.test_session_purchase_id, {
        refuneded_total: data.total_refund,
        alternative_status: 5, // 5 is refunded
        is_refunded: 1,
        refunded_on: dbDateNow(this.app),
        refunded_by_uid,
        refunded_student_num: data.refundStudentNum,
        purchase_refund_id: refund.id
    })

    const values = await dbRawRead(this.app, [data.test_session_purchase_id], `
      select tsp.refuneded_total as recovery_value
            , tsp.is_refunded
            , tsp.refunded_on
            , concat(u.first_name,' ',u.last_name) as refunded_by
            , tsp.refunded_student_num
            , tsp.purchase_refund_id
        from test_session_purchases tsp
   left join users u on u.id = tsp.refunded_by_uid
        where tsp.id = ?  
    ;`)
    return values[0]
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: any, params?: Params): Promise<Data> {
    if (!params || !params.query) {
      throw new Errors.BadRequest();
    }
    const created_by_uid = await currentUid(this.app, params);

    if(data.Action in StripeAction) {
      this.sendStripePaymentEmail(data.test_session_purchase_id, data.Action, data);
    }

    switch(data.Action) {
      case StripeAction.REFUND:
        return this.refundStripePayment(data, created_by_uid);
      default:
        throw new Errors.BadRequest()
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }
}
