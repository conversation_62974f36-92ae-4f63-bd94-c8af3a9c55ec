import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead } from '../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class StudentAttempts implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    const student_attempts = await dbRawRead(this.app, [], `
    select tsp.id as purchase_order_num
           , pmm.method_type as purchase_method
           , case
              when sap.is_refunded = 1 
                then 'REFUNDED' 
              when aps.status is not null
                then aps.status
              else 'REJECTED'
              end as payment_status 
           , sap.id as purchased_student_attempt_id  
           , tsp.ordered_on as purchased_on 
           , sc.group_type as name_of_assessment
           , tsp.test_window_id as admin_window 
           , ta.test_session_id as test_session_id 
           , sap.uid as student_uid 
           , um.value as student_oen 
           , u.first_name as student_first_initial 
           , u.last_name as student_last_name 
           , sap.used_on as used_on 
           , count(taqr.id) as assessment_accessed 
        from student_attempt_purchases sap
        join test_session_purchases tsp on sap.ts_purchase_id = tsp.id and tsp.is_revoked != 1
        join purchase_method_mapping pmm on pmm.id = tsp.purchase_method_id
        join school_classes sc on sc.group_id = tsp.class_group_id
   left join test_attempts ta on ta.id = sap.assigned_ta_id and ta.is_invalid != 1
        join users u on u.id = sap.uid
        join user_metas um on um.uid = sap.uid and um.key_namespace = 'eqao_sdc' and um.key = 'StudentOEN'
   left join test_attempt_question_responses taqr on taqr.test_attempt_id = ta.id and taqr.is_invalid != 1 and taqr.is_nr != 1
   left join alternative_payment_status aps on aps.id = tsp.alternative_status
       where sap.is_revoked != 1
    group by sap.id
    order by sap.id desc  
    ;`)
    return student_attempts;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }
}
