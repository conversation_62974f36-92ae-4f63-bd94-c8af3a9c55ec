import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../declarations';
import { dbRawReadSingle, dbRawRead } from '../../../util/db-raw';
import { sleep } from '../../../util/timeout';
import logger from '../../../logger';

interface Data {}

interface ServiceOptions {}



export class Ping implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  private static serverUp(): Paginated<Data> {
    return {total: 0, limit: 0, skip: 0, data: [{v:'Apr23b'}]};
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    /*
    const groupRoleRecords = <Paginated<any>> await this.app.service('db/read/marking-pool-by-supr').find({
      query: {
        uid: NaN
      }
    });*/
    // return [];
    return Ping.serverUp();
  }

  private async awaitTest() {
    return 'PING';
  }

  async get (id: Id, params?: Params): Promise<Data> {
    /*
    return {
      id, text: `A new message with ID: ${id}!`
    };
     */
    //return this.app.service('db/read/marking-pool-by-supr').get(1/0);
    return Ping.serverUp();
  }

  async create (data: Data, params?: Params): Promise<Data> {
    /*
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
     */
    /*
    return this.app.service('db/write/users').create({
      id: NaN
    })*/
    return Ping.serverUp();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    // return data;
    return Ping.serverUp();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    //throw new Errors.BadRequest();
    // return data;
    return Ping.serverUp();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    // return { id };
    // if (id == 456789789789745645645665612132789){
    //   await this.sendEmails();
    // }
    return Ping.serverUp();
  }

  async sendEmails(){

    const config  = {
      batchSlug: 'MPT_ADHOC_SESSION_EMAIL_20210507b',
      EN_SUBJ: 1931101,
      EN_MSG: 1931103,
      FR_SUBJ: 1931102,
      FR_MSG: 1931104,
      query: `
        SELECT DISTINCT u.contact_email, ts.invigLang
        FROM user_roles AS ur
        JOIN users AS u
          ON u.id = ur.uid
        left join test_sessions ts
              on ts.test_session_group_id = ur.group_id
        WHERE ur.role_type = 'mpt_booked_applicant'
          AND ur.is_revoked = 0
          AND ur.id > 1579039
        ORDER BY u.contact_email
      `
    }
    const configWaitlist  = {
      batchSlug: 'MPT_ADHOC_SESSION_EMAIL_20210507',
      EN_SUBJ: 1930101,
      EN_MSG: 1930096,
      FR_SUBJ: 1930102,
      FR_MSG: 1930097,
      query: `
        SELECT DISTINCT u.contact_email, ts.invigLang
        FROM user_roles AS ur
        JOIN users AS u
          ON u.id = ur.uid
        left join test_sessions ts
              on ts.test_session_group_id = ur.group_id
        WHERE ur.role_type = 'mpt_waiting_list_applicant'
          AND ur.is_revoked = 0
          AND ur.id > 1579039
        ORDER BY u.contact_email
      `
    }
    const FR_MATCH = 'French Invigilation';

    const whitelabel = 'mathproficiencytest.ca';
    const mailCore = this.app.service('mail/core');
    const translation = this.app.service('public/translation');
    const EMAIL_BUFFER_SIZE = 15; // this is the max send rate on SES

    const emailsReceived = (await dbRawRead(this.app, [config.batchSlug], `select * from log  where slug = ?`)).map(record => record.data);

    const grabLoggedData = async (id:number) =>  (await dbRawReadSingle(this.app, [id], `select data from log where id = ?;`)).data;
    const EN_SUBJ = await grabLoggedData(config.EN_SUBJ);
    const EN_MSG = await grabLoggedData(config.EN_MSG);
    const FR_SUBJ = await grabLoggedData(config.FR_SUBJ);
    const FR_MSG = await grabLoggedData(config.FR_MSG);

    const ttRecords = await dbRawRead(this.app, [], config.query);

    let data = [ // test records
      {
        contact_email: '<EMAIL>',
        invigLang: 'English Invigilation',
      },
      {
        contact_email: '<EMAIL>',
        invigLang: 'French Invigilation',
      },
      {
        contact_email: '<EMAIL>',
        invigLang: 'English Invigilation',
      },
      {
        contact_email: '<EMAIL>',
        invigLang: 'French Invigilation',
      },
    ]

    data = ttRecords;

    let buffer = [];
    while (data.length > 0){
      console.log(data.length, 'remaining')
      buffer = data.splice(0,EMAIL_BUFFER_SIZE);
      let numSent = 0;
      await Promise.all(buffer.map(record => {
        const isEmailed = (emailsReceived.indexOf(record.contact_email) !== -1);
        if (isEmailed){
          console.log('Already emailed')
          return;
        }
        let subject = EN_SUBJ;
        let emailTemplate = EN_MSG;
        if ((''+record.invigLang).trim() === FR_MATCH){
          subject = FR_SUBJ;
          emailTemplate = FR_MSG;
        }
        return mailCore.sendEmail({
          whitelabel,
          emailAddress: record.contact_email,
          subject,
          emailTemplate,
          parameterMapping: {},
        }).then(() => {
          logger.info({created_by_uid: 21, slug:config.batchSlug, data: record.contact_email});
          console.log(record.contact_email);
          numSent ++
        })
      }));
      const requiredDelay = 1200 * (numSent/EMAIL_BUFFER_SIZE);
      if (requiredDelay){
        await sleep(1200);
      }
    }
    console.log('0 remaining')


  }

}
