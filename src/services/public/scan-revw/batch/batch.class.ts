import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead, dbRawWrite } from '../../../../util/db-raw';
import { getSysConstNumeric } from '../../../../util/sys-const-numeric';
import { currentUid } from '../../../../util/uid';
import { generateS3DownloadUrl } from '../../../upload/upload.listener';
import _, { reject } from 'lodash';
import { dbDateNow } from '../../../../util/db-dates';
import { boolAsNum } from '../../../../util/param-sanitization';
import { SQL_SCAN_BATCH_RESP_REVIEW } from '../../../../sql/scoring';
import { isABED } from '../../../../util/whiteLabelParser';

const synchronousClaimBufferFactor = 20;

interface Data {}

interface ServiceOptions {}

export class Batch implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params?.query){
      const uid = await currentUid(this.app, params);
      const {mw_id, is_editing} = params.query;
      if (is_editing === undefined) { throw new Errors.BadRequest('MISSING_EDITING_FLAG') }
      const activeBatch = await this.getActiveBatch(mw_id, uid, is_editing);
      if (!activeBatch){
        return []
      }
      const imageRecords = await this.getBatchScans(activeBatch.id, is_editing==1)
      return [
        {
          batchId: activeBatch.id,
          scans: imageRecords,
        }
      ]
    }
    throw new Error()
  }

  async getBatchScans(batchId:number, isEditing:boolean){
    type ImageRecord = {
      tasr_id: number, 
      taqr_id: number, 
      srbr_id: number,
      scan: string,
      full_scan: string,
      crop_meta: string,
      is_discarded: number,
      isCropped?: boolean,
      uploaded_on: string,
      stu_gov_id?: string,
      url?: string,
      urlUncropped?: string,
      uploaded_by_name?: string,
      uploaded_by_email?: string,
      ts_name_custom?: string,
      assignment_slug?: string,
      sc_name?: string,
      sc_access_code?: string,
      schl_name?: string,
      cropInfo?: {
        crop: {
          top_ratio: number,
          right_ratio: number,
          bottom_ratio: number,
          left_ratio: number,
        },
        rotation: number
      },
    }

    const whiteLabel = this.app.get('whiteLabel');
    let studentIdentificationNumberKeynamespaces: string[];
    let studentIdentificationNumberKey: string;
    if (isABED(whiteLabel)) {
      studentIdentificationNumberKey = "StudentIdentificationNumber";
      studentIdentificationNumberKeynamespaces = ["abed_sdc", "abed_course"]
    } else {
      studentIdentificationNumberKey = "StudentOEN";
      studentIdentificationNumberKeynamespaces = ["eqao_sdc"]
    }

    const imageRecords:ImageRecord[] = await dbRawRead(this.app, {batchId, studentIdentificationNumberKey, studentIdentificationNumberKeynamespaces}, `
      select srbr.id srbr_id
           , tasr.scan
           , tasr.id tasr_id
           , tasr.full_scan
           , tasr.crop_meta
           , srbr.taqr_id
           , tasr.is_discarded
           , tasr.uploaded_on 
           , tqsi.resp_sheet_config
           , um.value stu_gov_id
           , concat(u_uploader.first_name, " ", u_uploader.last_name) uploaded_by_name
           , u_uploader.contact_email uploaded_by_email
           , ts.name_custom ts_name_custom
           , scts.slug assignment_slug
           , sc.name sc_name
           , sc.access_code sc_access_code
           , schl.name schl_name
      from scan_review_batch_responses srbr 
      join scan_review_batches srb 
        on srb.id = srbr.srb_id 
      join scan_review_taqr_pool srtp 
        on srtp.taqr_id = srbr.taqr_id 
        and srtp.mw_id = srb.mw_id 
      join test_attempt_scan_responses tasr 
        on tasr.id = srtp.cached_tasr_id 
        and tasr.is_discarded = 0
      left join test_attempt_question_responses taqr on tasr.taqr_id = taqr.id
      left join test_attempts ta on ta.id = taqr.test_attempt_id
      left join test_window_td_alloc_rules twtdar on ta.twtdar_id = twtdar.id
      left join test_question_scoring_info tqsi 
        on tqsi.item_id = taqr.test_question_id
        and tqsi.lang = twtdar.lang
        and tqsi.is_revoked = 0
      left join user_metas um
        on um.uid = ta.uid
        and um.key = :studentIdentificationNumberKey
        and um.key_namespace in (:studentIdentificationNumberKeynamespaces)
      left join users u_uploader
        on u_uploader.id = tasr.uploaded_by_uid
      left join test_sessions ts
        on ts.id = ta.test_session_id
      left join school_class_test_sessions scts
        on scts.test_session_id = ts.id
      left join school_classes sc
       on sc.id = scts.school_class_id
      left join schools schl
        on schl.group_id = sc.schl_group_id
      where srbr.srb_id = :batchId
    `);
    for(const record of imageRecords) {
      if (record.scan){
        record.url = generateS3DownloadUrl(record.scan, 60*30);
      }
      if (record.full_scan){
        record.urlUncropped = generateS3DownloadUrl(record.full_scan, 60*30);
      }
      if (record.crop_meta){
        record.isCropped = true
      }
      record.cropInfo = !!record.crop_meta ? JSON.parse(record.crop_meta) :  {
        crop: {
          top_ratio: 0, //1/5,
          right_ratio: 0,
          bottom_ratio: 0,
          left_ratio: 0,
        },
        rotation: 0
      }
    }
    return imageRecords;
  }

  async getActiveBatch(mw_id:number, uid:number, is_editing:number){
    const activeBatches:{id:number, created_on: string}[] = await dbRawRead(this.app, [mw_id, uid, is_editing], `
      select id, created_on 
      from scan_review_batches
      where mw_id = ?
        and uid = ? 
        and is_editing = ?
        and is_complete = 0
        and is_revoked = 0
    `);
    if (activeBatches.length){
      return activeBatches[0]
    }
    return;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Error()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (params){
      const uid = await currentUid(this.app, params);
      const {mw_id, isEditing} = <any> data;
      if (isEditing === undefined) { throw new Errors.BadRequest('MISSING_EDITING_FLAG') }
      const is_editing = boolAsNum(isEditing);
      const prevBatch = await this.getActiveBatch(mw_id, uid, is_editing);
      if (prevBatch){
        throw new Errors.GeneralError('BATCH_ALREADY_ASSIGNED')
      }
      const SCAN_BATCH_SIZE = await getSysConstNumeric(this.app, 'SCAN_BATCH_SIZE', true) as number;
      const SCAN_CHECK_COUNT = isEditing ?  1 : await getSysConstNumeric(this.app, 'SCAN_CHECK_COUNT', true); // only have people edit the scans once
      const claimPoolSize = SCAN_BATCH_SIZE*synchronousClaimBufferFactor;
      // find scans available
      const nextScans:{taqr_id:number}[] = await dbRawRead(this.app, [uid, SCAN_CHECK_COUNT, claimPoolSize], SQL_SCAN_BATCH_RESP_REVIEW(mw_id, isEditing));
      const sortedScans = isEditing ? _.orderBy(nextScans, 'tasr_id') : _.shuffle(nextScans); // editors want responses that were uploaded around the same time to be closer together, reviewers need to be kep alert by having the claim pool shuffled
      const indexShift = Math.floor(Math.max(0, sortedScans.length-SCAN_BATCH_SIZE) * Math.random());
      const selectedScans = sortedScans.slice(indexShift, indexShift+SCAN_BATCH_SIZE);
      if (!selectedScans.length){
        throw new Errors.GeneralError('NO_SCANS_REMAINING')
      }
      // create batch
      const batch = await this.app.service('db/write/scan-review-batches').create({
        mw_id,
        uid,
        is_editing,
      });
      const batchResponses:{srbr_id:number, taqr_id:number}[] = [];
      await Promise.all(
        selectedScans.map( async scan => {
          const response = await this.app.service('db/write/scan-review-batch-responses').create({
            srb_id: batch.id,
            taqr_id: scan.taqr_id,
            uid,
          });
          batchResponses.push({
            srbr_id: response.id,
            taqr_id: scan.taqr_id,
          });
        })
      )
      return {
        batchId: batch.id,
        batchResponses,
      }
    }
    throw new Error();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if (!params){
      throw new Errors.BadRequest();
    }
    const uid = await currentUid(this.app, params);
    const batchId = id;
    const {isEditing} = <any> data;
    const responseReviews:{taqr_id:number, srbr_id:number, isFlagged:boolean}[] = (<any> data).responseReviews;
    // revoke previous reviews if editing 
    if (isEditing){
      const taqr_ids = responseReviews.map( r => r.taqr_id);
      const recordsToRevoke = await dbRawRead(this.app, [taqr_ids], `
        select srbr.id
        from scan_review_batch_responses srbr 
        join scan_review_batches srb 
          on srb.id = srbr.srb_id 
          and srb.is_editing = 0
        where srbr.taqr_id IN (?)
          and srbr.is_revoked = 0
      `);
      const recordsToRevokeIds = recordsToRevoke.map(r => r.id);
      await dbRawWrite(this.app, [uid, recordsToRevokeIds], `
        UPDATE mpt_dev.scan_review_batch_responses
        SET is_revoked=1
          , revoked_reason = 'CROP'
          , revoked_by_uid = ?
          , revoked_on = now()
        where id in (?)
      `)
      const recordsToCompleteIds = responseReviews.map(r => r.srbr_id);
      await dbRawWrite(this.app, [recordsToRevokeIds], `
        UPDATE mpt_dev.scan_review_batch_responses
        SET is_complete = 1
        where id in (?)
      `)
    }
    else {
      // track flags
      const cropsToRevokeTaqrIds:number[] = [];
      await Promise.all(
        responseReviews.map( async response => {
          await this.app.service('db/write/scan-review-batch-responses').patch(response.srbr_id, {
            is_flagged: response.isFlagged ? 1 : 0,
            is_complete: 1,
          });
          if (response.isFlagged){
            cropsToRevokeTaqrIds.push(response.taqr_id)
          }
        })
      )
      const cropsToRevoke = cropsToRevokeTaqrIds.length ? await dbRawRead(this.app, [cropsToRevokeTaqrIds], `
        select srbr.id
        from scan_review_batch_responses srbr 
        join scan_review_batches srb 
          on srb.id = srbr.srb_id 
          and srb.is_editing = 1
        where srbr.taqr_id IN (?)
          and srbr.is_revoked = 0
      `) : [];
      const cropsToRevokeIds = cropsToRevoke.map(r => r.id);
      if (cropsToRevokeIds.length){
        await dbRawWrite(this.app, [uid, cropsToRevokeIds], `
          UPDATE mpt_dev.scan_review_batch_responses
          SET is_revoked=1
            , revoked_reason = 'CROP REJ'
            , revoked_by_uid = ?
            , revoked_on = now()
          where id in (?)
        `)
      }
    }
    await this.app.service('db/write/scan-review-batches').patch(batchId, {
      is_complete: 1,
      completed_on: dbDateNow(this.app),
    });
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
