import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { HEADER_SEB_REQ } from '../../../../constants/headers';
import { dbRawRead } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';
import { getSysConstNumeric } from '../../../../util/sys-const-numeric';
const IPCIDR = require("ip-cidr");

interface Data {}

interface ServiceOptions {}

export interface ISessionSecurityInfo {
  test_session_id: Id;
  schl_group_id: Id,
  is_private_school: boolean;
  is_kiosk_approved: boolean;
  is_softlock_enabled: boolean;
  is_softlock_enabled_pj: boolean;
  is_softlock_enabled_g9: boolean;
  is_softlock_enabled_g10: boolean;
  group_type: string
}

interface ISecureValidation {
  isSecure: boolean;
  isDisabled: boolean;
  isExempt: boolean;
}

export class LockDown implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  /**
   * Validate whether student has access to the test based on:
   *  - his IP address and whether it is registered or exempt from registration
   *  - his exemption status from security based on different factors
   *  - whether he has logged in through an approved secure environment
   */
  public async validateSecurity(params: Params, uid: number, test_session_id: Id, is_forced = true): Promise<ISecureValidation> {
    const sessionInfo = await this.getSessionInfo(test_session_id)

    // 1. Validate student's IP. Will throw an error if validation fails
    if(is_forced) {
      await this.validateIp(sessionInfo, params.ip)
    }

    // 2. Validate all school / student exemptions
    const isExempt = await this.validateSecurityExempt(uid, sessionInfo);
    if(isExempt) {
      return { isSecure: true, isDisabled: false, isExempt: true };
    }
    
    // 3. Validate whether student entered through a secure browser
    const isSecureEnvironment = await this.validateSecureEnvironment(params, sessionInfo)
    if(is_forced && !isSecureEnvironment.isSecure) { // Throw an error if security is forced
      throw new Errors.Forbidden('NO_SECURITY')
    }

    return {isExempt: false, ...isSecureEnvironment}
  }

  public async validateSecurityExempt(uid:number, sessionInfo: ISessionSecurityInfo) {
    // EQAO PJ non private schools are exempt
    if(['EQAO_G3', 'EQAO_G6'].includes(sessionInfo.group_type) && !sessionInfo.is_private_school) {
      return true;
    }
    
    // School Security Exemptions
    const schoolExemptFromSecurity = await this.app.service('public/student/session').allowInsecureSchool(<number>sessionInfo.test_session_id); 
    if (schoolExemptFromSecurity) {
      return true;
    }

    // Individual student accomodations
    const isExemptAccomodation = await this.app.service('public/student/session').verifyAssistedTechAccomodation(uid, sessionInfo.group_type)
    if(isExemptAccomodation) {
      return true;
    }

    return false
  }

  public async validateSecureEnvironment(params: Params, sessionInfo: ISessionSecurityInfo) {
    // SafeExamBrowser (SEB) 2.x or 3.x
    // Check for SEB login attempt using either SafeExamBrowser 2.x header or 3.x in the user-agent.
    const is_seb_disabled = await getSysConstNumeric(this.app, 'IS_SEB_DISABLED').catch(() => false)
    if (params.headers?.[HEADER_SEB_REQ] || params.headers?.['user-agent']?.includes('SEB/3')) {
      return is_seb_disabled
        ? { isSecure: false, isDisabled: true }
        : { isSecure: true, isDisabled: false };
    }

    // KIOSK
    // KIOSK is approved for:
    // - if not disabled by the system
    // - all non private schools
    // - private schools if they are is_kiosk_approved
    const is_kiosk_disabled = await getSysConstNumeric(this.app, 'IS_KIOSK_DISABLED').catch(() => false)
    const is_kiosk_allowed = !sessionInfo.is_private_school || (sessionInfo.is_private_school && sessionInfo.is_kiosk_approved)
    if(params.query && params.query['KIOSK_PASSWORD'] && is_kiosk_allowed) {
      return is_kiosk_disabled
        ? { isSecure: false, isDisabled: true } 
        : { isSecure: true, isDisabled: false };
    }

    // Respondus Lockdown Browser
    if(params?.query?.rldbarv) {
      const validationResult = this.app.service('auth/lock-down-browser').validateChallenge(params)
      if(validationResult.result) {
        return { isSecure: true, isDisabled: false };
      }
    }

    return { isSecure: false, isDisabled: false };
  }

  private async validateIp(sessionInfo: ISessionSecurityInfo, studentIp: string) {
    if (this.app.get('disableIpRegistration')) {
      return true;
    }

    // Non-private schools are exempt from IP validation
    if(!sessionInfo.is_private_school) {
      return true;
    }

    const registeredIps = await dbRawRead(this.app, [sessionInfo.schl_group_id], `
      SELECT id,ip_address as ip
      FROM mpt_dev.school_ip_addresses
      WHERE schl_group_id = ? and is_revoked != 1;
      ;`);

    const matchedCidrs = registeredIps.filter(item => {
      const ip = new IPCIDR(item.ip)
      if(ip.isValid() && ip.contains(studentIp)){
        return true;
      }
    });

    if(matchedCidrs.length > 0) {
      return true;
    }

    throw new Errors.Forbidden('UNREGISTERED_IP_ADDR')
  }

  private async getSessionInfo(test_session_id: Id) {
    const sessionInfo = await dbRawRead(this.app, [test_session_id], `
      SELECT 
        scts.test_session_id,
        sc.schl_group_id schl_group_id, 
        sch.is_private as is_private_school, 
        sch.is_kiosk_approved, 
        sch.is_softlock_enabled, 
        sch.is_softlock_enabled_pj, 
        sch.is_softlock_enabled_g9,
        sch.is_softlock_enabled_g10,
        sc.group_type
      FROM mpt_dev.school_class_test_sessions scts
      JOIN mpt_dev.school_classes sc
        ON sc.id = scts.school_class_id
      JOIN mpt_dev.schools sch on sch.group_id = sc.schl_group_id
      WHERE scts.test_session_id = ?;
      ;`);

    return <ISessionSecurityInfo>sessionInfo[0];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.NotImplemented()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: any, params?: Params): Promise<any> {
    if(!params || !data?.test_session_id) {
      throw new Errors.BadRequest('ERR_MISSING_PARAMS')
    }
    const { test_session_id, school_class_id } = data;
    const uid = await currentUid(this.app, params)

    const validationResult = await this.validateSecurity(params, uid, data?.test_session_id, false)
    const ldbSecurityConfig = await this.app.service('auth/lock-down-browser').getLdbLaunchConfiguration(school_class_id)
    return {
      ...validationResult,
      ldbConfiguration: ldbSecurityConfig // TODO
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented()
  }
}
function getLdbLaunchConfiguration(arg0: number) {
  throw new Error('Function not implemented.');
}

