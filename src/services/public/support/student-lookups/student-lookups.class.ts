import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead, dbRawReadSingle } from '../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}
enum DATABASE {
  DEV = 'mpt_dev',
  PASI = 'pasi_data'
}

export class StudentLookups implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<any> {

    if(!params || !params.query) throw new Errors.BadRequest('MISSING_PARAMS');

    const { OEN, questionType, questionId, ASN, is_abed, database } = params.query
    if(is_abed){
      if(!ASN) throw new Errors.BadRequest('MISSING_ASN');
      if(database == DATABASE.DEV){
        const studentUids = await dbRawRead(this.app, {asn: ASN}, `
          select uid
          from user_metas um
          where um.key = 'StudentIdentificationNumber'
            and um.value = :asn
        `)

        const uids = studentUids.map(res=> res.uid);
        if(uids.length == 0){
          return {
            students: [],
            schools: []
          }
        }
        let rows = await dbRawRead(this.app, {uids: uids}, `
        select um.uid
          , um.value dob
          , ${ASN} as asn
          , u.first_name
          , u.middle_name
          , u.last_name
          , u.is_PASI_student
        from user_metas um
        join users u
          on um.uid = u.id
        where  um.key = 'DateOfBirth'
          and um.uid in (:uids)
        ;`);

        const schools = await dbRawRead(this.app, {uids: uids}, `
          select ur.uid
          , s.name
          , s.foreign_id 
          , s.group_id 
          from user_roles ur 
          join schools s
            on s.group_id = ur.group_id 
            and ur.is_revoked = 0
            and ur.is_removed = 0
          where ur.uid in (:uids)
          `)
        return {
          students: rows,
          schools
        };
      }
      if(database == DATABASE.PASI){
        let rows = [];
        let students = await dbRawRead(this.app, {asn: ASN}, `
        select 
        s.StudentNames student_name
          , s.IdentificationRecord 
          , s.StateProvinceId asn 
          , s.uid
          , 1 as is_PASI_student
        from pasi_data.students s
        where s.StateProvinceId = :asn
        ;`);
        for (let student of students){
          
          const studentName = student.student_name.Name[0]
          const dob = student.IdentificationRecord.BirthDate.split('T')[0]
          
          rows.push({
            first_name: studentName.FirstName,
            last_name: studentName.LastName,
            middle_name: studentName.MiddleName,
            asn : student.asn,
            uid : student.uid,
            dob
          })
        }
        // To do, also check diploma pasi schools
        const schools = await dbRawRead(this.app, {asn: ASN}, `
          select s.uid
          , ps.Names as name
          , ps.OrganizationCode as foreign_id
          from pasi_data.students s
          join pasi_data.student_school_enrolments sse
          on s.StateProvinceId = sse.StateProvinceId
          join pasi_data.pasi_schools ps 
          on sse.SchoolCode = ps.OrganizationCode 
          where s.StateProvinceId = :asn
          `)
        return {
          students: rows,
          schools
        };
      }
      throw new Errors.BadRequest('INCORRECT_DATABASE');
    }
    else {
    
    if(!OEN) throw new Errors.BadRequest('MISSING_OEN');
    if(!questionId && !questionType) throw new Errors.BadRequest('MISSING PARAMS');
    
    let sqlQuery = `
    select ta.id attempt_id
      , taqr.test_question_id
      , taqr.id taqr_id
      , tq.question_label
      , taqr.response_raw
      , taqr.updated_on
      , taqr.score
      , taqr.weight
      , ta.closed_on
    from test_attempts ta 
    join user_metas um on um.uid = ta.uid and um.key = 'StudentOEN' and ta.is_invalid != 1 
    and um.value = ${OEN}
    join test_attempt_question_responses taqr 
      on ta.id = taqr.test_attempt_id 
      and taqr.is_invalid = 0`;

    if(questionId){
      sqlQuery += ` 
      and taqr.test_question_id = ${questionId}`;
    }
    
    if(questionType){
      sqlQuery += ` 
      and taqr.response_raw like '%${questionType}%'`          
    }

    sqlQuery += ` 
    join test_questions tq on tq.id = taqr.test_question_id;`;

    let rows = await dbRawRead(this.app, [], sqlQuery);

    return rows;
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {

    if(!params || !params.query) throw new Errors.BadRequest('MISSING_PARAMS');
    
    const { OEN } = params.query    
    if(!OEN) throw new Errors.BadRequest('MISSING_OEN');

    let valid = await this.app.service('private/schools/student/validate').C_OEN_DIGIT_CHECK(OEN, '');

    return valid;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
   throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
   throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
