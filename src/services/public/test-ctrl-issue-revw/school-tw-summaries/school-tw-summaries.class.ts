import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { BadRequest } from '@feathersjs/errors';
import { currentUid } from '../../../../util/uid';
import { Errors } from '../../../../errors/general';
import { dbRawRead, dbRawWrite } from '../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class SchoolTwSummaries implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    const test_window_id = params?.query?.test_window_id;
    if (test_window_id){
      return this.app.service('public/school-admin/tw-statement').getAllSchools(test_window_id);
    }
    throw new BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    const schl_group_id = id;
    const test_window_id = params?.query?.test_window_id;
    if (schl_group_id && test_window_id){
      return this.app.service('public/school-admin/tw-statement').getTwStatement({test_window_id, schl_group_id: +schl_group_id});
    }
    throw new BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async updateSchoolRecords(isConfirmed:boolean, schl_group_id: NullableId, params?: Params){
    if (!params?.query){
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    const test_window_id = params?.query?.test_window_id;
    const uid = await currentUid(this.app, params);
    // todo: limit to records that have not been updated since last pull (grab a now() with the initial pull to make this easy)
    const records = await dbRawRead(this.app, {schl_group_id, test_window_id}, `
      select id, is_confirmed, confirmed_on, confirmed_by_uid 
      from school_tw_student_signoffs stss
      where schl_group_id = :schl_group_id
        and test_window_id = :test_window_id
      and is_revoked  = 0 
    `);
    if (records.length){
      const ids = records.map(r => r.id);
      if (isConfirmed){
        await dbRawWrite(this.app, {ids, uid}, `
          UPDATE school_tw_student_signoffs
            SET is_confirmed=1
              , confirmed_on=now()
              , confirmed_by_uid = :uid
            WHERE id in (:ids)
        ;`)
      }
      else{
        await dbRawWrite(this.app, {ids}, `
          UPDATE school_tw_student_signoffs
            SET is_confirmed=0
            WHERE id in (:ids)
        ;`)
      }
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    await this.updateSchoolRecords(true, id /*schl_group_id*/, params)
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    await this.updateSchoolRecords(false, id /*schl_group_id*/, params)
    return { id };
  }
}
