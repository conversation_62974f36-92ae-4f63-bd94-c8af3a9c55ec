// Initializes the `public/test-ctrl-issue-revw/school-tw-summaries` service on path `/public/test-ctrl-issue-revw/school-tw-summaries`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { SchoolTwSummaries } from './school-tw-summaries.class';
import hooks from './school-tw-summaries.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/test-ctrl-issue-revw/school-tw-summaries': SchoolTwSummaries & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-ctrl-issue-revw/school-tw-summaries', new SchoolTwSummaries(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-ctrl-issue-revw/school-tw-summaries');

  service.hooks(hooks);
}
