import { Id, NullableId, Pa<PERSON>ated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { dbRawRead } from '../../../../util/db-raw';
import { Errors } from '../../../../errors/general';
import AWS from 'aws-sdk';
import { GetLogEventsRequest, GetQueryResultsRequest, GetQueryResultsResponse, ResultField, StartQueryRequest, StartQueryResponse } from 'aws-sdk/clients/cloudwatchlogs';
import { AWSCredentials, CLOUDWATCH_LOGS_ABED, CLOUDWATCH_LOGS_CAEC, getCloudwatchLogs, parseLogs } from '../../../../util/cloudwatch-logs';
import { camelify, snakeify } from '../../../../util/caseify';
import { dbDateNow } from '../../../../util/db-dates';
import { currentUid } from '../../../../util/uid';

interface Data {}

interface ServiceOptions {}

export class SessionQuestionLogs implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<any> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    if(!params || !params.query || !params.query.duration || !params.query.date || !params.query.question_ids || !params.query.test_attempt_id) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    // get info from tw to see if CAEC
    const tw_info = await this.app.service('db/read/test-windows').get(params.query?.test_window_id);

    let isCAEC = false;

    if (tw_info && tw_info.is_test_centre == 1) {
      isCAEC = true;
    }

    const uid = id;
    const questionIds: number[] = params.query.question_ids;
    // const endTime = new Date();
    let rangeMinutes = params.query.duration;
    let date = params.query.date;
    let MINUTE_LIMIT = 60*48; // 48 hours

    rangeMinutes = rangeMinutes > MINUTE_LIMIT ? MINUTE_LIMIT : rangeMinutes;

    const startTime = new Date(date);
    const endTime = new Date(startTime.getTime() + (rangeMinutes * 60 * 1000));
    const parsedQuestionIds = this.parseQuestionIds(questionIds);
    const testAttemptId = params.query.test_attempt_id;

    const endpoint = isCAEC ? 'public/test-taker/invigilation/question-response' : 'public/student/session-question';

    const query = `
      fields uid, @timestamp, method, originalUrl, @message, code, message, requestBody.test_question_id as testQuestionId, requestBody.test_attempt_id as testAttemptId
      | filter originalUrl like '${endpoint}'
      | filter uid like '${uid}'
      | filter requestBody.test_question_id	in [${parsedQuestionIds}]
      | filter requestBody.test_attempt_id like '${testAttemptId}'
    `

    const awsCredentials: AWSCredentials = {
      secretAccessKey: '8hQTXd79J8NZxePMTqCuF7oNh1c32olF+i8joqYo',
      accessKeyId: '********************'
    }
    const CLOUDWATCH_LOGS = isCAEC ? CLOUDWATCH_LOGS_CAEC : CLOUDWATCH_LOGS_ABED;
    const rawLogs = await getCloudwatchLogs(CLOUDWATCH_LOGS, query, startTime, endTime, awsCredentials);
    const parsedLogs = parseLogs(rawLogs);
    return parsedLogs || [];
  }

  parseQuestionIds(questionIds: number[]) {
    // if from CAEC sent an object, not array
    if (!Array.isArray(questionIds)) {
      questionIds = Object.values(questionIds);
    }
    const arrayString = questionIds.map((id) => {
      return `'${id}'`
    });
    const resultString = arrayString.join(', ');
    return resultString;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: any, params?: Params): Promise<Data> {
    if (!params) {
      throw new Errors.BadRequest('MISSING_PARAMS')
    }
    if(!data.test_attempt_id || !data.test_question_id || !data.response_raw) {
      throw new Errors.BadRequest('MISSING_DATA')
    }

    const uid = await currentUid(this.app, params);
    const {testAttemptId, testQuestionId, responseRaw} = camelify(data);

    await this.invalidateTaqrs(testAttemptId, testQuestionId, uid)

    const taqrRecord = await this.app.service('db/write/test-attempt-question-responses').create({
      test_attempt_id: testAttemptId,
      test_question_id: testQuestionId,
      response_raw: responseRaw,
    })
    return taqrRecord.id;
  }

  async invalidateTaqrs(test_attempt_id: number, test_question_id: number, updated_by_uid: number) {
    const taqrs = await dbRawRead(this.app, {test_attempt_id, test_question_id}, `
      select * from test_attempt_question_responses taqr
      where test_question_id = :test_question_id
        and test_attempt_id = :test_attempt_id
        and is_invalid = 0;
    `)

    return await Promise.all(taqrs.map(async (taqr) => {
      return await this.app.service('db/write/test-attempt-question-responses').patch(taqr.id, {
        is_invalid: 1,
        invalidated_on: dbDateNow(this.app),
        invalidation_note: 'Invalidating for restore',
        updated_by_uid
      })
    }));
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }
}
