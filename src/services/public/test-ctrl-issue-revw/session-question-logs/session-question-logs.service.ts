// Initializes the `/public/test-ctrl-issue-revw/session-question-logs` service on path `/public/test-ctrl-issue-revw/session-question-logs`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { SessionQuestionLogs } from './session-question-logs.class';
import hooks from './session-question-logs.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/test-ctrl-issue-revw/session-question-logs': SessionQuestionLogs & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-ctrl-issue-revw/session-question-logs', new SessionQuestionLogs(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-ctrl-issue-revw/session-question-logs');

  service.hooks(hooks);
}
