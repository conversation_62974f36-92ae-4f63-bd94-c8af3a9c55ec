import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import logger from '../../../../logger';
import { dbDateNow } from '../../../../util/db-dates';
import { dbRawRead } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';

interface Data {}

interface ServiceOptions {}

export class UnsubReq implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if(!params || !params.query) throw new Errors.BadRequest('MISSING_PARAMS');

    const { test_window_id, showAllRecord } = params.query;

    const unsubmissionRecord = await dbRawRead(this.app, [test_window_id], 
      ` select tau.*
              ,um.value as studentASN
		          ,sc.access_code as classCode
		          ,concat(u.first_name, ' ', u.last_name) as teacherName 
              ,concat(u2.first_name, ' ', u2.last_name) as schoolAdminName
              ,s.name as schoolName
              ,s.foreign_id as schoolMident
              ,u.contact_email as teacherEmail
              , case when tau.is_approval_req = 1 then 'No'
                     else 'Yes'
                end as is_auto_approved
          from test_attempt_unsubmissions tau
        join test_windows tw on tw.id = tau.test_window_id and tw.id = ?
        join user_metas um on tau.student_uid = um.uid and um.key_namespace = 'abed_course' and um.key = "StudentIdentificationNumber" 
        join school_class_test_sessions scts on tau.test_session_id = scts.test_session_id
        join school_classes sc on scts.school_class_id = sc.id
        join schools s on s.group_id = sc.schl_group_id
        join user_roles ur on ur.group_id = sc.group_id and ur.role_type = 'schl_teacher' and ur.is_revoked = 0
        join users u on u.id = ur.uid
        join user_roles ur2 on ur2.group_id = sc.schl_group_id and ur2.role_type = 'schl_admin' and ur2.is_revoked = 0
        join users u2 on u2.id = ur2.uid
        ${showAllRecord === 'true' ? '' : `where tau.is_approved = 0
          and tau.is_revoked = 0
          and tau.is_pending = 1`}
        group by tau.id
        order by tau.id desc
      ;`);

    return unsubmissionRecord;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if(!id || !data || !params) throw new Errors.BadRequest();

    const dataRecord = <any>data;
    const currentUID = await currentUid(this.app, params);
    let isPJ = false, isG9 = false, isSample = false;
    let attemptSectionIndex = 0;
    let questioneersRecord;
    let questioneersTestAttemptId;
    let questioneersTestAttempSubsessiontId;
    let unsubmitTestAttemptSubSessionIds:any[] = [];
    let unsubmitTestAttemptActiveSubSessionId:any

    const subSessionIndex = dataRecord.rollback_subsession_index;
    attemptSectionIndex = subSessionIndex;

    let studentAttemptsInfo:any[] = JSON.parse(dataRecord.test_attempt_info)
    const testSessionSlug = studentAttemptsInfo[0].scts_slug
    isPJ = testSessionSlug.includes('PRIMARY')||testSessionSlug.includes('JUNIOR')
    isG9 = testSessionSlug.includes('G9')
    isSample = testSessionSlug.includes('SAMPLE');

    if(!isSample){
      // Might be needed in the future if session q is reintroduced into ABED
      // questioneersRecord = studentAttemptsInfo.find(record => record.tsss_slug === 'session_q')
      // questioneersTestAttemptId = questioneersRecord.ta_id
      // questioneersTestAttempSubsessiontId =  questioneersRecord.tass_id
    }

    if(isPJ){
      if(isSample && attemptSectionIndex > 1){
        attemptSectionIndex -= 2
      }
      if(!isSample && attemptSectionIndex > 3){
        attemptSectionIndex -= 4
      }
    }

    const unsubmitTestAttemptId = studentAttemptsInfo.find(record => +record.tsss_order === +subSessionIndex).ta_id
    unsubmitTestAttemptActiveSubSessionId = studentAttemptsInfo.find(record => +record.tsss_order === +subSessionIndex).ta_active_sub_session_id
    studentAttemptsInfo.forEach (sai =>{
      if(sai.ta_id == unsubmitTestAttemptId && sai.tsss_order >= subSessionIndex){
        unsubmitTestAttemptSubSessionIds.push(sai.tass_id)
      }
    }) 
    
    if(!isSample){
      await this.app.service('public/educator/student-test-attempt').patchAttempt(questioneersTestAttemptId, 0)
      await this.app.service('public/educator/student-test-attempt').patchTestAttemptSubSession(questioneersTestAttempSubsessiontId)
    }

    await this.app.service('public/educator/student-test-attempt').patchAttempt(unsubmitTestAttemptId, attemptSectionIndex)
    await Promise.all(unsubmitTestAttemptSubSessionIds.map( async utassid =>{
      await this.app.service('public/educator/student-test-attempt').patchTestAttemptSubSession(utassid)
    }))

    // Update test_attempt_unsubmissions table
    const unsubmittedRecord = await this.app.service('db/write/test-attempt-unsubmissions').patch(id, {
      is_pending: 0,
      is_approved: 1,
      approved_on: dbDateNow(this.app),
      approved_by_uid : currentUID,
    })

    const loggerData = {
      testSessionId: dataRecord.test_session_id,
      studentUID: dataRecord.student_uid,
      rollBackSubSession: subSessionIndex,
      studentAttemptsInfo: dataRecord.test_attempt_info,
      reasonId: dataRecord.unsubmit_reason_id,
      reasonText: dataRecord.unsubmit_reason_text,
    }
    const slug = 'ISSUE_REVIEWER_UNSUBMIT_SUBSESSION_APPROVED'

    if(unsubmittedRecord && unsubmittedRecord.id) {
      this.createUnsubmissionLogger(loggerData, currentUID, slug);
      return [unsubmittedRecord];
    }
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if(!id || !data || !params) throw new Errors.BadRequest();

    const dataRecord = <any>data;
    const currentUID = await currentUid(this.app, params);
    const subSessionIndex = dataRecord.rollback_subsession_index;

    // Update test_attempt_unsubmissions table
    const unsubmittedRecord = await this.app.service('db/write/test-attempt-unsubmissions').patch(id, {
      is_pending: 0,
      is_revoked: 1,
      revoked_on: dbDateNow(this.app),
      revoked_by_uid : currentUID,
    })
    
    const loggerData = {
      testSessionId: dataRecord.test_session_id,
      studentId: dataRecord.student_uid,
      rollBackSubSession: subSessionIndex,
      studentAttemptsInfo: dataRecord.test_attempt_info,
      reasonId: dataRecord.unsubmit_reason_id,
      reasonText: dataRecord.unsubmit_reason_text,
    }
    const slug = 'ISSUE_REVIEWER_UNSUBMIT_SUBSESSION_REJECTED';

    if(unsubmittedRecord && unsubmittedRecord.id) {
      this.createUnsubmissionLogger(loggerData, currentUID, slug);
      return [unsubmittedRecord];
    }
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.BadRequest();
  }

  createUnsubmissionLogger(dataRecord:any, uid:number, slug:string) {
    let log_entry = {
      created_by_uid: uid,
      slug: slug,
      data: JSON.stringify(dataRecord)
    }
    logger.info(log_entry);
  }
}
