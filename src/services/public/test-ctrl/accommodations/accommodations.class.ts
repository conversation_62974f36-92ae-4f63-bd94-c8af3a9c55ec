import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { dbRawRead, dbRawReadSingle, dbRawWrite } from '../../../../util/db-raw';
import { Errors } from '../../../../errors/general';
import dataExportHooks from '../../data-exporter/data-export/data-export.hooks';
import { dbDateNow } from '../../../../util/db-dates';
import { currentUid } from '../../../../util/uid';

interface Data {
  sortOrder: number;
  type: string;
  testWindowId: number;
  name: string;
  accommodation_slug: string;
  foreign_id: number;
  is_external: number;
}

enum systemSlugs {
  ABED = 'abed',

}

interface IAccommodation {
    id: number,
    name: string,
    system_slug: string,
    accommodation_slug: string,
    type: string,
    has_extra_notes : number,
    is_revoked: number,
    sortOrder : number,
    foreign_id : number,
    is_external: number,
}

interface ServiceOptions {}

export class Accommodations implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find(params?: Params): Promise<any[] | Paginated<Data>> {
    let { testWindowId } = (<any>params).query;
    const group_type = await this.getGroupTypeFromTestWindow(testWindowId)
    

    //in case we are making a new student, we want to display the list of accommodations before the student is created
    //so we allow data to be pulled when there is no uid.
    let setOfAccommodations: IAccommodation[] = await dbRawRead(this.app, {group_type}, this.getAccommsNoUID());
    
    return setOfAccommodations;

  }

  async getGroupTypeFromTestWindow(testWindowId: number){
    const test_window = await dbRawReadSingle(this.app, {testWindowId}, `
      select tw.type_slug 
      from test_windows tw
      where id = :testWindowId`
    )
    const group_type = test_window.type_slug;
    return group_type;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS')
    }

    const {testWindowId, name, accommodation_slug, sortOrder, type, foreign_id, is_external} = data;

    if(!testWindowId || !name || !accommodation_slug || sortOrder == undefined ||  !type || !foreign_id || is_external == undefined) {
    throw new Errors.BadRequest('MISSING_DATA');
  }
  const uid = await currentUid(this.app, params);
  const group_type = await this.getGroupTypeFromTestWindow(data.testWindowId)
  await this.app.service('db/write/accommodations').create({
    name: name,
    accommodation_slug: accommodation_slug,
    system_slug: group_type,
    accommodation_type: type, // Further update to not hard coded
    sortOrder:sortOrder,
    foreign_id: foreign_id,
    created_by_uid: uid,
    is_external: is_external
  })

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch(id: NullableId, data: any, params?: Params): Promise<any> {
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS')
    }
    const {  name, accommodation_slug, sortOrder, type, is_revoked, foreign_id, is_external } = data;
    if(name == undefined 
      && accommodation_slug == undefined 
      && is_revoked == undefined 
      && sortOrder == undefined
      && type == undefined
      && foreign_id == undefined
      && is_external == undefined
    ){
      throw new Errors.BadRequest("MISSING_DATA");
    }
    if(is_revoked !== undefined){
      const uid = await currentUid(this.app, params);
      if(is_revoked){
        await this.app.service('db/write/accommodations').patch(id, {
          is_revoked,
          revoked_on: is_revoked? dbDateNow(this.app): null,
          revoked_by_uid: uid,
        })
      }
      else{
        await this.app.service('db/write/accommodations').patch(id, {
          is_revoked: 0
        })
      }
      return {
        message: "Success"
      }
    }
    
    await this.app.service('db/write/accommodations').patch(id, {
      name,
      accommodation_slug,
      sortOrder,
      accommodation_type: type,
      foreign_id,
      is_external
    })
    return {
      message: "Success"
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  getAccommsNoUID() {
    return `
      select 
        acc.id
        ,acc.name
        ,acc.system_slug
        ,acc.accommodation_slug
        ,acc.accommodation_type as type
        ,acc.has_extra_notes 
        ,acc.is_revoked
        ,acc.sortOrder 
        ,acc.foreign_id 
        ,acc.is_external
      from  
        accommodations acc
      where 
        acc.system_slug = :group_type
      order by acc.sortOrder;
      `;
  }

}