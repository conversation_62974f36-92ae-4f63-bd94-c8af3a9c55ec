// Initializes the `public/educator/student-accommodations` service on path `/public/educator/student-accommodations`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Accommodations } from './accommodations.class';
import hooks from './accommodations.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/test-ctrl/accommodations': Accommodations & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-ctrl/accommodations', new Accommodations(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-ctrl/accommodations');

  service.hooks(hooks);
}
