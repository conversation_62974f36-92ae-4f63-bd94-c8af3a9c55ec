import * as authentication from '@feathersjs/authentication';
import { hasRoleAction } from '../../../../../hooks/has-role-action';
import { DBD_U_GROUP_TYPES } from '../../../../../constants/db-extracts';
// Don't remove this comment. It's needed to format import lines nicely.

const { authenticate } = authentication.hooks;

export default {
  before: {
    all: [],
    find: [],
    get: [],
    create: [
      authenticate('jwt') ,
      hasRoleAction({singularGroupType: DBD_U_GROUP_TYPES.mpt_test_controller}),
    ],
    update: [
      authenticate('jwt') ,
      hasRoleAction({singularGroupType: DBD_U_GROUP_TYPES.mpt_test_controller}),
    ],
    patch: [
      authenticate('jwt') ,
      hasRoleAction({singularGroupType: DBD_U_GROUP_TYPES.mpt_test_controller}),
    ],
    remove: [
      authenticate('jwt') ,
      hasRoleAction({singularGroupType: DBD_U_GROUP_TYPES.mpt_test_controller}),
    ]
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
};
