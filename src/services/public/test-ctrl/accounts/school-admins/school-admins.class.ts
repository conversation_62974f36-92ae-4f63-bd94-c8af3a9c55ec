import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import * as Errors from '@feathersjs/errors';
import { generateExcel } from '../../../../download-data-frame/download.listener';
const AWS = require('aws-sdk');
import { storeInS3, generateS3DownloadUrl} from '../../../../upload/upload.listener'
import { currentUid } from "../../../../../util/uid";
import { dbRawRead, dbRawWrite } from '../../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export const AWS_CONFIG = {
  secretAccessKey: 'Czd414/T9Agn0+d4P2xo1Z6ejQxNdaURBdf6WmAF',
  accessKeyId: '********************',
  region: 'ca-central-1'
}
const STORAGE_BUCKET = 'storage.mathproficiencytest.ca';

// configure file uploads
const s3 = new AWS.S3({
  ... AWS_CONFIG
});

export class SchoolAdmins implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (!params || !params.query) {
      throw new Errors.BadRequest();
    }
    return this.fetchSchoolAdminBulkLoadRecords()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    this.runProcess(data, params);
    return {isProcessStarted: true}
  }

  async runProcess(data: Data, params?: Params) {
    if (!params || !params.query){
      throw new Errors.BadRequest();
    }
    const { adminData, revokeExistingAdmin, dryRun,  } = <any> data;
    const isForceEmail = false; // !dryRun; // todo: this should be a separate toggle in the Web UI
    const created_by_uid = await currentUid(this.app, params);
    
    let emailLinkDomain = (<any>data).emailLinkDomain;
    if (emailLinkDomain.indexOf('localhost') > -1){
      emailLinkDomain = 'https://eqao.vretta.com/'
    }

    const schoolMidents :any[]= adminData.map((ad:any) => {return ad.SchoolMident})
    if(adminData.length === 0 ){
      throw new Errors.BadRequest();
    }

    // Step 1. Process revoked accounts
    let revokedAdminData:any[] = []
    if(revokeExistingAdmin){
      //revoke school access
      const revokedAccounts = await dbRawRead(this.app, [schoolMidents], `
         select sd.foreign_id as BoardMident
              , schl.foreign_id as SchoolMident
              , schl.name as SchoolName
              , u.contact_email as PrincipalEmail
              , u.first_name as PrincipalFirstName
              , u.last_name as PrincipalLastName
              , u.id as uid
              , ur.id as ur_id
          from schools schl 
          join school_districts sd on sd.group_id = schl.schl_dist_group_id
          join user_roles ur on ur.is_revoked = 0 and ur.role_type = 'schl_admin' and ur.group_id = schl.group_id 
          join users u on u.id = ur.uid
         where schl.foreign_id in (?)
      ;`);
      const userRolesId = revokedAccounts.map( (ra:any) => {return ra.ur_id });
      if (userRolesId.length){
        await dbRawWrite(this.app, [created_by_uid, userRolesId], `
          update user_roles set is_revoked = 1, revoked_on = now(), revoked_by_uid = ? where id in (?) and is_revoked = 0 and role_type = 'schl_admin'  
        ;`);
      }

      //revoke board access
      const revokeUserIds = revokedAccounts.map( (ra:any) => {return ra.uid });
      if(revokeUserIds.length){
        const revokedBoardAccounts = await dbRawRead(this.app, [revokeUserIds, schoolMidents], `
          select distinct ur.id
            from schools schl 
            join school_districts sd on sd.group_id = schl.schl_dist_group_id
            join user_roles ur on ur.is_revoked = 0 and ur.role_type = 'schl_admin' and ur.group_id = sd.group_id and ur.uid in (?) 
            join users u on u.id = ur.uid
          where schl.foreign_id in (?)
        ;`);

        const boardUserRolesId = revokedBoardAccounts.map( (ra:any) => {return ra.id });
        if(boardUserRolesId.length){
          await dbRawWrite(this.app, [created_by_uid, boardUserRolesId], `
            update user_roles set is_revoked = 1, revoked_on = now(), revoked_by_uid = ? where id in (?) and is_revoked = 0 and role_type = 'schl_admin'  
          ;`);
        }
      }
      

      revokedAccounts.forEach(ra =>{
        if(revokedAdminData.find(rad => rad.SchoolMident === ra.SchoolMident && rad.PrincipalEmail === ra.PrincipalEmail) === undefined){
          revokedAdminData.push({
            BoardMident: ra.BoardMident,
            SchoolMident: ra.SchoolMident,
            SchoolName: ra.SchoolName,
            PrincipalEmail: ra.PrincipalEmail,
            PrincipalFirstName: ra.PrincipalFirstName,
            PrincipalLastName: ra.PrincipalLastName
          })
        }
      })
    }

    //Step 2. Fetch not included accounts
    const notIncludedAdminData = await dbRawRead(this.app, [schoolMidents], `
        select distinct sd.foreign_id as BoardMident
             , schl.foreign_id as SchoolMident
             , schl.name as SchoolName
             , u.contact_email as PrincipalEmail
             , u.first_name as PrincipalFirstName
             , u.last_name as PrincipalLastName
          from schools schl 
          join school_districts sd on sd.group_id = schl.schl_dist_group_id
          join user_roles ur on ur.is_revoked = 0 and ur.role_type = 'schl_admin' and ur.group_id = schl.group_id 
          join users u on u.id = ur.uid
         where schl.foreign_id not in (?)
    ;`);
    

    //Step 3. Create new school admin accounts
    //*******************************remember to add require confirm when create user_role************************************************************
    let successAdminData:any[] = []
    let failAdminData:any[] = []
    for(let i = 0; i < adminData.length; i++){
      let admin = adminData[i]
      let first_name = admin.PrincipalFirstName || '_'; //remember to check user_first name and last name
      let last_name = admin.PrincipalLastName || '_'; //remember to check user_first name and last name
      let email = admin.PrincipalEmail;
      let school = await dbRawRead(this.app, [admin.SchoolMident], `select group_id from schools where foreign_id = ?;`)
      if(school.length == 0 ){
        failAdminData.push({...admin, FailReason: 'SchoolMident_NOT_EXIST' })
        continue
      }
      const schl_group_id = school[0].group_id
      await this.app.service('public/school-admin/school-access')
        .createAdminAccount( first_name, last_name, email, emailLinkDomain, schl_group_id, created_by_uid, dryRun, isForceEmail )
        .then(res =>{
          successAdminData.push({...admin})
        })
        .catch(err =>{
          failAdminData.push({...admin, FailReason: err.message })
        })
    }

    //Step 4. Create new school_admin_bulk_load record
    const newSchoolAdminBuldLoadRecord = await this.app
      .service('db/write/school-admin-bulk-load-record')
      .create({
        created_by_uid,
        revoke_existing_admin: revokeExistingAdmin ? 1 : 0
      });

    //Step 5. Upload files to s3
    const folderName = 'test-ctrl-school-admin-bulk-create'

    // 5.1 upload Input Accounts
    let inputAccountsFilePath = ''
    if(adminData.length > 0){
      const inputAccountsexcelFile = generateExcel(adminData);
      const inputAccountFileName = 'InputAccounts.xlsx';
      inputAccountsFilePath = [folderName, newSchoolAdminBuldLoadRecord.id, inputAccountFileName].join('/');
      storeInS3(inputAccountsFilePath, inputAccountsexcelFile)
    }
    
    // 5.2 upload Success Accounts
    let successAccountsFilePath = ''
    if(successAdminData.length > 0){
      const successAccountsFile = generateExcel(successAdminData);
      const successAccountsFileName = 'SuccessAccounts.xlsx';
      successAccountsFilePath = [folderName, newSchoolAdminBuldLoadRecord.id, successAccountsFileName].join('/');
      storeInS3(successAccountsFilePath, successAccountsFile)
    }

    // 5.3 upload Fail Accounts
    let failAccountsFilePath = ''
    if(failAdminData.length > 0){
      const failAccountsFile = generateExcel(failAdminData);
      const failAccountsFileName = 'FailAccounts.xlsx';
      failAccountsFilePath = [folderName, newSchoolAdminBuldLoadRecord.id, failAccountsFileName].join('/');
      storeInS3(failAccountsFilePath, failAccountsFile)
    }

    // 5.4 upload Revoked Accounts
    let revokedAccountsFilePath = ''
    if(revokedAdminData.length > 0){
      const revokedAccountsFile = generateExcel(revokedAdminData);
      const revokedAccountsFileName = 'RevokeAccounts.xlsx';
      revokedAccountsFilePath = [folderName, newSchoolAdminBuldLoadRecord.id, revokedAccountsFileName].join('/');
      storeInS3(revokedAccountsFilePath, revokedAccountsFile)
    }

    // 5.5 upload Not Included Accounts
    let notIncludedAccountsFilePath = ''
    if(notIncludedAdminData.length > 0){
      const notIncludedAccountsFile = generateExcel(notIncludedAdminData);
      const notIncludedAccountsFileName = 'NotIncluded.xlsx';
      notIncludedAccountsFilePath = [folderName, newSchoolAdminBuldLoadRecord.id, notIncludedAccountsFileName].join('/');
      storeInS3(notIncludedAccountsFilePath, notIncludedAccountsFile)
    }

    // 5.6 upload Idle Accounts
    //TODO (Not Doing Now)
    let idleAccountsFilePath = ''

    //update school-admin-bulk-load-record file path
    await this.app
      .service('db/write/school-admin-bulk-load-record')
      .patch(newSchoolAdminBuldLoadRecord.id, {
        input_accounts_file: inputAccountsFilePath,
        success_accounts_file: successAccountsFilePath,
        fail_accounts_file: failAccountsFilePath,
        revoked_accounts_file: revokedAccountsFilePath,
        not_included_accounts_file: notIncludedAccountsFilePath,
        idle_accounts_file: idleAccountsFilePath,
      });

    //Step 6. Fetch new record and return it
    return this.fetchSchoolAdminBulkLoadRecords(newSchoolAdminBuldLoadRecord.id)
  }

  async fetchSchoolAdminBulkLoadRecords(id?:any){
    const params = id? [id]:[]
    const schoolAdminBulkLoadRecords= await dbRawRead(this.app, params, `
        select id
              , created_by_uid as loader_uid
              , created_on as date
              , revoke_existing_admin as revoke_existing_admin
              , input_accounts_file as input_accounts
              , success_accounts_file as sucess_accounts
              , fail_accounts_file as fail_accounts
              , revoked_accounts_file as revoked_accounts
              , not_included_accounts_file as not_included_accounts
              , idle_accounts_file as idle_accounts
          from school_admin_bulk_load_record
          where is_revoked = 0   
        ${ id? 'and id = ?':''}
      order by id desc
    ;`);
    schoolAdminBulkLoadRecords.forEach(record =>{
      if(record.input_accounts){
        record.input_accounts = generateS3DownloadUrl(record.input_accounts)
      }
      if(record.sucess_accounts){
        record.sucess_accounts = generateS3DownloadUrl(record.sucess_accounts)
      }
      if(record.fail_accounts){
        record.fail_accounts = generateS3DownloadUrl(record.fail_accounts)
      }
      if(record.revoked_accounts){
        record.revoked_accounts = generateS3DownloadUrl(record.revoked_accounts)
      }
      if(record.not_included_accounts){
        record.not_included_accounts = generateS3DownloadUrl(record.not_included_accounts)
      }
      if(record.idle_accounts){
        record.idle_accounts = generateS3DownloadUrl(record.idle_accounts)
      }
    })
    return schoolAdminBulkLoadRecords;
  }



  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
