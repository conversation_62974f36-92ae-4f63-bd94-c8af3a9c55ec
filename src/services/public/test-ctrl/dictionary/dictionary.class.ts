import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead } from '../../../../util/db-raw';
import { ESCAPE_HTML_ENTITIES_TABLE } from './constants';

interface Data {}

interface ServiceOptions {}

const enum DB_TABLE_DICTIONARY { // todo: not seeing why these are in separate db tables
  EN = 'dictionary_abed',
  FR = 'dictionary_abed_fr',
}

export class Dictionary implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>>  {
    if (params && params.query && params.query.userData){
      const word = params.query.userData;
      const lang = params.query.lang;
      const isReplaceEntities = (lang === 'fr');
      return this.getSuggestions(word, this.dbTableByLang(lang), isReplaceEntities);
    }
    throw new Errors.BadRequest();
  }

  async get (word: string, params?: Params): Promise<Data> {
    if (!word){
      throw new Errors.BadRequest();
    }
    const lang = params?.query?.lang;
    return this.getWordDef(word, this.dbTableByLang(lang) );
  }

  private dbTableByLang(lang:string){
    if (lang === 'fr'){
      return DB_TABLE_DICTIONARY.FR;
    }
    return DB_TABLE_DICTIONARY.EN;
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async getSuggestions(word: string, dictionaryDBTable:DB_TABLE_DICTIONARY, isReplaceEntities:boolean) {
    let wordLookup = isReplaceEntities ? this.escapeHtmlEntities(word) : word;
    const wordLookupWildcardRight = wordLookup + '%';
    type ISuggestionRecord = {id:number, word:string, is_searched:number}
    return <Promise<ISuggestionRecord[]>> dbRawRead(this.app, {wordLookup, wordLookupWildcardRight}, `
        SELECT id
             , word
             , (CASE WHEN word= CONVERT(:wordLookup USING latin1) THEN 1 ELSE 0 END) is_searched
        FROM ${dictionaryDBTable} d
        WHERE d.word like CONVERT(:wordLookupWildcardRight USING latin1)
        GROUP BY word, Cast(word As BINARY(100))
        LIMIT 10
    ;`);
  }
  
  async getWordDef(word: string, dictionaryDBTable:DB_TABLE_DICTIONARY){
    const raw_json = await dbRawRead(this.app, {word}, `
      SELECT 
        raw_json,
        thesaurus_raw_json
      FROM ${dictionaryDBTable} d
      WHERE BINARY d.word = :word
      LIMIT 1
    ;`);
    return raw_json;
  }

  /**
     * Converts all html entities in a string to their corresponding html code/name
     * @param text 
     * @returns Same string with html entity characters replaced with their html code
     */
  escapeHtmlEntities(text: string) {
    const getReplacementChar = (char: string) => {
        return '&' + 
        (ESCAPE_HTML_ENTITIES_TABLE[char.charCodeAt(0)] || '#'+char.charCodeAt(0)) + ';';
    }
    return text.replace(/[\u00A0-\u2666<>]/g, getReplacementChar);
  };
}
