// Initializes the `public/test-ctrl/dictionary` service on path `/public/test-ctrl/dictionary`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Dictionary } from './dictionary.class';
import hooks from './dictionary.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/test-ctrl/dictionary': Dictionary & ServiceAddons<any>;
  }
}

export default function (app: Application) {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-ctrl/dictionary', new Dictionary(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-ctrl/dictionary');


  service.hooks(hooks);
}
