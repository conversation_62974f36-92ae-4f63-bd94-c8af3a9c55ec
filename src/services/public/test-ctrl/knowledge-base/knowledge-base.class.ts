import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead } from '../../../../util/db-raw';
import { dbDateNow } from '../../../../util/db-dates';
import { currentUid } from '../../../../util/uid';

interface Data {}

interface ServiceOptions {}

export class KnowledgeBase implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    const {context_slug} = params?.query!;
    return dbRawRead(this.app, {context_slug}, `
      select * 
      from support_knowledge_base
      where is_revoked = 0 and ${params?.query?.context_slug ? `context_slug = :context_slug` : `context_slug is null`} 
      ;
    `)
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (params){
      const verifiedUid = await currentUid(this.app, params)
      const { slug, section, caption, file_url, asset_type } = <any> data;
      if(!slug || !caption || !file_url || !asset_type){
        throw new Errors.BadRequest('MISSING_PARAMS');
      }
      const userGuideData = {
        slug,
        section,
        caption,
        file_url,
        asset_type,
        created_by_uid: verifiedUid
      };
      
      await this.app.service('db/write/support-knowledge-base').create({
        ... userGuideData,
      });
      
      return {
        success: true,
        message: "User guide created successfully."
      }
    }
    throw new Errors.BadRequest()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if(!id){
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    const { slug, section, caption, file_url, asset_type, orders } = <any> data;
    
    if(!slug || !section || !caption || !file_url || !asset_type){
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    
    await this.app.service('db/write/support-knowledge-base').patch(id, {
      slug,
      section,
      caption, 
      file_url, 
      asset_type,
      orders
    })
    
    return {
      success: true,
      message: "User guide edited successfully."
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    if(!id){
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    await this.app.service('db/write/support-knowledge-base').patch(id, {
      is_revoked: 1,
      revoked_on: dbDateNow(this.app),
    })
    return {
      success: true,
      message: "User guide removed successfully."
    }
  }

}
