// Initializes the `public/test-ctrl/knowledge-base` service on path `/public/test-ctrl/knowledge-base`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { KnowledgeBase } from './knowledge-base.class';
import hooks from './knowledge-base.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/test-ctrl/knowledge-base': KnowledgeBase & ServiceAddons<any>;
  }
}

export default function (app: Application) {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-ctrl/knowledge-base', new KnowledgeBase(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-ctrl/knowledge-base');

  service.hooks(hooks);
}
