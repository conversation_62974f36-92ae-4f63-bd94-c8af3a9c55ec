// Initializes the `public/test-ctrl/load-test/scorer` service on path `/public/test-ctrl/load-test/scorer`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Scorer } from './scorer.class';
import hooks from './scorer.hooks';

// Add this service to the service type index
declare module '../../../../../declarations' {
  interface ServiceTypes {
    'public/test-ctrl/load-test/scorer': Scorer & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-ctrl/load-test/scorer', new Scorer(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-ctrl/load-test/scorer');

  service.hooks(hooks);
}
