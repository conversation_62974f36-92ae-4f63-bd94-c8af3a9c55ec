import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { dbRawRead, dbRawReadCount } from '../../../../../util/db-raw';
import { Errors } from '../../../../../errors/general';

interface Data {}

interface ServiceOptions {}

export class Classes implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (!params || !params.query) {
      throw new Errors.BadRequest();
    }
    const records = await dbRawRead(this.app, [], `
    SELECT  sc.name as className, sc.access_code,ss.name as semester_name, sc.is_active,sd.foreign_id as brdMident, sd.name as brdName, sch.name as schName, sd.is_sample as is_sample_board
    FROM mpt_dev.school_classes sc
    join mpt_dev.school_districts sd on sd.group_id = sc.schl_dist_group_id
    join mpt_dev.schools sch on sch.group_id = sc.schl_group_id
    join mpt_dev.school_semesters ss on ss.id = sc.semester_id
    ;`);
    return [{
      records
    }];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
