import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../../../declarations';
import { Errors } from '../../../../../../../errors/general';
import { dbRawRead, dbRawReadReporting } from '../../../../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class ByBoard implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    let slugs: string[] = ['G9_OPERATIONAL', 'G9_SAMPLE'];
    let testWindowId: number | null = null;

    if (params && params.query) {
      const { ts_slugs, test_window_id } = params.query;
      if (ts_slugs) slugs = ts_slugs;
      if (test_window_id) testWindowId = test_window_id;
    }
    
    const records = await dbRawReadReporting(this.app, [slugs], `
      
      select t.AssessmentType
      , t.BrdMident
      , t.BrdName
      , t.Lang
      , sum(t.NumStudents) as NumStudents
      , count(0) as NumSessions
      from (
      select sd.foreign_id as BrdMident, sd.name BrdName, sd.brd_lang as Lang, scts.slug as AssessmentType, count(0) as NumStudents
      from test_windows tw
      join test_sessions ts  on tw.id = ts.test_window_id ${ testWindowId ? ` and ts.test_window_id = ${testWindowId}` : ''}
      join school_class_test_sessions scts on ts.id = scts.test_session_id 
      join school_classes sc on sc.id = scts.school_class_id
      join user_roles ur on ur.group_id = sc.group_id and ur.role_type like '%student%'
      join school_districts sd on sd.group_id = sc.schl_dist_group_id and sd.is_sample = 0 and sd.is_active = 1
      where scts.slug IN (?)
      and tw.is_active = 1
      group by scts.id
      ) t
      group by t.BrdMident, t.AssessmentType
      order by t.AssessmentType, sum(t.NumStudents) desc
      ;
    
    `)
    return records;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
