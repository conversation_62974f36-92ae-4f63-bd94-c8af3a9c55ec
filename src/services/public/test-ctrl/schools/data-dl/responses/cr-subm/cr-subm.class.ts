import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../../../declarations';
import { Errors } from '../../../../../../../errors/general';
import { dbRawRead, dbRawReadReporting } from '../../../../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class CrSubm implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    let slugs: string[] = ['G9_OPERATIONAL'];
    let testWindowId: number | null = null;

    if (params && params.query) {
      const { ts_slugs, test_window_id } = params.query;
      if (ts_slugs) slugs = ts_slugs;
      if (test_window_id) testWindowId = test_window_id;
    }

    const records = await dbRawReadReporting(this.app, [testWindowId], `
      select /*+ MAX_EXECUTION_TIME(1000000000) */ item_id ItemId, item_label ItemLabel, lang Lang, count(0) NumResponses, sum(is_nr) NumNoResponses
      from ( 
        select taqr.id, taqr.test_question_id item_id, mwi.slug item_label, taqr.is_nr, mw.lang 
        from test_window_td_alloc_rules twtar
        join test_attempts ta
            on ta.twtdar_id = twtar.id
        join test_attempt_question_responses taqr 
            on taqr.test_attempt_id = ta.id 
        join marking_window_test_window mwtw 
            on mwtw.test_window_id = twtar.test_window_id
            and mwtw.is_material = 0
        join marking_windows mw 
            on mw.id = mwtw.marking_window_id 
        join marking_window_items mwi 
            on mwi.marking_window_id = mw.id 
            and mwi.item_id  = taqr.test_question_id 
        where twtar.test_window_id = ?
        group by taqr.id
      ) t 
      group by item_id
      ;
    `)
    return records;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
