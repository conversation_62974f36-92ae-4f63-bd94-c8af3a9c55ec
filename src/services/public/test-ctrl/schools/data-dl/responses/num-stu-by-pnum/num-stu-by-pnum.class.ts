import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../../../declarations';
import { Errors } from '../../../../../../../errors/general';
import { dbRawRead, dbRawReadReporting } from '../../../../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class NumStuByPnum implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    let slugs: string[] = ['G9_OPERATIONAL'];
    let testWindowId: number | null = null;

    if (params && params.query) {
      const { ts_slugs, test_window_id } = params.query;
      if (ts_slugs) slugs = ts_slugs;
      if (test_window_id) testWindowId = test_window_id;
    }

    const records = await dbRawReadReporting(this.app, [testWindowId], `
      select slug, panel_number, count(0) NumAttempts, sum(is_closed) NumClosed
      from ( 
        select tf.source_tf_id panel_number, twtar.slug, ta.id, ta.is_closed 
        from test_window_td_alloc_rules twtar
        join test_attempts ta
            on ta.twtdar_id = twtar.id
            and twtar.is_secured = 1
            and twtar.is_questionnaire  = 0
        join test_forms tf 
            on tf.id = ta.test_form_id 
        where twtar.test_window_id = ?
        group by ta.id
      ) t 
      group by slug, panel_number
      ;
    `)
    return records;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
