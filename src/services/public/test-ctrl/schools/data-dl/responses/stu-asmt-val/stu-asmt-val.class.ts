import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../../../declarations';
import { Errors } from '../../../../../../../errors/general';
import { dbRawRead, dbRawReadReporting } from '../../../../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class StuAsmtVal implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    let testWindowId: number | null = null;

    if (params && params.query) {
      const { test_window_id } = params.query;
      if (test_window_id) testWindowId = test_window_id;
    }

    const records = await dbRawReadReporting(this.app, [], `
      
      select * from (
        select sd.foreign_id as BrdMident
          , sd.name as BrdName
          , s.foreign_id as SchMident
          , s.name as SchName
          , DATE_FORMAT(ssais.created_on, '%y-%m-%d') validated_on
          , min(is_revoked) is_revoked
        from school_student_asmt_info_signoffs ssais
        join schools s on s.group_id = ssais.schl_group_id
        join school_districts sd on sd.group_id = s.schl_dist_group_id and sd.is_sample = 0 and sd.is_active = 1
        where ${testWindowId ? `test_window_id = ${testWindowId}` : 'tw_type_slug = \'EQAO_G9M\''}
        group by ssais.schl_group_id
      ) t
      order by t.is_revoked, t.validated_on
      ;
    
    `)
    return records;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
