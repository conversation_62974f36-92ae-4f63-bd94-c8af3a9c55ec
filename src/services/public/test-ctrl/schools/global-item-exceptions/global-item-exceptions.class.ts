import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { dbRawRead } from '../../../../../util/db-raw';
import { Errors } from '../../../../../errors/general';

interface Data {}

interface ServiceOptions {}

export class GlobalItemExceptions implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {

    if (!params?.query){
      throw new Errors.BadRequest();
    }
    const {test_window_id} = params.query
    return dbRawRead(this.app, {test_window_id}, `
      select id
           , test_window_id
           , item_id
           , lang
           , is_prorated
           , is_score_override
           , score_override
           , is_nr_override
           , nr_override
           , match_response_value
           , notes
           , created_by_uid
           , created_on
           , is_revoked
           , revoked_by_uid
           , revoked_on
      from tw_exceptions_items tei 
      where test_window_id = :test_window_id
    `);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
