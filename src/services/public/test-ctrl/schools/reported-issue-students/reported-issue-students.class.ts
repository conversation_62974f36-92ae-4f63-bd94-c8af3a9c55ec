import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { dbDateNow } from '../../../../../util/db-dates';
import { dbRawRead, dbRawReadSingle } from '../../../../../util/db-raw';
import { currentUid } from '../../../../../util/uid';
import { FLAGS } from '../../../../../util/whiteLabelParser';

interface Data {}

interface ServiceOptions {}

export class ReportedIssueStudents implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    const whitelabel = params?.query?.whitelabel || '';
    const abedLabel = FLAGS.ABED;
    // load students associated with a session 
    const ts_id = id;
    const ABED_NAMESPACES = ["'abed_sdc'", "'abed_course'"];
    const EQAO_NAMESPACES = ["'eqao_sdc'"]
    const students = await dbRawRead(this.app, {ts_id}, `
      select ta.id ta_id -- can be one of many
          , twtar.test_window_id 
          , ta.uid
          , u.first_name 
          , u.last_name
          , um.value ${whitelabel == abedLabel ? 'StudentASN' : 'StudentOEN'}
          , tes.category single_stu_ex_cat
          , count(distinct tes.id) num_stu_ex
          , count(distinct tesi.id) num_stu_item_ex
      from test_attempts ta  
      join test_window_td_alloc_rules twtar 
        on twtar.id = ta.twtdar_id 
      join users u 
        on u.id = ta.uid 
      join user_metas um 
        on um.key_namespace in (${whitelabel == abedLabel ? ABED_NAMESPACES : EQAO_NAMESPACES})
        and um.key =  '${whitelabel == abedLabel ? 'StudentIdentificationNumber' : 'StudentOEN'}'
        and um.uid = u.id
      left join tw_exceptions_students tes 
        on tes.uid = ta.uid
        and tes.test_window_id = twtar.test_window_id
        and tes.is_revoked = 0 
      left join tw_exceptions_student_items tesi  
        on tesi.uid = ta.uid
        and tesi.test_window_id = twtar.test_window_id
        and tesi.is_revoked = 0
      where ta.test_session_id = :ts_id
      group by u.id
      order by u.first_name 
             , u.last_name
    `);

    const query = `
    select ta.id ta_id -- can be one of many
        , twtar.test_window_id 
        , ta.uid
        , u.first_name 
        , u.last_name
        , um.value ${whitelabel == abedLabel ? 'StudentASN' : 'StudentOEN'}
        , tes.category single_stu_ex_cat
        , count(distinct tes.id) num_stu_ex
        , count(distinct tesi.id) num_stu_item_ex
    from test_attempts ta  
    join test_window_td_alloc_rules twtar 
      on twtar.id = ta.twtdar_id 
    join users u 
      on u.id = ta.uid 
    join user_metas um 
      on um.key_namespace in (${whitelabel == abedLabel ? ABED_NAMESPACES : EQAO_NAMESPACES})
      and um.key =  '${whitelabel == abedLabel ? 'StudentIdentificationNumber' : 'StudentOEN'}'
      and um.uid = u.id
    left join tw_exceptions_students tes 
      on tes.uid = ta.uid
      and tes.test_window_id = twtar.test_window_id
      and tes.is_revoked = 0 
    left join tw_exceptions_student_items tesi  
      on tesi.uid = ta.uid
      and tesi.test_window_id = twtar.test_window_id
      and tesi.is_revoked = 0
    where ta.test_session_id = :ts_id
    group by u.id
    order by u.first_name 
           , u.last_name
  `

  console.log(query);
    // if (students.length){
    //   const {test_window_id} = students[0];
    //   const studentUids:number[] = [];
    //   const studentRef = new Map()
    //   for (let student of students){
    //     studentUids.push(student.uid);
    //     studentRef.set(+student.uid, student);
    //     student.stuEx = [];
    //     student.stuItemEx = [];
    //   }
    //   const studentExceptions = await this.app
    //     .service('public/test-ctrl/schools/student-exceptions')
    //     .getStudentExceptionsByTwAndUids(
    //       test_window_id, 
    //       studentUids, 
    //       false
    //     );
    //     studentExceptions
    // }

    return students;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if (params){
      const whitelabel = params.query?.whitelabel || '';
      const abedLabel = FLAGS.ABED;
      // add/remove students from the reported issue
      type IStudent = {
        uid: number,
        first_name: string,
        last_name: string,
        StudentIdentificationNumber?: string,
        StudentOEN?: string,
        isIncluded: boolean,
      }
      type IPayload = {
        ric_id: number,
        test_window_id: number,
        students: IStudent[]
      }
      type IRic = {
        id:any,
        ri_category_id:any,
        order:any,
        description:any,
        test_window_id:any,
        created_on:any,
        created_by_uid:any,
        updated_on:any,
        updated_by_uid:any,
        is_resolved:any,
        resolved_on:any,
        resolved_by_uid:any,
        test_session_id:any,
        school_class_id:any,
        category:any,
        sub_category:any,
        log_id:any,
        cust_ticket_url:any,
        dev_ticket_url:any,
        resolution_slug:any,
        assigned_uid:any,
        assigned_on:any,
      }
      const {ric_id, students} = <IPayload> data;
      const reviewerUid = await currentUid(this.app, params);
      const reportedIssueCommon: IRic = await this.app.service('db/write/reported-issues-common').get(ric_id)
      const studentsAdded:string[] = []
      const studentsRestored:string[] = []
      const studentsRemoved:string[] = []
      const addStudentToCommentList = (arr:string[], student:IStudent) => {
        arr.push(`${student.first_name} ${student.last_name} (${whitelabel == abedLabel ? student.StudentIdentificationNumber : student.StudentOEN})`)
      }
      for (let student of students){
        const student_uid = student.uid
        const [curStuRI] = await dbRawRead(this.app, {ric_id, student_uid}, `
          select id, is_revoked
          from reported_issues
          where testtaker_uid = :student_uid
            and reported_issues_common_id = :ric_id
        `)
        if (student.isIncluded){
          if (curStuRI){
            if (curStuRI.is_revoked == 1){
              await this.app.service('db/write/reported-issues').patch(curStuRI.id, {
                is_revoked: 0,
                // revoked_by_uid: student_uid,
              })
              addStudentToCommentList(studentsRestored, student);
            }
          }
          else {
            await this.app.service('db/write/reported-issues').create({
              reported_issues_common_id: ric_id,
              test_window_id: reportedIssueCommon.test_window_id,
              test_session_id: reportedIssueCommon.test_session_id,
              testtaker_uid: student_uid,
              created_on: dbDateNow(this.app),
              created_by_uid: reviewerUid,
            });
            // deal with double addition due to race condition
            const stuRIs = await dbRawRead(this.app, {ric_id, student_uid}, `
              select id, is_revoked
              from reported_issues
              where testtaker_uid = :student_uid
                and reported_issues_common_id = :ric_id
            `)
            if (stuRIs.length > 1){
              for (let i=0; i<stuRIs.length; i++){
                await this.app.service('db/write/reported-issues').patch(curStuRI.id, {
                  is_invalid: 1,
                  is_revoked: 1,
                  revoked_by_uid: student_uid,
                })
              }
            }
            addStudentToCommentList(studentsAdded, student);
          }
        }
        else {
          // check for existing record, revoke if exists
          if (curStuRI){
            if (curStuRI.is_revoked == 0){
              await this.app.service('db/write/reported-issues').patch(curStuRI.id, {
                is_revoked: 1,
                revoked_by_uid: reviewerUid,
                revoked_on: dbDateNow(this.app)
              });
              addStudentToCommentList(studentsRemoved, student);
            }
          }
        }
      }
      let comment = 'Modified students associated to reported issue. ';

      const commentAddition = [
        {prefix: 'Added: ', arr: studentsAdded},
        {prefix: 'Restored: ', arr: studentsRestored},
        {prefix: 'Removed: ', arr: studentsRemoved},
      ].map(config => {
        if (config.arr.length == 0){
          return ''
        }
        else {
          return config.prefix + config.arr.join(', ')
        }
      })
      .filter(s => s !== '')
      .join('; ');
      comment += commentAddition;

      await this.app.service('db/write/reported-issue-comments').create({
        reported_issues_common_id: ric_id,
        comment,
        uid: reviewerUid,
        is_auto: 1
      })

      const [updatedRicRecord] = await this.app.service('public/test-ctrl/schools/reported-issues').loadRecords({ric_id})

      return updatedRicRecord
    }
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
