import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { dbRawRead } from '../../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class ReportedIssuesAssignees implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return dbRawRead(this.app, {}, `
      select * from (
        select ur.uid uid
            , concat(u.first_name, ' ', u.last_name) assigned_name
            , u.contact_email assigned_email
            , case 
              when u.contact_email like '%@vretta.com' then 1
              else 0
            end is_vretta
        from user_roles ur 
        join u_groups ug 
          on ug.id = ur.group_id 
          -- and ug.group_type = ...
        join users u
          on u.id = ur.uid
        where ur.role_type = 'test_ctrl_issue_revw'
          and ur.is_revoked = 0
      ) t
      group by uid
      order by is_vretta, assigned_email
    `)
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
