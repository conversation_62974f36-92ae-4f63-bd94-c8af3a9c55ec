import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Knex } from 'knex';
import { Application } from '../../../../../declarations';
import { dbRawRead, dbRawReadCount, dbRawReadReporting } from '../../../../../util/db-raw';
import { Errors } from '../../../../../errors/general';
import { currentUid } from '../../../../../util/uid';
import { dbDateNow } from '../../../../../util/db-dates';

enum SCHOOL_TYPE_SLUGS {
  ELEMENTARY = "ELEMENTARY",
  SECONDARY = "SECONDARY",
  K12 = "K12"
}

interface Data { }

interface ISchoolInfo{
  brdMident: number,
  schMident: number,
  is_sample:number,
  schName: string,
  lang: string,
  is_private: string,
  type_slug: string,
  is_sandbox: number,
  whiteLabelContext?: string,
}
interface ServiceOptions { }

export class School implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor(options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find(params?: Params): Promise<Data[] | Paginated<Data>> {
    if (!params || !params.query) {
      throw new Errors.BadRequest();
    }
    const records = await dbRawReadReporting(this.app, [], `
      SELECT s.id s_id
           ,sd.classifications
           , sd.foreign_id as brdMident
           , sd.name as brdName
           , sd.is_sample
           , s.foreign_id as schMident
           , s.name as schName
           , s.lang
           , s.is_private
           , s.is_active
           , s.type_slug
           , s.is_sandbox
      FROM schools s
      join school_districts sd 
        on sd.group_id = s.schl_dist_group_id
    ;`);
    return [{
      records
    }];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get(id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create(data: ISchoolInfo, params?: Params): Promise<Data> {
    if(!params || !params.query) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    const created_by_uid = await currentUid(this.app, params);
    const { tc_group_id } = (<any> params).query;
    const {
      brdMident,
      schMident,
      schName,
      lang,
      is_private,
      type_slug,
      is_sandbox,
      whiteLabelContext
    } = data;

    const schoolDistrict = <Paginated<any>> await this.app
    .service('db/read/school-districts')
    .find({
      query:{
        foreign_id: brdMident
      }
    });
    if (schoolDistrict.total > 0) {

      const existingSchool = <Paginated<any>> await this.app
      .service('db/read/schools')
      .find({
        query:{
          foreign_id: schMident
        }
      });

      if (existingSchool.total > 0){
        throw new Errors.BadRequest('School mident already exists')
      }

      const schoolGroupRecord = await this.app
      .service('db/write/u-groups')
      .create({
        group_type:'school',
        description:schName
      })


      const schoolRecord = await this.app
        .service('db/write/schools')
        .create({
          group_id: schoolGroupRecord.id,
          schl_dist_group_id:schoolDistrict.data[0].group_id,
          foreign_id:schMident,
          name:schName,
          lang:lang,
          is_active:1,
          is_private:(is_private === 'private') ? 1 : 0,
          type_slug,
          is_sandbox,
        })

      if(type_slug && whiteLabelContext == 'ABED'){
        let assessmentDefs = [];
        if(type_slug == SCHOOL_TYPE_SLUGS.ELEMENTARY) {
          assessmentDefs.push(SCHOOL_TYPE_SLUGS.ELEMENTARY);
        } else if(type_slug == SCHOOL_TYPE_SLUGS.SECONDARY) {
          assessmentDefs.push(SCHOOL_TYPE_SLUGS.SECONDARY);
        } else if(type_slug == SCHOOL_TYPE_SLUGS.K12){
          assessmentDefs.push(SCHOOL_TYPE_SLUGS.ELEMENTARY, SCHOOL_TYPE_SLUGS.SECONDARY);
        }

        const assessmentIds = await dbRawRead(this.app, [assessmentDefs], `
          select * from assessment_def
            where type_slug in (?)
          ;
        `);

        assessmentIds.forEach(async (assessment) => {
          await this.app.service('db/write/school-assessments-map').create({
            assessment_def_id: assessment.id,
            created_by_uid,
            created_on: dbDateNow(this.app),
            school_id: schoolRecord.id
          });

          await this.app.service('db/write/school-type-assessment-settings').create({
            assessment_def_id: assessment.id,
            created_by_uid,
            created_on: dbDateNow(this.app),
            school_id: schoolRecord.id,
            is_insecure: 0,
            is_softlock_enabled: 0,
            settings_type_slug: 'ABED'
          });
        })
      }
      
    } else {
      throw new Errors.BadRequest('School Board does not exist')
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update(id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch(id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove(id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
