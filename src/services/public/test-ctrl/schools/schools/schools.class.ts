import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { dbRawReadReporting } from '../../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class Schools implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return dbRawReadReporting(this.app, {}, `
      select s.id
          , s.group_id
          , s.schl_dist_group_id
          , s.foreign_id
          , s.name
          , s.lang
          , s.is_private
          , sd.foreign_id  sd_foreign_id
          , sd.name  sd_name
          , null num_teachers
          , null num_students
      from schools s
      join school_districts sd on s.schl_dist_group_id = sd.group_id
      where sd.is_active = 1
          and s.is_active = 1
    `)
    // const schools = <Paginated<any>> await this.app.service('db/read/schools-summary').find({query:{$limit:50000}})
    // return schools.data;
  }

  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
