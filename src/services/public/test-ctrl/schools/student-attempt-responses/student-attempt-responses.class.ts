import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { dbRawRead, dbRawReadReporting, dbRawReadSingle, dbRawReadSingleReporting } from '../../../../../util/db-raw';
import { generateS3DownloadUrl } from '../../../../upload/upload.listener';
import axios from 'axios';
import { Errors } from '../../../../../errors/general';

interface Data {}

interface ServiceOptions {}

export class StudentAttemptResponses implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {

    if (params && params.query){
      
      const {test_window_id} = params.query
      const attempt_id = id;
  
      // form
      const attemptRecord = await dbRawReadSingleReporting(this.app, [attempt_id], `
        select ta.id
             , ta.uid
             , ta.started_on
             , ta.closed_on
             , ta.test_form_cache
             , ta.test_form_id
             , tf.source_tf_id
             , tf.test_design_id
             , tf.created_on tf_created_on
             , tf.file_path 
        from test_attempts ta
        join test_forms tf on tf.id = ta.test_form_id
        where ta.id = ?
      `)
      const {
        started_on,
        closed_on,
        test_form_cache,
        file_path,
        test_form_id,
        source_tf_id,
        test_design_id,
        tf_created_on,
        uid,
      } = attemptRecord
      const formUrl = generateS3DownloadUrl(file_path, 60);
      const formData = await axios.get(formUrl, {});
  
      // responses
      const taqrRecords = await dbRawReadReporting(this.app, [attempt_id], `
        select 
            taqr.test_attempt_id ta_id
          , taqr.id taqr_id
          , taqr.module_id 
          , taqr.section_id
          , taqr.test_question_id 
          , tq.question_label
          , taqr.score 
          , taqr.weight
          , taqr.is_nr 
          , taqr.created_on
          , taqr.updated_on
          , tesi.created_on prorated_on
          , tesi.is_prorated is_prorated
          , tesi.is_score_override is_score_override
          , tesi.new_score new_score
        from test_attempts ta
        join test_window_td_alloc_rules twtar 
          on ta.twtdar_id = twtar.id
        join test_attempt_question_responses taqr 
          on taqr.test_attempt_id = ta.id
        join test_questions tq 
          on tq.id = taqr.test_question_id 
          and taqr.is_invalid  = 0
        left join tw_exceptions_student_items tesi 
          on tesi.item_id = taqr.test_question_id 
          and tesi.test_window_id = twtar.test_window_id
          and tesi.uid = ta.uid
          and tesi.is_revoked = 0
        where ta.id in (?)
        group by taqr.id
      `);
  
      const stuItemExs = await dbRawRead(this.app, {test_window_id, uid}, `
        select item_id
             , tesi.created_on prorated_on
             , tesi.is_prorated is_prorated
             , tesi.is_score_override is_score_override
             , tesi.new_score new_score
        from tw_exceptions_student_items tesi 
        where tesi.test_window_id = :test_window_id
            and tesi.uid = :uid
            and tesi.is_revoked = 0
      `)
      const stuItemExRef:any = {};
      for (let stuItemEx of stuItemExs){
        stuItemExRef[+stuItemEx.item_id] = stuItemEx;
      }
  
      return {
        attempt_id,
        started_on,
        closed_on,
        test_form_cache,
        test_form_id,
        source_tf_id,
        test_design_id,
        tf_created_on,
        retrieved_on: (new Date()).toISOString(),
        formData: formData?.data,
        stuItemExRef,
        taqrRecords
      }
    }
    throw new Errors.BadRequest()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
