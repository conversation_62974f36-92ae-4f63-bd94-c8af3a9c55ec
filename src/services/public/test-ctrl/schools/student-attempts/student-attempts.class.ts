import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { dbEscapeString, dbRawRead } from '../../../../../util/db-raw';
import { OSSLT_QUESTIONNAIRE } from './model/questionnaire';
import { OSSLT_QUESTION_CONTEXT } from './model/testlets';
const _ = require('lodash');

const USE_RC_TABLE_FORMAT = true;

interface Data { }

interface ServiceOptions { }

const MCQ_OPTION_MAP = [
  'A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z',
  'AA','AB','AC','AD','AE','AF','AG','AH','AI','AJ','AK','AL','AM','AN','AO','AP','AQ','AR','AS','AT','AU','AV','AW','AX','AY','AZ',
  'BA','BB','BC','BD','BE','BF','BG','BH','BI','BJ','BK','BL','BM','BN','BO','BP','BQ','BR','BS','BT','BU','BV','BW','BX','BY','BZ',
  'CA','CB','CC','CD','CE','CF','CG','CH','CI','CJ','CK','CL','CM','CN','CO','CP','CQ','CR','CS','CT','CU','CV','CW','CX','CY','CZ',
  'DA','DB','DC','DD','DE','DF','DG','DH','DI','DJ','DK','DL','DM','DN','DO','DP','DQ','DR','DS','DT','DU','DV','DW','DX','DY','DZ',
  'EA','EB','EC','ED','EE','EF','EG','EH','EI','EJ','EK','EL','EM','EN','EO','EP','EQ','ER','ES','ET','EU','EV','EW','EX','EY','EZ',
  'FA','FB','FC','FD','FE','FF','FG','FH','FI','FJ','FK','FL','FM','FN','FO','FP','FQ','FR','FS','FT','FU','FV','FW','FX','FY','FZ',
];
const sortDndTargets = (targets: any[], isSimplified?: boolean) => { // todo: this needs better handling so that we can breakpoint caught exceptions
  targets.forEach((target: any) => {
    if(isSimplified){
      target._sID =  ('' + target.id).trim();
    } else {
      target.id = ('' + target.targetContext.id).trim()
    } 
  });
  return isSimplified ? _.sortBy(targets, '_sID') :  _.sortBy(targets, 'id');
};

export class StudentAttempts implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor(options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find(params?: Params): Promise<Data[] | Paginated<Data>> {
    // load list of attempts matching OEN
    // return this.pullResponses();
    return [];
  }

  async get(id: Id, params?: Params): Promise<Data> {
    // load list of responses for selected attempt id (also include item config and raw response in each case)
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }





  private async pullResponses(pens:Array<string|number>, options:any) {

    options = options || {};

    console.log('audit pre');
    const itemsToSplit:number[] = []; // OSSLT_QUESTIONNAIRE;

    let testWindows = [null];
    let testDesigns = [null];
    // testWindows, testDesigns
    // const records = await dbRawRead(this.app, [], `
    //   select 
    //     taqr.id as response_id, taqr.test_question_id as item_id, tq.question_label as item_label, taqr.response, taqr.response_raw, taqr.created_on as response_created_on, taqr.updated_on as response_updated_on,
    //     ta.id as test_attempt_id, sd.foreign_id as BrdMident, s.foreign_id as SchMident, sc.name as GroupingCode, sc.access_code as ClassAccessCode, um.value as StudentOEN, um2.value as Lang, twtar.slug as ComponentSlug, twtar.long_name as ComponentName,  ta.uid, ta.twtdar_id, ta.test_form_id, ta.created_on as attempt_created_on, ta.started_on as attempt_started_on, ta.is_closed, ta.closed_on 
    //   from test_attempts ta
    //   join school_class_test_sessions scts on scts.test_session_id = ta.test_session_id
    //   join test_sessions ts on ts.id = scts.test_session_id
    //   join school_classes sc on sc.id = scts.school_class_id
    //   join schools s on s.group_id = sc.schl_group_id
    //   join school_districts sd on sd.group_id = sc.schl_dist_group_id
    //   join test_window_td_alloc_rules twtar on twtar.id = ta.twtdar_id
    //   join test_attempt_question_responses taqr on taqr.test_attempt_id = ta.id
    //   join test_questions tq on taqr.test_question_id = tq.id
    //   left join user_metas um on um.uid = ta.uid and um.key_namespace = 'eqao_sdc' and um.key = 'StudentOEN'
    //   left join user_metas um2 on um2.uid = ta.uid and um2.key_namespace = 'eqao_dyn' and um2.key = 'Lang'
    //   where scts.slug IN ('OSSLT_OPERATIONAL')
    //   and ta.started_on is not null
    //   group by taqr.id
    //   LIMIT 5000000
    // ;`);

    const query = await this.getQueries(options);
    const records = await dbRawRead(this.app, [pens, options.filterItemIds], query);
    console.log('audit post', records.length);

    const responseTypes: { [key: string]: boolean } = {};
    
    const recordsExpanded: any[] = [];
    const submIndexRef = new Map();
    const trueItemMap = new Map();


    for (let i=0; i<records.length; i++){
      const response = records[i];
      if (i % 100 === 0) { console.log('mcq', i); }
      // MEET: only need the function below for the individual submission
      await this.processResponseRaw({
        response,
        responseTypes,
        itemsToSplit,
        trueItemMap,
        submIndexRef,
        recordsExpanded,
        options
      })
    }
    records.forEach((response, i) => {
    });

    const allItemsRef = new Map()
    const allItems: any[] = []
    const distractorReportRef = new Map();
    const distractorReports: any[] = [];
    recordsExpanded.forEach(record => {

      let itemInfo = allItemsRef.get(record.item_id);
      if (!itemInfo) {
        itemInfo = {
          ComponentName: record.ComponentName,
          item_label: record.item_label,
          item_id: record.item_id,
          response_type: record.response_type,
          num_correct: 0,
          num_nr: 0,
        }
        allItemsRef.set(record.item_id, itemInfo);
        allItems.push(itemInfo)
      }
      itemInfo.num_correct += record.is_correct;
      itemInfo.num_nr += record.is_nr;

      const key = [record.item_id, record.response].join('/')
      let report = distractorReportRef.get(key);
      if (!report) {
        // const item_id = trueItemMap.get(record.item_id)
        report = {
          ComponentName: record.ComponentName,
          module_number: record.module_number,
          module_name: record.module_name,
          item_label: record.item_label,
          item_id: record.item_id,
          response_type: record.response_type,
          score: record.score,
          response: record.response,
          is_correct: record.is_correct,
          is_nr: record.is_nr,
          count: 0,
        }
        distractorReportRef.set(key, report);
        distractorReports.push(report)
      }
      report.count++;
    })

    console.log('processing-post', records.length)
    return {
      allItems,
      records,
      recordsExpanded,
    }
    // return <any>allItems;
    // return <any> distractorReports;
    // return <any> recordsExpanded
    // return {
    //   records
    // };

  }

  async processResponseRaw(
    config:{
      response: {
        response_raw?:string, 
        item_id:number, 
        // optional
        // questionConfig?: any,        
        response?:string, // output
        is_correct?:boolean | null, // output
        score?:number | string, // output
        no_response?:boolean, // output
        auto_score?:boolean,
        review_score?: boolean,
        manual_score?: boolean,
        response_id?:number, // taqr id
        test_attempt_id?:number, // ta id
      }
      // optional
      responseTypes?: { [key: string]: boolean },
      itemsToSplit?:number[],
      trueItemMap?:Map<any, any>,
      submIndexRef?:Map<any, any>,
      recordsExpanded?:any[],
      options?: any
    }
  ){
    const {
      response,
      itemsToSplit, 
      responseTypes,
      trueItemMap,
      submIndexRef,
      recordsExpanded,
      options
    } = config;
    const {
      item_id, 
      response_raw, 
    } = response;

    const isSplitItem = ((itemsToSplit|| []).indexOf(+item_id) !== -1) // temp
    let responseText: string[];
    let responseTextStrict: string[];
    let subResponses: { responseType?: string, responseText: string[], responseTextStrict: string[], entry_id?: string }[] = [];
    let subResponse: any = {};
    const nextSubResponse = (responseType?:string) => {
      responseText = [];
      responseTextStrict = [];
      subResponse = { responseText, responseType }
      subResponses.push(subResponse);
    }
    nextSubResponse();
    let isAnyIncorrect = false;
    let isAnyAutoScore = false;
    let isAnyReviewScore = false;
    let isAnyManualScore = false;
    let isAnyFilled = false;
    let isAnyResponded = false;
    let score = 0;
    let isolateEntryId: string | undefined = undefined; // should be based on the question definition
    if (response_raw) {
      try {
        const response = JSON.parse(response_raw);
        const entryOrder = response.__meta && response.__meta.entryOrder?.length ? response.__meta.entryOrder : [];
        let entryIds = Object.keys(response).sort((a, b) => entryOrder.indexOf(+a) - entryOrder.indexOf(+b));
        // console.log("***************************", entryOrder, entryIds)
        entryIds = _.difference(entryIds, ['__meta', 'undefined'])
        entryIds.forEach((entry_id, entryIndex) => {
          if (isolateEntryId && (isolateEntryId != entry_id)) { return; }
          const entryData = response[entry_id];
          if (responseTypes){
            responseTypes[entryData.type] = true;
          }
          subResponse.entry_id = entry_id;
          if (subResponse.responseType) {
            subResponse.responseType = subResponse.responseType + ',' + entryData.type
          }
          else {
            subResponse.responseType = entryData.type
          }

          this.processEntryResponseText(entryData, false, responseText, subResponse, nextSubResponse, entry_id, entryIndex,  entryIds, isSplitItem, options);

          if (entryData.isFilled) {
            isAnyFilled = true;
          }

          if (entryData.isResponded){
            isAnyResponded = true;
          }

          // for all auto scored
          if (entryData.scoring_type === 'AUTO' || entryData.scoring_type === 'REVIEW') {
            
            if(entryData.scoring_type === 'AUTO') isAnyAutoScore = true;
            else if(entryData.scoring_type === 'REVIEW') isAnyReviewScore = true;

            score += entryData.score;
            if (!entryData.isCorrect && entryData.isCorrect !== undefined) { // optional MCQ has isCorrect undefined
              isAnyIncorrect = true;
            }
          } else if (entryData.scoring_type === 'MANUAL') {
            isAnyManualScore = true
          }

          if (isSplitItem) {
            if (entryIndex + 1 < entryIds.length) {
              nextSubResponse();
            }
          }

        })
      }
      catch (e) {
        console.error('processing error')
      }
    }
    const subRecords:any[] = [];
    subResponses.filter(sr => sr.entry_id || sr.responseText.length || subResponses.length === 1 || sr.responseType !== 'select_table').forEach((subResponse, idx) => {
      const { responseText, entry_id } = subResponse;
      // if (!entry_id || entry_id == 'undefined') { return; }

      if (responseText.length) {
        response.response = responseText.join(';');
      }
      else {
        response.response = 'NA';
      }
      // if give raw (based on param)
      response.response_raw = undefined;
      // put a mapping here too
      response.is_correct = isAnyManualScore ? null : (!isAnyIncorrect && isAnyFilled);
      response.score = (isAnyAutoScore || isAnyReviewScore) ? score : 'NA';
      response.auto_score = isAnyAutoScore;
      response.review_score = isAnyReviewScore;
      response.manual_score = isAnyManualScore
      response.no_response =   !isAnyResponded  // !isAnyFilled;
      const itemModuleMeta = OSSLT_QUESTION_CONTEXT['' + response.item_id] || OSSLT_QUESTION_CONTEXT[+response.item_id] || {}
      let response_id = 'r' + (response.response_id || '')
      let item_id = 'i' + response.item_id
      if (subResponses.length > 1) {
        item_id += '.e' + entry_id
        response_id += '.e' + entry_id
      }
      if (trueItemMap){
        trueItemMap.set(item_id, response.item_id)
      }

      let isCorrect = response.is_correct ? 1 : response.is_correct === null ? null : 0;

      const subRecord = {
        ...response,
        ...itemModuleMeta,
        response_id,
        test_attempt_id: response.test_attempt_id,
        item_id,
        response_type: subResponse.responseType,
        score: response.score,
        response: response.response,
        is_correct: isCorrect,
        is_nr: response.no_response ? 1 : 0,
        // is_na: 0,
      }
      const submissionKey = subRecord.test_attempt_id + ';' + subRecord.item_id;
      subRecords.push(subRecord)
      if (submIndexRef && recordsExpanded){
        if (submIndexRef.has(submissionKey)) {
          const submIndex = submIndexRef.get(submissionKey);
          recordsExpanded[submIndex] = subRecord;
        }
        else {
          submIndexRef.set(submissionKey, recordsExpanded.length);
          recordsExpanded.push(subRecord);
        }
      }
    });
    return subRecords;
  }

  processEntryResponseText(entryData:any, isDeepMcq:boolean, responseText:string[] = [], subResponse:any = {}, nextSubResponse:(responseType?:string)=>void, entry_id:string='-1', entryIndex:number=-1,  entryIds:string[]=[], isSplitItem?:boolean, options?:any){
    options = options || {};
    if (entryData.type === 'mcq') {
      // Note: for MCQ isCorrect is undefined for optional responses
      if (entryData.selections && entryData.selections.length) { // && entryData.isCorrect !== undefined // todo:clarify why mcq is being excluded for unsocred items

        let values;
        if (isDeepMcq){
          values = entryData.selections.map((selection: any) => {
            if (selection.content){
              return selection.content;
            }
            else if(selection.advancedList){
              const strs:string[] = [];
              selection.advancedList.forEach((el:any) => {
                if (el.elementType === 'image'){
                  if (el.caption) {
                    strs.push(el.caption);
                  }
                  if (el.subtexts){
                    el.subtexts.forEach((elst:any) => {
                      if (elst.elementType === 'image_subtext'){
                        if (elst.text) {
                          strs.push(elst.text)
                        };
                      }
                    })
                  }
                }
                else if (el.elementType === 'text'){
                  if (el.caption){
                    strs.push(el.caption);
                  }
                }
              })
              if (strs.length === 0){
                return 'COULD_NOT_PROCESS'
              }
              return strs.join(', ')
            }
          })
        }
        else {
          values = entryData.selections.map((selection: any) => MCQ_OPTION_MAP[selection.i]).sort();
        }

        if (isSplitItem) {
          values.forEach((row: string, rowIndex: number) => {
            responseText.push(row);
            subResponse.entry_id = entry_id + '.r' + rowIndex;
            if (entryIndex + 1 < entryIds.length || rowIndex + 1 < values.length) {
              if (typeof nextSubResponse == 'function'){
                nextSubResponse();
              }
            }
          })
        }
        else {
          responseText.push(values.join(','));
        }
      }
      else {
        responseText.push('');
      }
    }
    ////////////////////////
    ////////////////////////
    ////////////////////////
    else if (entryData.type == 'mic') {
      let val = entryData.url;
      if (options.stripLinks){
        val = (val || '').split('https://s3.ca-central-1.amazonaws.com/authoring.mathproficiencytest.ca').join('');
      }
      responseText.push(val);
    }
    else if (entryData.type == 'moveable_dnd') {
      // console.log (entryData.type, entryData)
      if (entryData.concatVal){
        responseText.push(entryData.concatVal);
      }
      else {
        const simplifiedEntries: string[] = [];
        try {
          const targets = sortDndTargets(entryData.simplifiedStateTargets, true); //entryData.targets
          targets.forEach((target: any) => {
            let val;
            if (target.contents && target.contents[0]) {
              // val = ('' + target.contents[0].ref.id).trim()
              val = ('' + target.contents[0].id).trim()

              if (entryData.useKeyIdOrOrderInFormatResponse && target.contents[0].key_id){
                val = ('' + target.contents[0].key_id).trim()
              }
            }
            if (!val) {
              val = '(EMPTY)'
            }
            simplifiedEntries.push(target.id + '=>' + val);
          })
        }
        catch (e) { }
        responseText.push(simplifiedEntries.join(', '));
      }
    }
    ////////////////////////
    ////////////////////////
    ////////////////////////
    else if (entryData.type == 'Grouping' || entryData.type == 'grouping') {
      // console.log (entryData.type, entryData)
      const simplifiedEntries: string[] = [];
      try {
        const targets = sortDndTargets(entryData.targets);
        targets.forEach((target: any) => {
          let val;
          if (target.contents && target.contents.length > 0) {
            const options = target.contents.map((option: any) => {
              const el = option.ref.element;
              if (el.elementType === 'text') {
                return el.caption
              }
              else {
                if(option.ref.key_id) return('' + option.ref.key_id).trim()
                return ('' + option.ref.id).trim()
              }
            });
            val = `[${ entryData['isTargetsOrdered'] ? options.join('; ') : options.sort().join('; ')}]`
          }
          if (!val) {
            val = '(EMPTY)'
          }
          simplifiedEntries.push(target.id + '=>' + val);
        })
      }
      catch (e) { }
      responseText.push(simplifiedEntries.join(', '));
    }
    ////////////////////////
    ////////////////////////
    ////////////////////////
    else if (entryData.type === 'select_table') {
      let simpleGrid:string[] = [];
      let isSingleCol = true;
      let isDoubleCol = true;
      entryData.checkMarks.forEach((row:any[]) => {
        if (row.length !== 1){ isSingleCol = false }
        if (row.length !== 2){ isDoubleCol = false }
      })
      if (isSingleCol && !isSplitItem){
        let selections:string[] = [];
        if (USE_RC_TABLE_FORMAT){
          entryData.checkMarks.forEach((row:any[], rowIndex:number) => {
            if (row[0].value){ selections.push(`R${rowIndex+1}`) }
          })
          responseText.push(selections.join(';'));
        }
        else{
          entryData.checkMarks.forEach((row:any[], rowIndex:number) => {
            if (row[0].value){
              selections.push(MCQ_OPTION_MAP[rowIndex])
            }
          })
          responseText.push(selections.join(','));
        }
      }
      else {
        entryData.checkMarks.forEach((row:any[], rowIndex:number) => {
          if (isDoubleCol && !USE_RC_TABLE_FORMAT){
            let selection = 'NR'
            if (row[0].value){ selection = 'Left' }
            if (row[1].value){ selection = 'Right' }
            simpleGrid.push(selection);
          }
          else{
            const selectedCells:string[] = []
            row.forEach((cell:any, i:number) => {
              if (cell.value){
                if (USE_RC_TABLE_FORMAT){
                  selectedCells.push(`R${rowIndex+1}C${i+1}`)
                }
                else{
                  selectedCells.push(MCQ_OPTION_MAP[i])
                }
              }
            })
            simpleGrid.push( selectedCells.join(',') );
          }
        })
        if (isSplitItem){
          simpleGrid.forEach((row:string, rowIndex:number) => {
            responseText.push(row);
            subResponse.entry_id = entry_id+'.r'+rowIndex;
            if (entryIndex + 1 < entryIds.length || rowIndex + 1 < simpleGrid.length){
              nextSubResponse('select_table');
            }
          })
        }
        else{
          responseText.push(simpleGrid.join(';'));
        }
      }
    }
    ////////////////////////
    ////////////////////////
    ////////////////////////
    else if (entryData.type === 'input-longtext') {
      let val = entryData.str;
      if (options.stripHtml){
        val = val ? val.replace(/(<([^>]+)>)/gi, "") : '';
      }
      responseText.push(val);
    }
    else if (entryData.type === 'input-shorttext') {
      // Not extracting where scoring is disabled (used as isOptional)
      if(!entryData.isScoringDisabled){
        let val = entryData.str;
        responseText.push(val);
      }
    }
    else if (entryData.type === 'input-number') {
      // Not extracting where scoring is disabled (used as isOptional)
      if(!entryData.isScoringDisabled){
        let val = entryData.value;
        // #TODO: strip formatting 
        responseText.push(val);
      }
    }
    else if (entryData.type === 'input-fraction') {
      
      let wholenumber = entryData.wholenumber;
      let numdenom = `[${entryData.numerator}/${entryData.denominator}]`
      let ans = '';

      if(wholenumber && numdenom) {
        ans = `${wholenumber} ${numdenom}`;
      } else if(wholenumber){
        ans = wholenumber;
      } else if(numdenom){
        ans = numdenom
      }

      responseText.push(ans);
    }
    else if (entryData.type === 'input-ratio') {
      if(!entryData.isScoringDisabled) {
        let formattedVal = entryData.terms.join(':');
        responseText.push(formattedVal);
      }
    }
    else if (entryData.type === 'input-algebra') {
      if(entryData.latex) {
        let ans = entryData.latex;
        responseText.push(ans);
      }
    }
    else if (entryData.type === 'order') {
      if (entryData.answers.length) {
        const allocations = entryData.answers.map((arr: any[]) => {
          if(!arr.length) return '(EMPTY)';
          return arr.map((o: any) => {
            let content = '(EMPTY)';
            const oCaption = o['caption']
            const oContent = o['content']
            if(oCaption && oCaption != null) content = o.caption
            else if(oContent && oContent != null) content = _.isObject(o.content) ? o.key_id  : o.content
            
            if (entryData.useKeyIdOrOrderInFormatResponse){
              if (o.initOrder && o.initOrder != '') content = o.initOrder; // Overwrites the content if the option has order configured
              if(o.key_id && o.key_id != '') content = o.key_id; // Overwrites the content if the option has key_id configured  
            }
            
            if(o.isReadOnly) content = `(${content})`
            return content 
          }).join(',');
        })
        responseText.push(allocations.join(', '));
      }
      else {
        responseText.push('UNHANDLED_ORDER_FORMAT');
      }
    }
    ////////////////////////
    ////////////////////////
    ////////////////////////
    else if (entryData.type === 'insertion') {
      if (entryData.targets) {
        const allocations: string[] = [];
        entryData.targets.forEach((candidateTarget: any) => {
          if (candidateTarget.isTarget) {
            let content = '(EMPTY)'
            const contentRecord = candidateTarget.contents[0]
            if (contentRecord) {
              content = contentRecord.ref.element.caption || contentRecord.ref.id

              if (entryData.useKeyIdOrOrderInFormatResponse && contentRecord.ref.key_id){ // Use the key_id if useKeyIdOrOrderInFormatResponse flag is on
                content = contentRecord.ref.key_id;
              }
            }

            // Origanally, the content is wrapped within a square bracket, but ABED doesn't want them. 
            // We could include the site context within the entry data so that the brackets can be selectively
            // added, but not sure if it's a good idea to have that information within the question entry data.   

            // allocations.push(`${candidateTarget.ctx.id}=>[${content}]`)
            allocations.push(`${candidateTarget.ctx.id}=>${content}`) 
          }
        })
        responseText.push(allocations.join(' '));
      }
      else {
        responseText.push('UNHANDLED_INSERTION_FORMAT');
      }
    }
    ////////////////////////
    ////////////////////////
    ////////////////////////
    else if (entryData.type === 'custom_interaction' ){

      if(entryData.subtype === 'GRID_DND'){
        // optionStates: {currX, currY, id: option.id}
        const dndRes = entryData.optionStates?.map((option: { id: any; currX: any; currY: any; }) => {
          if(!option.currX || !option.currY) return `${option.id} => "NA"`
          return `${option.id} => (${option.currX}, ${option.currY})`
        }) 
        responseText.push(dndRes.join(','));
      } 

      else if(entryData.subtype === 'GRID_FILL'){
        responseText.push(entryData.value);
      } 
      
      else if(entryData.subtype === 'GRID_SCATTER_PLOT'){
        const scatterRes: string[] = [];
        if(!_.isEmpty(entryData.value)){
          for (let [key, value] of Object.entries(entryData.value)) {
            scatterRes.push(key);
          }
          responseText.push(scatterRes.join(','));
        } else {
          responseText.push("NA")
        }
      } 

      // Currently disabled as it being used with Input compnant both extract same response (repetition)
      else if(entryData.subtype === 'PIE_CHART'){

          // values: {[id:string] : value}
          let PieValues: string[] = []

          if(entryData.values){
            for (let [id, value] of Object.entries(entryData.values)) {
              PieValues.push(`${id} => ${value}`);
            }
            responseText.push(`[${PieValues.join(',')}]`);
          } else { 
            responseText.push("NA")
          }
      } 
      
      else if(entryData.subtype === 'ROTATE_SPRITE'){
          // value
          responseText.push((entryData.value ?? "NA"))
      } 
      
      else if(entryData.subtype === 'SCALE_RADIUS'){
          // radius
          responseText.push((entryData.radius ?? "NA"))
      } 
      
      else if(entryData.subtype === 'SLIDER'){
          // value
          responseText.push((entryData.value ?? "NA"))
      }
    }
    ////////////////////////
    ////////////////////////
    ////////////////////////
    else if (entryData.type === 'input') {
      console.log('known unhandled type', entryData.type)
    }
    else if (entryData.type === 'virtual_tools') {
      // const data = JSON.stringify(entryData.data);
      // responseText.push((data ?? "NA"))
      // Don't include the raw JSON data for the virtual tool in the formatted response
    }
    else if((<string>entryData.type).includes('diagram-interactive')) {
      responseText.push(entryData.formattedResponse ?? "NA")
    }
    else if(entryData.type === 'input-scientific-notation') {
      responseText.push(entryData.expression ?? "NA")
    } 
    else if((<string>entryData.type).includes('diagram-interactive')) {
      responseText.push(entryData.formattedResponse ?? "NA")
    }
    ////////////////////////
    ////////////////////////
    ////////////////////////
    else {
      console.log('unhandled type', entryData.type)
    }

    return responseText
  }



  async create(data: Data, params?: Params): Promise<Data> {
    const {pens, options} = <any> data;
    return this.pullResponses(pens, options);
    // throw new Errors.MethodNotAllowed();
  }

  async update(id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch(id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove(id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async getQueries(options: any) {
    if(options.host){
      switch(options.host){
        case 'IS_NBED':
          return await this.getNBED(options)
        case 'IS_SMCS':
          return this.getSMCS()
        default:
          return this.getBCED()
      }      
    }
    return this.getBCED()

  }

  async getBCED() {
    return ` 
      select taqr.id as response_id
      , taqr.test_question_id as item_id
      , tq.question_label as item_label
      , taqr.response
      , taqr.response_raw
      , taqr.created_on as response_created_on
      , taqr.updated_on as response_updated_on
      , ta.id as test_attempt_id
      , sd.foreign_id as BrdMident
      , s.foreign_id as SchMident
      , sc.name as GroupingCode
      , sc.access_code as ClassAccessCode
      , um.value as StudentPEN
      , twtar.type_slug as AssessmentCode
      , twtar.form_code as FormCode
      , twtar.slug as ComponentSlug
      , twtar.long_name as ComponentName
      , ta.uid
      , ta.twtdar_id
      , ta.test_form_id
      , ta.created_on as attempt_created_on
      , ta.started_on as attempt_started_on
      , ta.is_closed
      , ta.closed_on 
      -- , tq.config as questionConfig -- heavy, should avoid
      from test_attempts ta
    join user_metas um 
        on um.uid = ta.uid 
        and um.key = 'StudentPEN'
        and um.value in (?)
        and ta.started_on is not null
    join school_class_test_sessions scts on scts.test_session_id = ta.test_session_id
    join test_sessions ts on ts.id = scts.test_session_id
    join school_classes sc on sc.id = scts.school_class_id
    join schools s on s.group_id = sc.schl_group_id
    join school_districts sd on sd.group_id = sc.schl_dist_group_id
    join test_window_td_alloc_rules twtar on twtar.id = ta.twtdar_id
    join test_attempt_question_responses taqr on taqr.test_attempt_id = ta.id
    join test_questions tq on taqr.test_question_id = tq.id
    where taqr.is_invalid = 0      
    group by taqr.id
    LIMIT 5000000
    ;`
  }

  async getNBED (options: any) {
    return `
      select /*+ MAX_EXECUTION_TIME(1440000000)*/
            taqr.id as response_id
           , taqr.test_question_id as item_id
           , tq.question_label as item_label
           , taqr.response
           , taqr.response_raw
           , taqr.created_on as response_created_on
           , taqr.updated_on as response_updated_on
           , ta.id as test_attempt_id
           , sd.foreign_id as BrdMident
           , s.foreign_id as SchMident
           , sc.name as GroupingCode
           , sc.access_code as ClassAccessCode
           , um.value as StudentPEN
           , twtar.type_slug as AssessmentCode
           , twtar.form_code as FormCode
           , twtar.slug as ComponentSlug
           , twtar.long_name as ComponentName
           , ta.uid
           , ta.twtdar_id
           , ta.test_form_id
           , ta.created_on as attempt_created_on
           , ta.started_on as attempt_started_on
           , ta.is_closed
           , ta.closed_on 
           -- , tq.config as questionConfig -- heavy, should avoid
           , u.first_name
           , u.last_name
           ${ options.includeCrScoreFromMarkingSession ? `, mso.value as cr_score` : '' }
           ${ options.isMultiScale ? `, msp.skill_code as scale_slug`: ''}           
      from test_attempts ta
      join users u on u.id = ta.uid
      join user_metas um 
            on um.uid = ta.uid 
            and um.key = 'NBED_UserId'
            and um.value in (?)
            and ta.started_on is not null
      join users u on u.id = um.uid
      join school_class_test_sessions scts on scts.test_session_id = ta.test_session_id
      join test_sessions ts on ts.id = scts.test_session_id
      join school_classes sc on sc.id = scts.school_class_id
      join schools s on s.group_id = sc.schl_group_id
      join school_districts sd on sd.group_id = sc.schl_dist_group_id
      join test_window_td_alloc_rules twtar on twtar.id = ta.twtdar_id
      ${ options.filterAsmtSlug ? ` and twtar.type_slug like ${ await dbEscapeString(options.filterAsmtSlug, true) } ` : ''}
      join test_attempt_question_responses taqr on taqr.test_attempt_id = ta.id
      join test_questions tq on taqr.test_question_id = tq.id      
      ${ options.includeCrScoreFromMarkingSession ? `
        join marking_claimed_batch_responses mcbr on mcbr.taqr_id = taqr.id 
        join marking_response_scores mrs on mcbr.id = mrs.batch_response_id
        join marking_score_profile_options mspo on mrs.score_option_id = mspo.id and mspo.is_revoked = 0
        left join marking_score_options mso on mspo.score_option_id = mso.id 
        `: ''
      }
      ${ options.isMultiScale ? `
        join marking_window_items mwi on mwi.id = mcbr.window_item_id 
        join marking_score_profiles msp on msp.id = mwi.score_profile_id
      ` : '' 
      }
      where taqr.is_invalid = 0 
      ${ options.filterItemIds ? ` and taqr.test_question_id in (?)` : ''}
      group by taqr.id ${ options.isMultiScale ? `, mcbr.window_item_id` : '' }
      LIMIT 5000000;
    `
  }

  async getSMCS(){
    return `
      select taqr.id as response_id
            , taqr.test_question_id as item_id
            , tq.question_label as item_label
            , taqr.response
            , taqr.response_raw
            , taqr.created_on as response_created_on
            , taqr.updated_on as response_updated_on
            , ta.id as test_attempt_id
            , ta.uid
            , ta.twtdar_id
            , ta.test_form_id
            , ta.created_on as attempt_created_on
            , ta.started_on as attempt_started_on
            , ta.is_closed
            , ta.closed_on 
            -- , tq.config as questionConfig -- heavy, should avoid
            , u.first_name
            , u.last_name
            , u.contact_email as StudentPEN
        from test_attempts ta
        join users u on u.id = ta.uid
          and u.contact_email in (?)
          and ta.started_on is not null
        join user_metas um 
              on um.uid = ta.uid 
              and ta.started_on is not null      
        join test_attempt_question_responses taqr on taqr.test_attempt_id = ta.id
        join test_questions tq on taqr.test_question_id = tq.id
      group by taqr.id
        LIMIT 5000000;
    `
  }

  
}
