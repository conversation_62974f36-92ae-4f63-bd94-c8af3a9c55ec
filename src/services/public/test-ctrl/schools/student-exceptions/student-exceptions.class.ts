import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { dbDateNow } from '../../../../../util/db-dates';
import { dbRawRead, dbRawReadSingle } from '../../../../../util/db-raw';
import { currentUid } from '../../../../../util/uid';


export enum STU_EX_OVERRIDES {
  PENDED = 'PENDED',
  INDIVIDUAL = 'INDIVIDUAL',
  WITHHOLD = 'WITHHOLD',
  COURSE_REMAP = 'COURSE_REMAP',
  LOS_CONFIRM = 'LOS_CONFIRM'
}

interface Data {}

interface ServiceOptions {}

export class StudentExceptions implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const {test_window_id} = params.query;
      const records = await dbRawRead(this.app, {test_window_id}, `
        select 
            tes.id tes_id
          , tes.test_window_id
          , tes.uid
          , tes.category
          , um.value uid_stu_gov_id
          , tes.student_foreign_id stu_gov_id
          , tes.twtar_type_slug as assessment_code
          , tes.action_config
          , tes.is_pended
          , tes.created_on
          , tes.created_by_uid
          , tes.is_revoked
          , tes.revoked_on
          , tes.revoked_by_uid
          , tes.notes
          , tes.revoke_notes
          , tes.ric_id
          , tes.test_attempt_id
          , ta.id ta_id
          , ta.is_invalid  ta_is_invalid
          , ta.started_on ta_started_on
          , sd.foreign_id ta_sd_code
          , s.foreign_id ta_sa_code -- tes.school_group_id
          , sc.id ta_sc_id -- tes.school_class_id
          -- , scts.slug assessment_code
        from tw_exceptions_students tes 
        left join test_attempts ta 
          on ta.id = tes.test_attempt_id
          and ta.uid = tes.uid 
          and ta.is_invalid = 1
        left join school_class_test_sessions scts
          on scts.test_session_id = ta.test_session_id 
        left join school_classes sc 
          on sc.id = scts.school_class_id 
        left join schools s 
          on s.group_id = sc.schl_group_id 
        left join school_districts sd 
          on sd.group_id = s.schl_dist_group_id
        left join user_metas um 
          on um.uid = tes.uid 
          and um.key = 'StudentIdentificationNumber'
        where tes.test_window_id = :test_window_id
        -- group by tes.id
      `)
      return records;
    }
    throw new Error();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    // const uid = id;
    if (params && params.query){
      const {uid, test_window_id} = params.query;
      const records = await this.getStudentExceptionsByTwAndUids(test_window_id, [uid], true);
      return {records}
    }
    throw new Error();
  }

  async getStudentExceptionsByTwAndUids(test_window_id:number, uids:number[], isRevokedIncluded:boolean){
    if (uids.length){
      return dbRawRead(this.app, {uids, test_window_id}, `
        select 
            id
          , uid
          , test_window_id
          , category
          , twtar_type_slug
          , is_pended
          , created_on
          , created_by_uid
          , ric_id
          , notes
          , is_revoked
          , revoked_on
          , revoked_by_uid
          , revoke_notes
          , test_attempt_id
        from tw_exceptions_students tes 
        where tes.test_window_id = :test_window_id
          and uid in (:uids)
          ${isRevokedIncluded ? 'and is_revoked = 0' : '' /*   -- we deliberately include all revoked exceptions nad leave the filtering to the front end */}
      `)
    }
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (params){
      const created_by_uid = await currentUid(this.app, params);
      const {test_window_id, uids, uid, is_pended, category, ric_id, notes, test_attempt_id} = <any> data;
      const studentUids = uids || [uid];
      const createdRecords:any[] = [];
      const retrievedRecords:any[] = [];
      let twtar_type_slug: string | null = null;
      if(test_attempt_id){
        const twtar = await dbRawReadSingle(this.app, {test_attempt_id}, `
          select twtar.id
          , twtar.type_slug 
          from test_attempts ta 
          join test_window_td_alloc_rules twtar 
            on ta.twtdar_id = twtar.id 
          where ta.id = :test_attempt_id;
          `)
        if(twtar){
          twtar_type_slug = twtar.type_slug;
        }
      }
      for (let studentUid of studentUids){
        const existingRecords = <any[]> await this.app.service('db/write/tw-exceptions-students').find({ paginate: false, query: {
          test_window_id, 
          uid: studentUid, 
          is_pended, 
          category: (category==undefined) ? null : category,
          test_attempt_id: (test_attempt_id==undefined) ? null : test_attempt_id,
          is_revoked: 0,
        }});
        if (existingRecords.length){
          retrievedRecords.push(existingRecords[0]); // assuming first record is enough, we should usually only have one that matches this criteria
        }
        else {
          const createdRecord = await this.app.service('db/write/tw-exceptions-students').create({
            test_window_id, 
            uid: studentUid, 
            ric_id, 
            notes,
            is_pended, 
            category,
            created_by_uid,
            twtar_type_slug,
            test_attempt_id: (test_attempt_id==undefined) ? null : test_attempt_id,
          })
          createdRecords.push(createdRecord)
        }
      }
      return {createdRecords, retrievedRecords}
    }
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    if (params && id){
      const revoked_by_uid = await currentUid(this.app, params);
      return this.app.service('db/write/tw-exceptions-students').patch(id, {
        is_revoked: 1,
        revoked_by_uid,
        revoked_on: dbDateNow(this.app),
      })
      return { id };
    }
    throw new Errors.BadRequest()
  }
}
