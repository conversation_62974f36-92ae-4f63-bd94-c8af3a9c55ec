import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { dbRawReadReporting } from '../../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class StudentResponses implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    /*
    # Relevant References: 

    ## Standard Student Question Save
    src/services/public/student/session-question/session-question.class.ts
    aggregateScoreAndWeightFromResponseRaw

    ## Service for Re-processing Student Saves 
    src/services/public/support/student-taqr/student-taqr.class.ts
    recalcTaqr
    */

    const {taqr_ids, isRawIncluded} = <any> data;
    const responses:any[] = []; // used to send back the responses
    const taqrs = await dbRawReadReporting(this.app, {taqr_ids}, `
      select taqr.id response_id
           , taqr.test_question_id item_id
           , taqr.response_raw
      from test_attempt_question_responses taqr
      where taqr.id in (:taqr_ids)
    `);
    for (let taqr of taqrs){
      const {response_id, item_id, response_raw} = taqr
      const subRecord: any = await this.app
        .service("public/test-ctrl/schools/student-attempts")
        .processResponseRaw({
          response: {response_id, item_id, response_raw},
          responseTypes: <{ [key: string]: boolean }>{},
        });
      
      const formatted_response = subRecord.map((record: any) => record.response).join(";");
      const formatted_response_strict = subRecord.map((record: any) => record.formatted_response_strict).join(";");
      const response_type = subRecord.map((record: any) => record.response_type).join(";");
      
      let is_nr = 1;
      let score = 0;
      // let isNrScoreDiscrep = false;

      subRecord.map((record: any) => {
        if (record?.no_response == 0){
          is_nr = 0;
        }
        if (record.score && record.score != 'NA'){
          score += record?.score
        }
      });

      responses.push({
        response_id,
        formatted_response,
        formatted_response_strict,
        response_type,
        is_nr,
        score,
        response_raw: isRawIncluded ? response_raw : undefined
      })
      
    }

    return responses;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
