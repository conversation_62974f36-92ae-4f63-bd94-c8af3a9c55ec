// Initializes the `public/test-ctrl/schools/student-responses` service on path `/public/test-ctrl/schools/student-responses`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { StudentResponses } from './student-responses.class';
import hooks from './student-responses.hooks';

// Add this service to the service type index
declare module '../../../../../declarations' {
  interface ServiceTypes {
    'public/test-ctrl/schools/student-responses': StudentResponses & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-ctrl/schools/student-responses', new StudentResponses(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-ctrl/schools/student-responses');

  service.hooks(hooks);
}
