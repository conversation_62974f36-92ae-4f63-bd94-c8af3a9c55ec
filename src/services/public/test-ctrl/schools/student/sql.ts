export const SQL_CAEC_CAND_LOOKUP = (AND_PARAM_CLAUSES: string[], excludeNotStartedAttempt?: string ) => `
-- case in email uid = 721496 and ts_id = 102319
select
       -- student info
               ur.uid
             , um_oen.value ?
             , u.first_name
             , u.last_name
             , cb.foreign_id sd_code
             , cb.title->>'$.en' sd_name
--              , s.foreign_id s_code
--              , s.name s_name
             , cb.foreign_id s_name
             , ur.created_on
--              , sc.access_code
             , ur.is_revoked
             , ur.revoked_on
             , um2.value "DOB"
        -- attempt info
             , attempts_info.ta_id
             , attempts_info.test_form_id
             , attempts_info.sc_id
             , attempts_info.sc_name
             , attempts_info.lang
             , attempts_info.ts_id
             , attempts_info.is_closed
             , attempts_info.ts_test_window_id test_window_id
             , attempts_info.ts_test_window_id
             , attempts_info.type_slug
             , attempts_info.slug
             , attempts_info.is_sample
             , attempts_info.assessment_name
             , attempts_info.teacher_uid
             , attempts_info.twtar_order
        from user_metas um_oen
        join user_roles ur
          on ur.uid = um_oen.uid
          and um_oen.key =  ?
          and um_oen.key_namespace in (?) -- account for all possible ASN key_namespace
          and ur.role_type = 'mpt_applicant'
          and ur.is_revoked = 0
        join users u
          on u.id = ur.uid
        join certification_bodies cb
        	on cb.group_id = ur.group_id
          -- and cb.is_sample = 0 -- exclude samples here
--     	join institutions i on i.jurisdiction_id = cb.id
--         join school_classes sc
--           on sc.group_id = ur.group_id
--         join school_semesters ss
--           on ss.id = sc.semester_id
--         join schools s
--           on s.group_id = sc.schl_group_id
--         join school_districts sd
--           on sd.group_id = s.schl_dist_group_id
        left join user_metas um2 on um2.uid = um_oen.uid and um2.key = 'DOB'
        left join
        (
           select
              ta.id ta_id,
              ta.test_form_id,
              tf.lang,
              ta.test_session_id ts_id,
              ta.is_closed,
              ts.test_window_id ts_test_window_id,
              twtar.type_slug,
              twtar.slug,
              twtar.is_sample,
              twtar.long_name assessment_name,
              ur_invig.uid "teacher_uid",
              twtar.order "twtar_order",
              ta.uid,
              ta.test_session_id,
              i.name sc_name,
              i.id sc_id
            from test_attempts ta
            join test_sessions ts
              on ts.id = ta.test_session_id
            join institutions i
            	on i.group_id = ts.instit_group_id
            join user_roles ur_invig
              on ur_invig.group_id = ts.test_Session_group_id and ur_invig.is_revoked = 0
              and ur_invig.role_type = 'mpt_test_admin_invig'
            join test_forms tf
              on tf.id = ta.test_form_id
            join test_window_td_alloc_rules twtar
              on twtar.id = ta.twtdar_id
            where twtar.test_window_id = ?
--             ${excludeNotStartedAttempt === 'true' ? `and ta.started_on is not null` : ``}
        ) as attempts_info on attempts_info.uid = u.id
        WHERE 1 = 1
           ${AND_PARAM_CLAUSES.join('\n')}
        group by ur.uid, ta_id
        order by u.last_name
               , u.first_name
               , attempts_info.ta_id
               , attempts_info.twtar_order
        limit 200
`;

export const SQL_ABED_CAND_LOOKUP = (AND_PARAM_CLAUSES: string[], excludeNotStartedAttempt?: string ) =>  `
        select
        -- student info
               ur.uid
             , um_oen.value ?
             , u.first_name
             , u.last_name
             , sd.foreign_id sd_code
             , sd.name sd_name
             , s.foreign_id s_code
             , s.name s_name
             , ur.created_on
             , sc.access_code
             , ur.is_revoked
             , ur.revoked_on
             , um2.value "DOB"
        -- attempt info
             , attempts_info.ta_id
             , attempts_info.test_form_id
             , attempts_info.sc_id
             , attempts_info.sc_name
             , attempts_info.lang
             , attempts_info.ts_id
             , attempts_info.is_closed
             , attempts_info.ts_test_window_id test_window_id
             , attempts_info.ts_test_window_id
             , attempts_info.type_slug
             , attempts_info.slug
             , attempts_info.is_sample
             , attempts_info.assessment_name
             , attempts_info.teacher_uid
             , attempts_info.twtar_order
        from user_metas um_oen
        join user_roles ur
          on ur.uid = um_oen.uid
          and um_oen.key = ?
          and um_oen.key_namespace in (?) -- account for all possible ASN key_namespace
        join users u
          on u.id = ur.uid
        join school_classes sc
          on sc.group_id = ur.group_id
        join school_semesters ss
          on ss.id = sc.semester_id
        join schools s
          on s.group_id = sc.schl_group_id
        join school_districts sd
          on sd.group_id = s.schl_dist_group_id
          /* and sd.is_sample = 0 */ -- exclude samples here
        left join user_metas um2 on um2.uid = um_oen.uid and um2.key = 'DateOfBirth'
        left join
        (
            select
              ta.id ta_id,
              ta.test_form_id,
              tf.lang,
              ta.test_session_id ts_id,
              ta.is_closed,
              ts.test_window_id ts_test_window_id,
              twtar.type_slug,
              twtar.slug,
              twtar.is_sample,
              twtar.long_name assessment_name,
              ur_teachers.uid "teacher_uid",
              twtar.order "twtar_order",
              ta.uid,
              scts.test_session_id,
              sc_2.name sc_name,
              sc_2.id sc_id
            from test_attempts ta
            left join school_class_test_sessions scts
              on scts.test_session_id = ta.test_session_id
            left join school_classes sc_2
              on sc_2.id = scts.school_class_id
            left join test_sessions ts
              on ts.id = ta.test_session_id
            left join user_roles ur_teachers
              on ur_teachers.group_id = sc_2.group_id and ur_teachers.is_revoked = 0
              and ur_teachers.role_type = 'schl_teacher'
            left join test_forms tf
              on tf.id = ta.test_form_id
            left join test_window_td_alloc_rules twtar
              on twtar.id = ta.twtdar_id
            where twtar.test_window_id = ?
            ${excludeNotStartedAttempt === 'true' ? `and ta.started_on is not null` : ``}
        ) as attempts_info on attempts_info.uid = u.id
        WHERE 1 = 1
          ${AND_PARAM_CLAUSES.join('\n')}
        group by ur.uid, ta_id
        order by u.last_name
               , u.first_name
               , attempts_info.ta_id
               , attempts_info.twtar_order
        limit 200
      `;