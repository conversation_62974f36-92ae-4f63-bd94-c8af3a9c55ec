import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';

interface Data {}

interface ServiceOptions {}

export class TestDesignPrint implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    const twtar_id = id;
    const {is_print, _framework_id, _print_configs} = <any> data;
    const print_configs = {
      framework_id: _framework_id,
      print_configs: _print_configs,
    }
    await this.app.service('db/write/test-window-td-alloc-rules').patch(
      twtar_id,
      {
        is_print: !!(is_print) ? 1 : 0,
        print_configs: JSON.stringify(print_configs),
      }
    )

    return data;
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
