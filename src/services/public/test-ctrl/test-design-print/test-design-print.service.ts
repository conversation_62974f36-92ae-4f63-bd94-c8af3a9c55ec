// Initializes the `public/test-ctrl/test-design-print` service on path `/public/test-ctrl/test-design-print`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { TestDesignPrint } from './test-design-print.class';
import hooks from './test-design-print.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/test-ctrl/test-design-print': TestDesignPrint & ServiceAddons<any>;
  }
}

export default function (app: Application) {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-ctrl/test-design-print', new TestDesignPrint(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-ctrl/test-design-print');

  service.hooks(hooks);
}
