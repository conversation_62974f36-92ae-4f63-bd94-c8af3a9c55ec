import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { dbRawRead } from '../../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class AdministrationWindows implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    const records = this.getTw();
    // for sake of test controller, treat bg as QA
    return records
  }

  async getTw(){
    const records = await dbRawRead(this.app, [], `
      select tw.id
          , tw.test_ctrl_group_id 
          , tw.type_slug 
          , tw.title 
          , tw.is_active 
          , tw.is_qa 
          , tw.is_bg 
          , tw.is_public_practice 
          , tw.is_field_test 
          , tw.is_sa_signoff_required 
          , tw.is_classroom_assessment 
          , tw.date_start 
          , tw.date_end 
          , tw.academic_year
          , tw.created_on 
      from test_windows tw
      where tw.is_active = 1
        and tw.is_archived = 0
        and tw.is_bg = 0
      order by tw.id desc
    `);
    return records;
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
