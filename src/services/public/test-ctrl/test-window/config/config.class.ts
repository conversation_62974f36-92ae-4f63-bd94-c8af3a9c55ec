import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { currentUid } from '../../../../../util/uid';
import { getPropVals } from '../../../../../util/param-sanitization';
import { ITestWindow } from '../../../../db/schemas/test_windows.schema';
import { Errors } from '../../../../../errors/general';
import { dbDateNow } from '../../../../../util/db-dates';
import * as DBT from "../../../../../types/db-types";

interface Data {}

const ALLOWED_PROPS = [
  'attempts_intvl',
  'classroom_name_slug',
  'date_end',
  'date_start',
  'hardstop_offset_h',
  'is_allow_tqer_live_override',
  'is_teacher_creation',
  'irt_ready',
  'is_active',
  'is_allow_appeals',
  'is_allow_classroom',
  'is_allow_mobile_tether',
  'is_allow_new_bookings',
  'is_allow_new_ts',
  'is_allow_remote',
  'is_allow_results_tt',
  'is_allow_test_centre',
  'is_allow_video_conference',
  'is_test_centre',
  'is_archived',
  'is_bg',
  'is_for_pasi',
  'is_invig_taketest',
  'is_invig_unsubmit',
  'is_multi_attempt',
  'is_public_practice',
  'is_field_test',
  'is_classroom_assessment',
  'is_sa_signoff_required',
  'is_qa',
  'is_school_allowed_strict',
  'next_tw_id',
  'notes',
  'num_attempts',
  'pasi_exam_period',
  'remote_name_slug',
  'test_centre_name_slug',
  'test_design_id',
  'title_persistent',
  'title',
  'type_slug',
  'window_code',
  'academic_year',
  'window_date_human',
  'is_allow_teacher_single_scan_upload',
  'is_allow_teacher_bulk_scan_upload',
  'is_allow_local_scoring_report',
  'is_allow_local_mark_edits',
  'PASI_school_year'
]

interface ServiceOptions {}

export class Config implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if (params){
      const testWindowId = id;
      const uid = await currentUid(this.app, params);
      const patchFields:Partial<ITestWindow> = getPropVals(data, ALLOWED_PROPS);
      await this.app
        .service('db/write/test-windows')
        .patch(testWindowId, {
          ... patchFields,
          last_activated_on: dbDateNow(this.app),
          last_activated_by_uid: uid,
        });
      return true;
    }
    throw new Errors.BadRequest();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
