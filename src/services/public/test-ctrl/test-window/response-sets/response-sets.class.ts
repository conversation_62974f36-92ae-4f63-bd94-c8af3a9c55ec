import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { dbRawRead, dbRawWrite } from '../../../../../util/db-raw';
import { currentUid } from '../../../../../util/uid';

interface Data {}

interface ServiceOptions {}

export class ResponseSets implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query) {
      const ResponseSetsTransferLog  = await dbRawRead(this.app, [], `
        select * 
        from response_sets_transfer_log rstl
        where rstl.is_revoked = 0
      ;`)

      return ResponseSetsTransferLog;
    }
    throw new Errors.BadRequest;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (data && params) {
      const {
        marking_window_id_from,
        marking_window_id_to
      } = <any> data;
  
      const currentUID = await currentUid(this.app, params);
      const MAX_NUM_PROMISE_CALL = 100;

      // Check if the response sets transfer is already completed
      const transferHistory = await dbRawRead(this.app, [marking_window_id_from, marking_window_id_to], `
        select * 
        from response_sets_transfer_log rstl
        where rstl.is_revoked = 0
          and rstl.marking_window_id_from = ?
          and rstl.marking_window_id_to = ?
      ;`);
      if (transferHistory.length > 0) {
        return {};
      }
      
      // marking-response-selections
      const newResponseSelections = await dbRawRead(this.app, [marking_window_id_from, marking_window_id_to], `
        select mwi_new.id window_item_id 
              , mrs.taqr_id 
              , mrs.tag1 
              , mrs.tag2 
              , mrs.score_option_id 
              , mrs.rationale 
              , mrs.rationale_hash 
              , now() created_on
              , mrs.last_touched_on
              , mrs.last_touched_by_uid 
              , mrs.is_excluded 
              , mrs.is_included 
              , mrs.response_set_num 
              , mrs.response_set_order 
              , mrs.response_set_id 
              , mrs.id cloned_from_id 
        from marking_window_items mwi
        join marking_response_selections mrs 
          on mrs.window_item_id = mwi.id
        join marking_window_items mwi_new
          on mwi_new.item_id = mwi.item_id
          and mwi_new.batch_alloc_policy_id = mwi.batch_alloc_policy_id
        where mwi.marking_window_id = ?
          and mwi_new.marking_window_id = ?
      ;`);

      if (newResponseSelections){
        let inserts:Promise<any>[] = [];

        for (let responseSelection of newResponseSelections) {
          inserts.push(
            this.app
            .service('db/write/marking-response-selections')
            .create(responseSelection)
          )
          if (inserts.length >= MAX_NUM_PROMISE_CALL){
            await Promise.all(inserts);
            inserts = [];
          }  
        }
        await Promise.all(inserts);
      }
        
      // marking-taqr-cache
      const newTaqrCache = await dbRawRead(this.app, [marking_window_id_from, marking_window_id_to], `
        select distinct mwi_new.marking_window_id 
              , mwi_new.id mwi_id 
              , mtc.taqr_id 
              , mtc.schl_group_id 
              , 1 is_material 
        from marking_window_items mwi
        join marking_response_selections mrs 
          on mrs.window_item_id = mwi.id
        join marking_taqr_cache mtc 
          on mtc.mwi_id = mwi.id 
          and mtc.taqr_id = mrs.taqr_id 
        join marking_window_items mwi_new
          on mwi_new.item_id = mwi.item_id
          and mwi_new.batch_alloc_policy_id = mwi.batch_alloc_policy_id
        where mwi.marking_window_id = ?
          and mwi_new.marking_window_id = ?
      ;`);

      if (newTaqrCache){
        let inserts:Promise<any>[] = [];

        for (let taqrCache of newTaqrCache) {
          inserts.push(
            this.app
            .service('db/write/marking-taqr-cache')
            .create(taqrCache)
          )
          if (inserts.length >= MAX_NUM_PROMISE_CALL){
            await Promise.all(inserts);
            inserts = [];
          }  
        }
        await Promise.all(inserts);
      }

      // marking-response-set
      const newResponseSets = await dbRawRead(this.app, [marking_window_id_from, marking_window_id_to], `
        select mwi_new.id window_item_id
              , mrs.set_type_id
              , mrs.set_type_variant_num
              , mrs.created_by_uid
              , mrs.id cloned_from_set_id
              , mrs.is_revoked
        from marking_window_items mwi
        join marking_response_set mrs 
          on mrs.window_item_id = mwi.id
          and (mrs.is_revoked is null or mrs.is_revoked =0)
        join marking_window_items mwi_new
          on mwi_new.item_id = mwi.item_id
          and mwi_new.batch_alloc_policy_id = mwi.batch_alloc_policy_id
        where mwi.marking_window_id = ?
          and mwi_new.marking_window_id = ?
      ;`);

      if (newResponseSets){
        let inserts:Promise<any>[] = [];

        for (let responseSet of newResponseSets) {
          inserts.push(
            this.app
            .service('db/write/marking-response-set')
            .create(responseSet)
          )
          if (inserts.length >= MAX_NUM_PROMISE_CALL){
            await Promise.all(inserts);
            inserts = [];
          }  
        }
        await Promise.all(inserts);
      }

      // marking-response-set-selections
      const newResponseSetSelections = await dbRawRead(this.app, [marking_window_id_from, marking_window_id_to], `
        select mwi_new.id window_item_id
            , mrs_new.id set_id
            , mrsel_new.id selection_id
            , mrss.taqr_id
            , mrss.response_set_num
            , mrss.response_set_order
            , mrss.id cloned_from_id
        from marking_window_items mwi
        join marking_response_set_selections mrss  
          on mrss.window_item_id = mwi.id 
        join marking_response_set mrs_new 
          on mrs_new.cloned_from_set_id = mrss.set_id 
        join marking_response_selections mrsel_new 
          on mrsel_new.cloned_from_id = mrss.selection_id 
        join marking_window_items mwi_new
          on mwi_new.item_id = mwi.item_id
          and mwi_new.batch_alloc_policy_id = mwi.batch_alloc_policy_id
          and mwi_new.id = mrsel_new.window_item_id
          and mwi_new.id = mrs_new.window_item_id
        where mwi.marking_window_id = ?
          and mwi_new.marking_window_id = ?
      ;`);

      if (newResponseSetSelections){
        let inserts:Promise<any>[] = [];

        for (let responseSetSelection of newResponseSetSelections) {
          inserts.push(
            this.app
            .service('db/write/marking-response-set-selections')
            .create(responseSetSelection)
          )
          if (inserts.length >= MAX_NUM_PROMISE_CALL){
            await Promise.all(inserts);
            inserts = [];
          }  
        }
        await Promise.all(inserts);
      }

      const processSummaryCreateFields = {
        marking_window_id_from,
        marking_window_id_to,
        num_response_selections: newResponseSelections.length,
        num_taqr_cache: newTaqrCache.length,
        num_response_sets: newResponseSets.length,
        num_response_set_selections: newResponseSetSelections.length,
        created_by_uid: currentUID
      };

      const processSummary = this.app
              .service('db/write/response-sets-transfer-log')
              .create(processSummaryCreateFields);

      return processSummary
    }
    return new Errors.BadRequest('NO_PARAMS');
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
