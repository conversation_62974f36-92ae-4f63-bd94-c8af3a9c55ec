// Initializes the `public/test-ctrl/test-window/scheduled-sessions` service on path `/public/test-ctrl/test-window/scheduled-sessions`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { ScheduledSessions } from './scheduled-sessions.class';
import hooks from './scheduled-sessions.hooks';

// Add this service to the service type index
declare module '../../../../../declarations' {
  interface ServiceTypes {
    'public/test-ctrl/test-window/scheduled-sessions': ScheduledSessions & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-ctrl/test-window/scheduled-sessions', new ScheduledSessions(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-ctrl/test-window/scheduled-sessions');

  service.hooks(hooks);
}
