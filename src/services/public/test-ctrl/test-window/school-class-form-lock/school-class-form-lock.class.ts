import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { dbRawRead, dbRawReadSingle } from '../../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class SchoolClassFormLock implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    const test_window_id = id;
    const {Session_ID, Asmt_Code, Locked_Form_Code} = <any>data
    if (test_window_id  && Session_ID && Asmt_Code && Locked_Form_Code){
      const n = await this.applyClassFormOverride(+test_window_id, +Session_ID, Asmt_Code.trim(), Locked_Form_Code.trim() )
      return { n }
    }
    throw new Errors.BadRequest();
  }

  async applyClassFormOverride(test_window_id:number, Session_ID:number, Asmt_Code:string, Locked_Form_Code:string){
    // pull up current class locks and related meta data (ie if they have started classes, form code is active, etc.)
    const record = await dbRawReadSingle(this.app, {test_window_id, Session_ID, Asmt_Code, Locked_Form_Code}, `
      select ts.id ts_id 
          , ts.test_window_id
          , scts.school_class_id 
          , sccf.id sccf_id
          , sccf.twtdar_id sccf_twtdar_id
          , sccf.test_form_id sccf_test_form_id
          , twtar.id twtar_id -- this will drive the override
          , count(distinct twtar_inactive.id) n_inactive_alloc_related
          , count(distinct twtar_all.id) n_alloc_related
          , tf.id test_form_id -- todo could be multiple forms here so split up this query 
          , count(distinct ta.id) n_started 
          , scts.slug session_asmt_code
      from test_sessions ts 
      left join school_class_test_sessions scts 
        on scts.test_session_id  = ts.id 
        and scts.slug = :Asmt_Code
      left join school_classes sc 
          ON sc.id = scts.school_class_id
      LEFT JOIN school_class_common_forms sccf 
        ON sccf.school_class_id = scts.school_class_id 
        AND sccf.type_slug = scts.slug
        AND sccf.is_revoked = 0 
      left join school_class_test_sessions scts_actual  
        ON sc.id = scts_actual.school_class_id
        and scts_actual.slug = scts.slug
      left join test_attempts ta 
        on ta.test_session_id = scts_actual.test_session_id
        and ta.started_on is not null 
      left join test_window_td_alloc_rules twtar 
        on twtar.type_slug = scts.slug
        and twtar.test_window_id = ts.test_window_id
        and twtar.is_active = 1 
        and twtar.form_code = :Locked_Form_Code 
      left join test_window_td_alloc_rules twtar_inactive 
        on twtar.type_slug = scts.slug
        and twtar.is_active = 0
        and twtar.test_window_id = ts.test_window_id
        and twtar.form_code = :Locked_Form_Code 
      left join test_window_td_alloc_rules twtar_all
        on twtar_all.type_slug = scts.slug
        and twtar_all.test_window_id = ts.test_window_id
      left join test_forms tf 
        on tf.test_design_id  = twtar.test_design_id 
      where ts.id = :Session_ID 
        and ts.test_window_id = :test_window_id
      group by ts.id 
    `)
    
    // confirm
    if (!record || !record.ts_id){
      throw new Errors.NotFound('SESSION_NOT_FOUND');
    }
    if (!record.sccf_id){
      throw new Errors.NotFound('NO_PREVIOUS_CLASS_FORM_LOCK'); // todo: it would make sense to auto remedy this somehow (maybe not in the sequence of the functino)
    }
    if (!record.twtar_id){
      if (record.n_inactive_alloc_related > 0){
        throw new Errors.Forbidden('CANNOT_ASSIGN_INACTIVE_FORM:'+Asmt_Code);
      }
      if (record.n_alloc_related > 0){
        throw new Errors.NotFound('AVAILABLE_FORMS_NOT_MATCHING'); // todo: it would make sense to auto remedy this somehow (maybe not in the sequence of the functino)
      }
      else {
        throw new Errors.NotFound('NO_AVAILABLE_FORMS_FOR_ASMT_CODE');
      }
    }
    if (!record.test_form_id){
      throw new Errors.BadRequest('NO_PUBLISHED_FORM_ALLOCATED'); // todo: it would make sense to auto remedy this somehow (maybe not in the sequence of the functino)
    }
    if (record.n_started > 0){
      throw new Errors.GeneralError('ATTEMPTS_IN_PROGRESS'); // todo: it would make sense to auto remedy this somehow (maybe not in the sequence of the functino)
    }

    // override the form lock for the class
    // todo: revoke and add instead of patching
    await this.app.service('db/write/school-class-common-forms').patch(record.sccf_id, {
      twtdar_id:    record.twtar_id,
      test_form_id: record.test_form_id,
    })

    // pull the attempts tied to type slug and override the form for them (class and type slug, not for the session)
    const ta_records = await dbRawRead(this.app, {test_window_id, Session_ID, Asmt_Code, Locked_Form_Code}, `
      select ta.id
          , ta.test_session_id      
          , ta.created_on 
          , ta.test_form_id  
          , ta.twtdar_id 
      from test_sessions ts 
      join school_class_test_sessions scts 
        on scts.test_session_id  = ts.id 
        and scts.slug = :Asmt_Code -- 'ABED_MATH_30_2_FT_YE'
      join school_classes sc 
          ON sc.id = scts.school_class_id
      join school_class_test_sessions scts_actual  
        ON sc.id = scts_actual.school_class_id
        and scts_actual.slug = scts.slug
      join test_sessions ts_actual  
        ON ts_actual.id = scts_actual.test_session_id 
        and ts_actual.is_closed = 0 
        and ts_actual.is_cancelled = 0 
      join test_attempts ta 
        on ta.test_session_id = scts_actual.test_session_id
        and ta.started_on is null 
      where ts.id = :Session_ID -- 91370
      and ts.test_window_id = :test_window_id
    `)

    for (let ta of ta_records){
      await this.app.service('db/write/test-attempts').patch(ta.id, {
        twtdar_id:    record.twtar_id,
        test_form_id: record.test_form_id,
      })
    }

    return ta_records.length
  }


  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
