// Initializes the `public/test-ctrl/test-window/school-class-form-lock` service on path `/public/test-ctrl/test-window/school-class-form-lock`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { SchoolClassFormLock } from './school-class-form-lock.class';
import hooks from './school-class-form-lock.hooks';

// Add this service to the service type index
declare module '../../../../../declarations' {
  interface ServiceTypes {
    'public/test-ctrl/test-window/school-class-form-lock': SchoolClassFormLock & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-ctrl/test-window/school-class-form-lock', new SchoolClassFormLock(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-ctrl/test-window/school-class-form-lock');

  service.hooks(hooks);
}
