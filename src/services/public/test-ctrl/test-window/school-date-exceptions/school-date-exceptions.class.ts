import { dbRawReadSingle, dbRawWrite } from './../../../../../util/db-raw';
import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { dbRawRead } from '../../../../../util/db-raw';
import { Errors } from '../../../../../errors/general';
import { currentUid } from '../../../../../util/uid';


interface Data {}

interface ServiceOptions {}

export class SchoolDateExceptions implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const {test_window_id} = params.query;
      const records = await dbRawRead(this.app, {test_window_id}, `
        select tsa.id
             , tsa.school_id s_id
             , sd.is_sample
             , sd.foreign_id sd_code
             , sd.name sd_name
             , s.foreign_id s_code
             , s.name s_name
             , tsa.is_all_type_slugs
             , tsa.type_slug
             , tsa.date_start_override
             , tsa.created_on
        from twtdar_schools_allowed tsa 
        join schools s on s.id = tsa.school_id
        join school_districts sd on sd.group_id = s.schl_dist_group_id 
        where tsa.test_window_id = :test_window_id
          and tsa.is_revoked = 0 
          and tsa.is_date_override = 1
        order by tsa.id desc
      `);
      return records
    }
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (params && params.query){
      const created_by_uid = await currentUid(this.app, params);
      const {school_id, type_slug, date_start} = <any> data;
      const {test_window_id} = params.query;

      const date_start_override = date_start;

      // 2. check if it is already listed
      const existingRecords = await dbRawRead(this.app, { test_window_id, school_id, type_slug, date_start }, `
        SELECT tsa.id
        from twtdar_schools_allowed tsa 
        WHERE tsa.test_window_id = :test_window_id
          and tsa.school_id = :school_id
          and tsa.type_slug = :type_slug
          and tsa.date_start_override = :date_start
          and is_revoked = 0
      `);
      if (existingRecords.length > 0) {
        throw new Errors.Conflict('PREVENT_DUPLICATE_RECORD');
      }

      // 3. insert new row
      const payload = {
        is_all_type_slugs: type_slug ? 0 : 1,
        type_slug,
      }
      await  dbRawWrite(this.app, { ... payload, test_window_id, school_id, type_slug, date_start, created_by_uid }, `
        INSERT INTO twtdar_schools_allowed 
         SET school_id = :school_id
           , type_slug = :type_slug
           , date_start_override = :date_start
           , test_window_id = :test_window_id
           , is_date_override = 1
           , created_on = now()
           , created_by_uid = :created_by_uid
      `);

      return {school_id, type_slug, date_start};
    }
    throw new Errors.BadRequest()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    if (params && params.query){
      if (!id) { throw new Errors.BadRequest('MISSING_ID'); }
      const revoked_by_uid = await currentUid(this.app, params);
      await dbRawWrite(this.app, { id, revoked_by_uid }, `
        UPDATE twtdar_schools_allowed 
        SET is_revoked = 1, 
            revoked_on = now(), 
            revoked_by_uid = :revoked_by_uid
        WHERE id = :id
      `);
      return { id, isSuccess:true };
    }
    throw new Errors.BadRequest();
  }
  
}
