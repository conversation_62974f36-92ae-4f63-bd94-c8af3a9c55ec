// Initializes the `public/test-ctrl/test-window/school-date-exceptions` service on path `/public/test-ctrl/test-window/school-date-exceptions`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { SchoolDateExceptions } from './school-date-exceptions.class';
import hooks from './school-date-exceptions.hooks';

// Add this service to the service type index
declare module '../../../../../declarations' {
  interface ServiceTypes {
    'public/test-ctrl/test-window/school-date-exceptions': SchoolDateExceptions & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-ctrl/test-window/school-date-exceptions', new SchoolDateExceptions(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-ctrl/test-window/school-date-exceptions');

  service.hooks(hooks);
}
