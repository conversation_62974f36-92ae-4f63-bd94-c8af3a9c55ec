import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { dbRawRead, dbRawWrite } from '../../../../../util/db-raw';
import { currentUid } from '../../../../../util/uid';

interface Data {}

interface ServiceOptions {}

export class SchoolLangExceptions implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const {test_window_id} = params.query;
      return await this.getSchoolLangExceptions(test_window_id);
    }
    throw new Errors.BadRequest();
  }


  async getSchoolLangExceptions(test_window_id:number){
    const records = await dbRawRead(this.app, {test_window_id}, `
      select tsa.id
          , tsa.school_id s_id   
          , sd.is_sample 
          , sd.foreign_id sd_code
          , sd.name sd_name
          , s.foreign_id s_code
          , s.name s_name
          , s.group_id s_group_id
          , s.id s_id
          , max(tsa.is_all_type_slugs) is_all_type_slugs
          , GROUP_CONCAT(tsa.type_slug) type_slugs
          , min(tsa.created_on) created_on -- does not include revoked
          , max(tsa.created_on) updated_on
      from twtdar_schools_allowed tsa 
      join schools s on s.id = tsa.school_id
      join school_districts sd on sd.group_id = s.schl_dist_group_id 
      where tsa.test_window_id = :test_window_id
        and tsa.is_revoked = 0 
        and tsa.is_lang_override  = 1
      group by tsa.school_id
      order by tsa.id desc
    `);
    return records
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
// eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (params && params.query){
      const created_by_uid = await currentUid(this.app, params);
      const {school_id} = <any> data;
      const s_id = school_id;
      const {test_window_id} = params.query;

      // 2. check if it is already listed
      const existingRecords = await dbRawRead(this.app, { test_window_id, s_id }, `
        SELECT tsa.id
        from twtdar_schools_allowed tsa 
        WHERE tsa.test_window_id = :test_window_id
          and tsa.school_id = :s_id
          and tsa.is_revoked = 0
          and tsa.is_lang_override = 1
      `);
      if (existingRecords.length > 0) {
        throw new Errors.Conflict('PREVENT_DUPLICATE_RECORD');
      }

      // 3. insert new row
      const payload = {
      }
      await  dbRawWrite(this.app, { ... payload, test_window_id, s_id, created_by_uid }, `
        INSERT INTO twtdar_schools_allowed 
         SET school_id = :s_id
           , test_window_id = :test_window_id
           , created_on = now()
           , created_by_uid = :created_by_uid
           , is_lang_override = 1
      `);

      return {s_id};
    }
    throw new Errors.BadRequest()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    // todo: this will be limited to a single one
    if (params && params.query){
      if (!id) { throw new Errors.BadRequest('MISSING_ID'); }
      const revoked_by_uid = await currentUid(this.app, params);
      await dbRawWrite(this.app, { id, revoked_by_uid }, `
        UPDATE twtdar_schools_allowed 
        SET is_revoked = 1, 
            revoked_on = now(), 
            revoked_by_uid = :revoked_by_uid
        WHERE id = :id
      `);
      return { id, isSuccess:true };
    }
    throw new Errors.BadRequest();
  }

}
