// Initializes the `public/test-ctrl/test-window/school-lang-exceptions` service on path `/public/test-ctrl/test-window/school-lang-exceptions`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { SchoolLangExceptions } from './school-lang-exceptions.class';
import hooks from './school-lang-exceptions.hooks';

// Add this service to the service type index
declare module '../../../../../declarations' {
  interface ServiceTypes {
    'public/test-ctrl/test-window/school-lang-exceptions': SchoolLangExceptions & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-ctrl/test-window/school-lang-exceptions', new SchoolLangExceptions(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-ctrl/test-window/school-lang-exceptions');

  service.hooks(hooks);
}
