export const SQL_GET_SCHOOL_METAS = `
  SELECT 
    sm.id
    , sm.school_id
    , sm.key
    , sm.key_namespace
    , sm.value
    , s.foreign_id as school_code
  FROM school_metas sm
  JOIN schools s ON s.id = sm.school_id
  WHERE sm.key_namespace = 'GEO' 
    AND sm.is_revoked = 0
`;

export const SQL_FIND_SCHOOL_METAS = `
  SELECT 
    ac.course_code
    , ac.associated_performance_courses
    , sm.school_id
    , s.foreign_id as school_code
  FROM assessment_courses ac
  JOIN tw_td_types tdt 
    ON tdt.type_slug = ?
  JOIN school_metas sm 
    ON sm.key_namespace = 'HIST_PERF' 
    AND sm.is_revoked = 0
  JOIN schools s 
    ON s.id = sm.school_id
  WHERE ac.test_window_id = ?
`; 