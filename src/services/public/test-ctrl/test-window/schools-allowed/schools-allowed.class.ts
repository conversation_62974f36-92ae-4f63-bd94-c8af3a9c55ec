import { dbRawReadSingle, dbRawWrite } from './../../../../../util/db-raw';
import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { dbRawRead } from '../../../../../util/db-raw';
import { Errors } from '../../../../../errors/general';
import { currentUid } from '../../../../../util/uid';

interface Data {}

interface ServiceOptions {}

export class SchoolsAllowed implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const {test_window_id} = params.query;
      return await this.getRegisteredParticipatingSchools(test_window_id);
    }
    throw new Errors.BadRequest();
  }

  async getActualParticipatingSchools(test_window_id:number, district_id?: number){
    const props: any = { test_window_id };
    if (district_id) {
      props.district_id = district_id;
    }

    const records = await dbRawRead(this.app, props, `
       select sd.is_sample 
            , sd.foreign_id sd_code
            , sd.name sd_name
            , s.foreign_id s_code
            , s.name s_name
            , s.group_id s_group_id
            , s.id s_id
            , count(distinct ts.id) num_sessions
            , count(distinct ta.uid) num_students
        from test_window_td_alloc_rules twtar 
        join test_sessions ts 
          on ts.test_window_id = twtar.test_window_id 
        join school_class_test_sessions scts 
          on ts.id = scts.test_session_id 
          and twtar.type_slug = scts.slug 
        join school_classes sc 
          on sc.id = scts.school_class_id 
        join schools s 
          on s.group_id = sc.schl_group_id 
        join school_districts sd 
          on sd.group_id = s.schl_dist_group_id 
        join test_attempts ta 
          on ta.test_session_id  = ts.id 
          and ta.started_on  is not null 
        where twtar.test_window_id = :test_window_id
          and twtar.is_secured = 1
          and sd.is_sample  = 0
          ${district_id ? 'and sd.group_id = :district_id' : ''}
          -- and s.group_id = testing -- 57392
        group by s.id
    ;`)
    return records
  }

  async getRegisteredParticipatingSchools(test_window_id:number, district_id?: number){
    const props: any = { test_window_id };
    if (district_id) {
      props.district_id = district_id;
    }

    const records = await dbRawRead(this.app, props, `
      select tsa.id
          , tsa.school_id s_id   
          , sd.is_sample 
          , sd.foreign_id sd_code
          , sd.name sd_name
          , s.foreign_id s_code
          , s.name s_name
          , s.group_id s_group_id
          , s.id s_id
          , max(tsa.is_all_type_slugs) is_all_type_slugs
          , GROUP_CONCAT(tsa.type_slug) type_slugs
          , min(tsa.created_on) created_on -- does not include revoked
          , max(tsa.created_on) updated_on
      from twtdar_schools_allowed tsa 
      join schools s on s.id = tsa.school_id
      join school_districts sd 
        on sd.group_id = s.schl_dist_group_id 
        ${district_id ? 'and sd.group_id = :district_id' : ''}
      where tsa.test_window_id = :test_window_id
        and tsa.is_revoked = 0 
        and tsa.is_date_override  = 0
        and tsa.is_lang_override  = 0
        -- and s.group_id = testing -- 57392
      group by tsa.school_id
      order by tsa.id desc
    `);
    return records
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (params && params.query){
      const created_by_uid = await currentUid(this.app, params);
      const {s_id, type_slug} = <any> data;
      const {test_window_id} = params.query;
      // To do: allow for sepcific type slug only
      // 2. check if it is already listed
      const existingRecords = await dbRawRead(this.app, { test_window_id, s_id }, `
        SELECT tsa.id
        from twtdar_schools_allowed tsa 
        WHERE tsa.test_window_id = :test_window_id
          and tsa.school_id = :s_id
          and is_revoked = 0
          ${type_slug?`and tsa.type_slug = :type_slug`:`and tsa.is_all_type_slugs = 1`}
          and tsa.is_lang_override = 0 -- ignore lang exception
          and tsa.date_start_override is null -- ignore date exception
      `);
      if (existingRecords.length > 0) {
        throw new Errors.Conflict('PREVENT_DUPLICATE_RECORD');
      }

      // 3. insert new row
      
      const payload = {
        is_all_type_slugs: type_slug ? 0 : 1,
        type_slug,
      }
      await  dbRawWrite(this.app, { ... payload, test_window_id, s_id, created_by_uid }, `
        INSERT INTO twtdar_schools_allowed 
         SET school_id = :s_id
           , test_window_id = :test_window_id
           , created_on = now()
           , created_by_uid = :created_by_uid
           , is_all_type_slugs = 1
      `);

      return {s_id};
    }
    throw new Errors.BadRequest()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    // todo: this will be limited to a single one
    if (params && params.query){
      if (!id) { throw new Errors.BadRequest('MISSING_ID'); }
      const revoked_by_uid = await currentUid(this.app, params);
      await dbRawWrite(this.app, { id, revoked_by_uid }, `
        UPDATE twtdar_schools_allowed 
        SET is_revoked = 1, 
            revoked_on = now(), 
            revoked_by_uid = :revoked_by_uid
        WHERE id = :id
      `);
      return { id, isSuccess:true };
    }
    throw new Errors.BadRequest();
  }

}
