// Initializes the `public/test-ctrl/test-window/schools-allowed` service on path `/public/test-ctrl/test-window/schools-allowed`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { SchoolsAllowed } from './schools-allowed.class';
import hooks from './schools-allowed.hooks';

// Add this service to the service type index
declare module '../../../../../declarations' {
  interface ServiceTypes {
    'public/test-ctrl/test-window/schools-allowed': SchoolsAllowed & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-ctrl/test-window/schools-allowed', new SchoolsAllowed(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-ctrl/test-window/schools-allowed');

  service.hooks(hooks);
}
