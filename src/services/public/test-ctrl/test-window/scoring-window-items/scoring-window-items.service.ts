// Initializes the `scoring-window-items` service on path `/scoring-window-items`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { ScoringWindowItems } from './scoring-window-items.class';
import hooks from './scoring-window-items.hooks';

// Add this service to the service type index
declare module '../../../../../declarations' {
  interface ServiceTypes {
    'public/test-ctrl/test-window/scoring-window-items': ScoringWindowItems & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-ctrl/test-window/scoring-window-items', new ScoringWindowItems(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-ctrl/test-window/scoring-window-items');

  service.hooks(hooks);
}
