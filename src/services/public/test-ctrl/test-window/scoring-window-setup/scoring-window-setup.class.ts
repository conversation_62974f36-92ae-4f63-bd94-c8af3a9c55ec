import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { currentUid } from '../../../../../util/uid';

interface Data {
  [key: string]: any,
}

interface ServiceOptions {}

export class ScoringWindowSetup implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (data && params) {
      const {
        name,
        start_on,
        end_on,
        is_active,
        is_paused,
        is_scoring_disabled,
        is_locked,
        is_hidden_for_scorers,
        lang,
        test_window_id
      } = <any> data;
  
      const currentUID = await currentUid(this.app, params);
  
      const uGroupCreateFields = {
        group_type: 'marking_window',
        description: name,
        created_by_uid: currentUID
      }
      const uGroup = await this.app
        .service('db/write/u-groups')
        .create(uGroupCreateFields);

      const markingWindowCreateFields = {
        group_id: uGroup.id,
        name,
        start_on,
        end_on,
        is_active,
        is_paused,
        is_scoring_disabled,
        is_locked,
        is_hidden_for_scorers,
        lang,
        created_by_uid: currentUID
      }
      const markingWindow = await this.app
        .service('db/write/marking-windows')
        .create(markingWindowCreateFields);  

      const markingWindowTestWindowCreateFields = {
        marking_window_id: markingWindow.id,
        test_window_id,
        created_by_uid: currentUID
      }
  
      const markingWindowTestWindow = await this.app
        .service('db/write/marking-window-test-window')
        .create(markingWindowTestWindowCreateFields);  

      
      // default marker tasks setup
      const componentTypesForEN = [
        {order: 1, component_type: 'TRAINING_MATERIALS', caption: 'Training', status: 'PENDING', allow_revisit: 1, is_pass_fail: 0},
        {order: 2, component_type: 'PRACTICE', caption: 'Practice Test', status: 'NOT_AVAILABLE', allow_revisit: 1, is_pass_fail: 0},
        {order: 3, component_type: 'QUALIFYING', caption: 'Qualifying Test', status: 'NOT_AVAILABLE', allow_revisit: 0, is_pass_fail: 1},
        {order: 4, component_type: 'SCORING', caption: 'Scoring', status: 'NOT_AVAILABLE', allow_revisit: 0, is_pass_fail: 0},
      ]
      const componentTypesForFR = [
        {order: 1, component_type: 'TRAINING_MATERIALS', caption: 'Formation', status: 'PENDING', allow_revisit: 1, is_pass_fail: 0},
        {order: 2, component_type: 'PRACTICE', caption: 'Test pour s’exercer', status: 'NOT_AVAILABLE', allow_revisit: 1, is_pass_fail: 0},
        {order: 3, component_type: 'QUALIFYING', caption: 'Test de qualification', status: 'NOT_AVAILABLE', allow_revisit: 0, is_pass_fail: 1},
        {order: 4, component_type: 'SCORING', caption: 'Notation', status: 'NOT_AVAILABLE', allow_revisit: 0, is_pass_fail: 0},
      ]

      let defaultMarkingTasksCreateFields = {
        ctrl_group_id: markingWindow.group_id,
        marking_window_id: markingWindow.id,
        is_default: 1,
        cache_batches_rem: 3
      }

      let targetComponents;
      if (lang == 'en') {
        targetComponents = componentTypesForEN
      } else {
        targetComponents = componentTypesForFR
      }

      for (let component of targetComponents) {
        await this.app
        .service('db/write/marking-item-marker-tasks')
        .create({
          ...defaultMarkingTasksCreateFields,
          ...component
        });  
      }
      return {};
    }
    return new Errors.BadRequest('NO_PARAMS');
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if(!params) throw new Errors.BadRequest();

    const { 
      name,
      start_on,
      end_on,
      is_active,
      is_paused,
      is_scoring_disabled,
      is_locked,
      is_hidden_for_scorers,
      lang,
    } = data;

    const payload = {
      name,
      start_on,
      end_on,
      is_active,
      is_paused,
      is_scoring_disabled,
      is_locked,
      is_hidden_for_scorers,
      lang,
    }; 

    const patchedRecord = await this.app
      .service('db/write/marking-windows')
      .patch(id, payload)
    
    return patchedRecord;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
