// Initializes the `scoring-window-setup` service on path `/scoring-window-setup`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { ScoringWindowSetup } from './scoring-window-setup.class';
import hooks from './scoring-window-setup.hooks';

// Add this service to the service type index
declare module '../../../../../declarations' {
  interface ServiceTypes {
    'public/test-ctrl/test-window/scoring-window-setup': ScoringWindowSetup & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-ctrl/test-window/scoring-window-setup', new ScoringWindowSetup(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-ctrl/test-window/scoring-window-setup');

  service.hooks(hooks);
}
