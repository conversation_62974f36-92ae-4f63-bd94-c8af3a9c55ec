import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { dbDateNow } from '../../../../../util/db-dates';
import { dbRawRead } from '../../../../../util/db-raw';
import { currentUid } from '../../../../../util/uid';

interface Data {}

interface ServiceOptions {}

export class ScoringWindows implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query) {
      const test_window_id = params.query.test_window_id

      const activeScoringWindows = await dbRawRead(this.app, [test_window_id], `
        select mw.*
        from marking_windows mw
        join marking_window_test_window mwtw 
        on mwtw.marking_window_id = mw.id 
        and mwtw.test_window_id = ?
        where mw.is_active = 1
        group by mw.id
      ;`)

      const scoringWindowTestWindows = await dbRawRead(this.app, [test_window_id], `
        select tw.id tw_id,
              tw.title tw_name,
              mw.id mw_id,
              mw.name mw_name,
              mwtw.id mwtw_id
        from test_windows tw 
        join marking_window_test_window mwtw
          on mwtw.test_window_id = tw.id
          and mwtw.is_removed = 0
        join marking_windows mw 
          on mw.id = mwtw.marking_window_id
        where tw.id = ?
      ;`)

      return [{
        activeScoringWindows: activeScoringWindows,
        scoringWindowTestWindows: scoringWindowTestWindows
      }]
    }
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (data && params) {
      const {
        marking_window_id,
        test_window_id
      } = <any> data;
  
      const currentUID = await currentUid(this.app, params);
  
      const testWindowMarkingWindowCreateFields = {
        marking_window_id,
        test_window_id,
        created_by_uid: currentUID
      }
      return await this.app
        .service('db/write/marking-window-test-window')
        .create(testWindowMarkingWindowCreateFields);  
    }
    return new Errors.BadRequest('NO_PARAMS');
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    if (id && params) {
      const currentUID = await currentUid(this.app, params);
      
      return await this.app
      .service('db/write/marking-window-test-window')
      .patch(id, {
        is_removed: 1,
        removed_on: dbDateNow(this.app),
        removed_by_uid: currentUID
      });

    }
    throw new Errors.BadRequest('MISSING_ID');
  }
}
