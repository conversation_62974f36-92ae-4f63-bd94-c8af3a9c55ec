// Initializes the `scoring-windows` service on path `/scoring-windows`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { ScoringWindows } from './scoring-windows.class';
import hooks from './scoring-windows.hooks';

// Add this service to the service type index
declare module '../../../../../declarations' {
  interface ServiceTypes {
    'public/test-ctrl/test-window/scoring-windows': ScoringWindows & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/test-ctrl/test-window/scoring-windows', new ScoringWindows(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/test-ctrl/test-window/scoring-windows');

  service.hooks(hooks);
}
