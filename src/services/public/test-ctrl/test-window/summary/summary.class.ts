const _ = require('lodash');
const moment = require('moment');
import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { dbRawRead, dbRawWrite } from '../../../../../util/db-raw';
import { currentUid } from '../../../../../util/uid';
import { BadRequest } from '@feathersjs/errors';
interface Data 
{
  id?:number,
  test_ctrl_group_id?: number,
  num_sessions_created?: number,
  num_sessions_administered?: number,
}

interface TypeSlugMappings
{
  id: number, 
  type_slug: string
}

interface ITestWindow {
  id: number,
  title: string,
  is_active: number,
  date_start: string,
  date_end: string,
  test_design_id: number,
  notes: string,
  created_on: string,
  create_by_uid:number,
  last_activated_on:string,
  last_activated_by_uid: number,
  test_ctrl_group_id:number,
}

interface ServiceOptions {}

export class Summary implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<TypeSlugMappings[]> 
  {
    if (params?.query?.getTypeSlugMappings == 1)
    {
      return await dbRawRead(this.app, [], 
      `
      SELECT tsm.id, tsm.type_slug
      FROM type_slug_mappings tsm
      WHERE tsm.is_revoked = 0
      ;
      `);
      
    }

    return [];
  }

  async get (id: Id, params?: Params): Promise<Data>
  { 
    // gets some important TW data
    if (id)
    {
      const testWindowRecord = <ITestWindow> await this.app
        .service('db/read/test-windows')
        .get(id);

      const assessmentPriceRecord = await dbRawRead(this.app, [id], `
        select price_per_student as ap_price_per_student
             , description as ap_description
             , created_on as ap_created_on
             , created_by_uid as ap_created_by_uid 
          from assessment_prices 
         where is_revoked = 0 
           and test_window_id = ?
      ;`)

      let testSessionStats;
      try {
        testSessionStats = await this.app
          .service('db/read/test-window-sessions-created')
          .get(id);
      }
      catch(e){
        testSessionStats = {
          num_sessions_created: 0,
          num_sessions_administered: 0,
        }
      }

      const returnValue = {
        ... testWindowRecord,
        ... assessmentPriceRecord[0], //should only have 1 assessment price only
        num_sessions_created: testSessionStats.num_sessions_created,
        num_sessions_administered: testSessionStats.num_sessions_administered,
      }
      return returnValue;

    }
    return {};
  }

  async create (data: Data, params?: Params): Promise<Data> 
  {
    const test_ctrl_group_id = data.test_ctrl_group_id;
    const uid = await currentUid(this.app, params!);

    if (test_ctrl_group_id == null || isNaN(test_ctrl_group_id))
    {
      throw new Error("Invalid test_ctrl_group_id");
    }

    const type_slug = await this.findRelevantTypeSlug(+test_ctrl_group_id);

    const dateStart = moment().add(7, 'days');
    const dateEnd = moment(dateStart).add(7, 'days');
    const createFields = 
    {
      test_ctrl_group_id: test_ctrl_group_id,
      is_qa: 1, // default to opening them in the background
      is_bg: 0,
      is_allow_new_ts: 0, // dont allow test sessions to be created until a test design is selected
      title: '{}',
      notes: '{}',
      date_start: dateStart.utc().format(),
      date_end: dateEnd.utc().format(),
      create_by_uid: uid,
      last_activated_by_uid: uid,
      type_slug

    }
    const newRecord = await this.app
    .service('db/write/test-windows')
    .create(createFields);

    await this.createSchoolSemesters(type_slug, +newRecord.id);

    return newRecord;
  }

  public async createSchoolSemesters(tw_type_slug: string, testWindowId: number): Promise<void>
  {
    // guranteed to return 1 row because of unique constraint on tw_type_slug
    let config = await dbRawRead(this.app, [tw_type_slug], 
    `
      SELECT *
      FROM school_semesters_config ssc
      WHERE ssc.is_revoked = 0 AND ssc.tw_type_slug = ?
      ;
    `);

    if (config.length === 0)
    {
      throw new Error(`Unable to find this tw_type_slug in table school_semesters_config, 
      no school semesters created. They will need to be manually created, but the test window
      has been successfully created.`); 
    }

    const getCurrentYear = (): number => 
    {
      return new Date().getFullYear();
    }

    await dbRawWrite(this.app, [getCurrentYear(), testWindowId, config[0].reference_test_window_id, config[0].namespace], 
    `
    INSERT INTO school_semesters
      (namespace, foreign_scope_id, foreign_id, year, name, test_window_id)
    SELECT 
     ss.namespace, ss.foreign_scope_id, ss.foreign_id, ?, ss.name, ? 
    FROM 
      school_semesters ss
    WHERE
      ss.test_window_id = ? AND
      ss.namespace = ?
    ORDER BY ss.id
    ;
    `);

    return;

  }

  public async findRelevantTypeSlug(test_ctrl_group_id: number): Promise<string>
  {
    // guranteed to return 1 row because of unique constraint on test_ctrl_group_id
    let rows = await dbRawRead(this.app, [test_ctrl_group_id], 
    `
      SELECT *
      FROM type_slug_mappings tsm
      WHERE tsm.is_revoked = 0 AND tsm.test_ctrl_group_id = ?
    `);

    if (rows.length === 0)
    {
      throw new Error("Unable to find this test_ctrl_group_id in table type_slug_mappings, stopping creation.");
    }

    return rows[0].type_slug.toLocaleUpperCase();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
