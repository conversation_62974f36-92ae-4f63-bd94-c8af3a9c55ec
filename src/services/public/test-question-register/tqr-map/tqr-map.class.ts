import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';

export interface ITqrParamMap {
  id: number,
  item_bank_code: string
  tqr_col: string
  options: string
  param_type: string,
  parsing_rules: any,
  secondary_item_bank_codes: string		
}
interface Data extends ITqrParamMap {}

interface ServiceOptions {}

export class TqrMap implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<ITqrParamMap[]> {


    const paramMap = await this.app.service('db/read/test-question-register-param-map')
    .db()
    .select('id','item_bank_code', 'tqr_col', 'options', 'param_type', 'parsing_rules').where('is_revoked', 0);


    if (params && params.query && params.query.isFiltered) {
      // only return the filtered ones

      const { assessmentType: pubAssessmentType,  language} = params.query
      
      // return filtered params
      return paramMap.filter((PC:any) => {
        const { options } = PC
        if(options){
          const {framework, publish } = JSON.parse(options)
          let checksAll = true;
          if(framework){
            const { assessment_type } = framework
  
            if(assessment_type && assessment_type != pubAssessmentType ){
              checksAll = false
            }
          }
  
          if(publish){
            const { lang } = publish
            
            if(lang && lang != language){
              checksAll = false
            }
          }
  
          if(checksAll) return PC
  
        } else {
          return PC
        }
      });
    }

    //return all of them
    return paramMap
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }
}
