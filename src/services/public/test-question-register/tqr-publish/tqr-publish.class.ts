import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { IDBQuestion, ILanguage, ITestDesign, TestFormConstructionMethod, UpsertResult } from './types';
import { AssessmentType } from '../../bc-admin-coordinator/test-window/assessment-type';
import { ITqrParamMap } from '../tqr-map/tqr-map.class';
import fs from 'fs';
import { Errors } from '../../../../errors/general';
import { dbRawRead } from '../../../../util/db-raw';
import { arrToMap } from '../../../../util/param-sanitization';
import _ from 'lodash';
import { IQuestionWeightMapping } from '../../test-auth/test-designs/test-designs.class';

interface IRealQuestionCheck {
  dBProp?: boolean,
  metaParam?: boolean,
  readSelQ?: boolean,
  expected_answer_mapping?: IExpectedAnsMap
}

interface IRealQuestionCheck {
  dBProp?: boolean,
  metaParam?: boolean,
  readSelQ?: boolean
}

interface IExpectedAns {
  correct_responses: string[];
  item_id: number;
  lang: string;
  weight: number;
}

interface IExpectedAnsMap {
  [key: number]: IExpectedAns; // Key is a number, value is of type `Question`
}

interface IMappings {
  question_weight_mapping?: IQuestionWeightMapping[] | null, 
  item_version_mapping?: any, 
  expected_answer_mapping?: IExpectedAnsMap
}

interface Data {
  test_design_id: number, pubAssessmentType: AssessmentType, component_code: string
}

interface IQuestionRegisterDataMapping {
  registerField: string,
  mapping: any
}

interface ServiceOptions {}

export const objParamToArray = ['entry_caption']

export class TqrPublish implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<any> {

    // get publishing fields for latest framework and params of the assessment 

    if (!params || !params.query) throw new Errors.BadRequest('INVALID_PARAMETERS');

    const {source_item_set_id} = params.query;

    const test_design = await this.app
      .service('db/read/temp-question-set')
      .db()
      .select(['id', 'framework'])
      .where('id', source_item_set_id);

    //  find test design framework
    let framework = JSON.parse(test_design[0].framework);

    const { assessmentType, componentCode, formCode } = framework;

    const language = await this.findLanguageForComponentCode(componentCode);

    // get the filtered TQR mapping
    const tqrMap = await this.app.service('public/test-question-register/tqr-map').find({query : { isFiltered: 1, assessmentType, language}});
    // ; await this.findTqrMappings(assessmentType, language);

    //  find questions 
    let questions = await this.findTestQuestionsForTestDesign(framework, language, tqrMap, 1, {dBProp: true, metaParam: true});  
    
    let registerFields = [];

    for(const question of questions){
      const registerField = this.extractRegisterFields(tqrMap, question, language)
      if(registerField) registerFields.push(registerField);
    }

    return [
      {
        tqrMap,
        language,
        registerFields
      }
    ]
    
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<any> {
    // const {test_design_id,  pubAssessmentType, component_code} = data
    // return await this.populateTestQuestionRegister(test_design_id, pubAssessmentType, component_code);
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<any> {
    
    if (!id || !params || !params.query) throw new Errors.BadRequest('INVALID_PARAMETERS');

    const test_design_id = +(id.toString());
    const {pubAssessmentType, component_code} = params.query;

    if(!pubAssessmentType || !component_code) throw new Errors.BadRequest('INVALID_PARAMETERS');

    return await this.populateTestQuestionRegister(test_design_id, pubAssessmentType, component_code);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }


  async populateTestQuestionRegisterEQAO(test_design_id: number, formIdMap: Map<number,any>, lang: ILanguage, mappings: IMappings) {

    const {question_weight_mapping, item_version_mapping, expected_answer_mapping} = mappings
    // default mapping
    const tqrDefaultMap = await this.app.service('public/test-question-register/tqr-map').find();

    // client param mapping
    const tqrClientMap = <ITqrParamMap[]>await this.app.service('public/test-question-register/tqr-generic-map').find();

    const tqrMap = [...tqrDefaultMap, ...tqrClientMap ];

    // Creating this general data mapping array for any future mapping requirements
    const questionRegisterDataMapping: IQuestionRegisterDataMapping[] = [{registerField: 'item_version_id', mapping: item_version_mapping}]

    // find test design
    let test_design = await this.findTestDesign(test_design_id);

    // find test design framework
    let framework = JSON.parse(test_design.framework);

     //find the questions and publish
    switch (framework.testFormType) {
      case TestFormConstructionMethod.SCORE_ENTRY:
        const scoreEntryQs = await this.findTestQuestionsForTestDesign(framework, lang, tqrMap, test_design_id, {readSelQ: true, expected_answer_mapping}, true);
        return this.publishExtractedQuestions(scoreEntryQs, tqrMap, test_design_id, lang, null, question_weight_mapping, questionRegisterDataMapping)
      case TestFormConstructionMethod.BUCKETS:
        return await this.findTestQuestionsForTestDesignBucket(framework, tqrMap, test_design_id, lang, null, questionRegisterDataMapping, expected_answer_mapping);
      case TestFormConstructionMethod.TLOFT:
        return await this.findTestQuestionsForTestDesignTLOFT(framework, tqrMap, test_design_id, lang, null, questionRegisterDataMapping, expected_answer_mapping);
      case TestFormConstructionMethod.LINEAR:
        const linerRegisteredQs = await this.findTestQuestionsForTestDesign(framework, lang, tqrMap, test_design_id, {readSelQ: true, expected_answer_mapping});
        return this.publishExtractedQuestions(linerRegisteredQs, tqrMap, test_design_id, lang, null, question_weight_mapping, questionRegisterDataMapping)
      case TestFormConstructionMethod.MSCAT:
      default:
        return await this.findTestQuestionsForTestForms(test_design_id, formIdMap, tqrMap, framework, expected_answer_mapping);
    }
   
  }

  async findTestQuestionsForTestForms(test_design_id: number, formIdMap: Map<number,any>, tqrMap: ITqrParamMap[], framework: any, expected_answer_mapping?: IExpectedAnsMap) {
    
    const results: Map<number, UpsertResult> = new Map();

    for (const [test_form_id, formObj] of formIdMap){

      const {lang} = formObj;
      const qIds:number[] = [];
      
      Object.keys(formObj.questionDb).map((qId:string | number) => qIds.push(+qId));

      // extract framework Params
      const frameworkParams = tqrMap.filter(PC => PC.param_type == 'framework').map(PC => PC.item_bank_code);      
      const frameworkParamSet = new Set(frameworkParams);

      let sectionItems: Record<string, any> = framework.sectionItems;
      let questionFrameworkParams = new Map();
      if (sectionItems){
        let keys = Object.keys(sectionItems);
        for (let k of keys) {
          let questions = sectionItems[k].questions;
          if (!questions || !Array.isArray(questions)) continue;
          questions.forEach(q => {
            // extract framework(sectionItems) params      
            const frameworkP:any = {}
            Object.keys(q).map(key => {
              if(frameworkParamSet.has(key)){
                frameworkP[key] = q[key]
              }
            });
            questionFrameworkParams.set(+q.id, frameworkP);
          })          
        }
      }
  
    
      const registeredQuestions: any = [];
      
      // prepare question data model 
      for(const id of qIds){
        let p: IDBQuestion = await this.app.service('db/read/test-questions').get(id);
        const { config } = p;
        const configMap = JSON.parse(config);

        //get scoring Info
        let pScore = await this.app
          .service('public/test-auth/test-question-scoring-info')
          .get(id, { query: { lang }});

        let registeredQuestion:any = {
          ...p,
          configMap,
          test_form_id,
          test_design_id, 
          lang,
          scoreInfo : pScore?.length ? pScore[0] : null 
        }

        if(!_.isEmpty(questionFrameworkParams.get(+id))) {
          registeredQuestion = {
            ...registeredQuestion,
            ...questionFrameworkParams.get(+id)
          }
        }

        // Determine eligibility - currently only excluding readingSelections
        if(this.isReadingSelectionPage(configMap, lang)) continue;
        if(expected_answer_mapping && !this.hasExpectedAnswer(expected_answer_mapping, p.id, pScore)) continue;

        registeredQuestions.push(registeredQuestion);
      }

      // extract TQR mappings and publish

      // upsert records in test_question_register 
      let promises: Promise<void>[] = [];
      let result: UpsertResult = {
        created: [],
        failed: [],
        updated: [],
      }

      for( const question of registeredQuestions){
        const registerFields =  this.extractRegisterFields(tqrMap, question, lang);
        // temp
        registerFields['test_design_id'] = test_design_id;
        registerFields['test_form_id'] = test_form_id;
        registerFields['lang'] = lang;
        const populate = this.publishRegisteredFields(registerFields, result);
        promises.push(populate);
      }

      await Promise.all(promises);
      results.set(test_form_id, result);
    }

    return results;
  }

  async findTestQuestionsForTestDesignBucket(framework: Record<any, any>, tqrMap: ITqrParamMap[], test_design_id: number, lang:ILanguage, test_form_id: number|null, questionRegisterDataMapping?: IQuestionRegisterDataMapping[], expected_answer_mapping?: IExpectedAnsMap){
    const results: Map<number, UpsertResult> = new Map();

    // get questions
    const questions = [];
    const  {quadrantItems} = framework;

    for(const quadrant of quadrantItems){
      const qS: number[] = quadrant?.questions?.map((o: any) => o.id);
      if(qS && qS.length) questions.push(...qS);
    }

    const registeredQuestions: any = [];

    // prepare question data model 
    for(const id of questions){
      let p: IDBQuestion = await this.app.service('db/read/test-questions').get(id);
      const { config } = p;
      const configMap = JSON.parse(config);

      if(this.isReadingSelectionPage(configMap, lang)) continue;

      //get scoring Info
      let pScore = await this.app
        .service('public/test-auth/test-question-scoring-info')
        .get(id, { query: { lang }});


      if(expected_answer_mapping && !this.hasExpectedAnswer(expected_answer_mapping, p.id, pScore)) continue;

      const registeredQuestion:any = {
        ...p,
        configMap,
        test_form_id,
        test_design_id, 
        lang,
        scoreInfo : pScore?.length ? pScore[0] : null
      }
      registeredQuestions.push(registeredQuestion);
    }

    // extract TQR mappings and publish

      // upsert records in test_question_register 
      let promises: Promise<void>[] = [];
      let result: UpsertResult = {
        created: [],
        failed: [],
        updated: [],
      }

      for( const question of registeredQuestions){
        const registerFields =  this.extractRegisterFields(tqrMap, question, lang);
        // temp
        registerFields['test_design_id'] = test_design_id;
        registerFields['test_form_id'] = test_form_id;
        registerFields['lang'] = lang;
        this.extractDataMappingToRegister(question, registerFields, questionRegisterDataMapping)
        const populate = this.publishRegisteredFields(registerFields, result);
        promises.push(populate);
      }

      await Promise.all(promises);
      return result;
  }

  extractDataMappingToRegister(registeredQuestion: any, registerFields: any, questionRegisterDataMapping?: IQuestionRegisterDataMapping[]) {
    try {
      if(!questionRegisterDataMapping || !questionRegisterDataMapping.length) {
        return;
      }
  
      questionRegisterDataMapping.forEach((dataMap) => {
        if(!dataMap.mapping || !dataMap.registerField) {
          return;
        }
  
        registerFields[dataMap.registerField] = dataMap.mapping[registeredQuestion.id]
      })
    } catch(err) {
      console.error(err);
    }
  }

  async findTestQuestionsForTestDesignTLOFT(framework: Record<any, any>, tqrMap: ITqrParamMap[], test_design_id: number, lang:ILanguage, test_form_id: number|null, questionRegisterDataMapping?: IQuestionRegisterDataMapping[], expected_answer_mapping?: IExpectedAnsMap){

    // get questions
    const  {testlets} = framework;
    
    const qIdSet: Set<number> = new Set();
    testlets?.forEach((testlet:any) => {
      testlet?.questions?.forEach((question:any) =>  qIdSet.add(question.id))
    })
    
    const questions: number[] = Array.from(qIdSet);

    const registeredQuestions: any = [];

    // prepare question data model 
    for(const id of questions){
      let p: IDBQuestion = await this.app.service('db/read/test-questions').get(id);
      const { config } = p;
      const configMap = JSON.parse(config);

      // Determine eligibility - currently only excluding readingSelections
      if(this.isReadingSelectionPage(configMap, lang)) continue;

      //get scoring Info
      let pScore = await this.app
        .service('public/test-auth/test-question-scoring-info')
        .get(id, { query: { lang }});

      if(expected_answer_mapping && !this.hasExpectedAnswer(expected_answer_mapping, p.id, pScore)) continue;

      const registeredQuestion:any = {
        ...p,
        configMap,
        test_form_id,
        test_design_id, 
        lang,
        // scoreInfo : pScore?.length ? pScore[0] : null
      }

      registeredQuestions.push(registeredQuestion);
    }

    return this.publishExtractedQuestions(registeredQuestions, tqrMap, test_design_id, lang, test_form_id, null, questionRegisterDataMapping)
  }

  async publishExtractedQuestions(registeredQuestions: any [], tqrMap: ITqrParamMap[], test_design_id: number, lang:ILanguage, test_form_id: number|null, question_weight_mapping?: IQuestionWeightMapping[] | null, questionRegisterDataMapping?: IQuestionRegisterDataMapping[]) {
    // extract TQR mappings and publish

      // upsert records in test_question_register 
      let promises: Promise<void>[] = [];
      let result: UpsertResult = {
        created: [],
        failed: [],
        updated: [],
      }

      for( const question of registeredQuestions){
        const registerFields =  this.extractRegisterFields(tqrMap, question, lang);
        // temp
        registerFields['test_design_id'] = test_design_id;
        registerFields['test_form_id'] = test_form_id;
        registerFields['lang'] = lang;
        if(question_weight_mapping && question_weight_mapping.length) {
          const questionWeight = question_weight_mapping.find((questionWeight) => questionWeight.questionId == question.id)
          if(questionWeight) {
            registerFields['score_points'] = questionWeight?.weight;
          }
        }
        registerFields['is_human_scored'] = question.scoreInfo?.is_human_scored || null;
        registerFields['batch_alloc_policy_id'] = question.scoreInfo?.batch_alloc_policy_id || null;
        this.extractDataMappingToRegister(question, registerFields, questionRegisterDataMapping)
        const populate = this.publishRegisteredFields(registerFields, result);
        promises.push(populate);
      }

      await Promise.all(promises);
      return result;
  }


  async publishRegisteredFields(registerFields: any, result: any) {
    // We cannot use REPLACE or INSERT...ON DUPLICATE KEY UPDATE 
    // because question_id is not a primary key for test_question_register
    let p = (this.app.service('db/read/test-question-register').find({
      query: {
        question_id: registerFields.question_id!,
        test_design_id: registerFields.test_design_id,
        test_form_id: registerFields.test_form_id,
        $limit: 1,  // we can assume that a question only appears once in a test form
      },
      paginate: false,
    })).then((tqr_records: any) => {

      // if no register exist for this question, create a new record
      if (tqr_records.length < 1) {
        return this.app.service('db/write/test-question-register')
          .create(registerFields)
          .then(() => {
            result.created.push(registerFields.question_id!);

            this.app.service('db/read/test-question-scoring-info')
              .find({
                query: {
                  item_id: registerFields.question_id!
                },
                paginate: false
            }).then((records:any) => {
              for (let record of records) {
                this.app.service('db/write/test-question-scales-register')
                  .create({
                    test_question_id: registerFields.question_id!,
                    test_design_id: registerFields.test_design_id,
                    score_profile_id: record.score_profile_id,
                    is_human_scored: record.is_human_scored,
                    lang: registerFields.lang
                  })
              }  
            })
          }).catch((e:any) => {
            result.failed.push(registerFields.question_id!);
          });
      }

      // if exists, update the record
      else {
        let register = tqr_records[0];
        return this.app.service('db/write/test-question-register')
          .patch(
            register.id,
            registerFields,
          ).then(() => {
            result.updated.push(registerFields.question_id!);
          }).catch(() => {
            result.failed.push(registerFields.question_id!);
          });
      }

    });

    return p;
  }

  async populateTestQuestionRegister(test_design_id: number, pubAssessmentType: AssessmentType, component_code:string ){

    // maybe should be based on the publishing lang?
    let language = await this.findLanguageForComponentCode(component_code);

    // find test design
    let test_design = await this.findTestDesign(test_design_id);

    // get the filtered TQR mapping
    const tqrMap = await this.findTqrMappings(pubAssessmentType, language)

    // find test design framework
    let framework = JSON.parse(test_design.framework);

    // find questions
    let questions = await this.findTestQuestionsForTestDesign(framework, language, tqrMap, test_design_id, {dBProp: true, metaParam: true});    
    
    // upsert records in test_question_register 
    let promises: Promise<void>[] = [];
    let result: UpsertResult = {
      created: [],
      failed: [],
      updated: [],
    }
    
    for (let question of questions) {


      let registerFields = this.extractRegisterFields(tqrMap, question, language);

      // We cannot use REPLACE or INSERT...ON DUPLICATE KEY UPDATE 
      // because question_id is not a primary key for test_question_register
      let p = (this.app.service('db/read/test-question-register').find({
        query: {
          question_id: registerFields.question_id!,
          test_design_id: test_design_id,
          $limit: 1,  // we can assume that a question only appears once in a test design
        },
        paginate: false,
      }) ).then((tqr_records: any) => {

        // if no register exist for this question, create a new record
        if (tqr_records.length < 1) {
          return this.app.service('db/write/test-question-register')
            .create(registerFields)
            .then(() => {
              result.created.push(registerFields.question_id!);
            }).catch((e:any) => {
              result.failed.push(registerFields.question_id!);
            });
        }

        // if exists, update the record
        else {
          let register = tqr_records[0];
          return this.app.service('db/write/test-question-register')
            .patch(
              register.id,
              registerFields,
            ).then(() => {
              result.updated.push(registerFields.question_id!);
            }).catch(() => {
              result.failed.push(registerFields.question_id!);
            });
        }

      });
      promises.push(p);
    }

    await Promise.all(promises);

    return result;
  }


  extractRegisterFields(tqrMap: ITqrParamMap[], question: any, lang: ILanguage) {
    
    let meta:any = question.configMap.meta;
    if (!meta) return {};
    
    for(let key of Object.keys(meta)) {
      if (meta[key]?.caption || meta[key]?.caption === '') {
        meta[key] = JSON.stringify(meta[key]);
      }
    }

    let registerFields: any = {};
    
    tqrMap.forEach(PC => {

      if (registerFields[PC.tqr_col] != null) {
        // value already filled Must be a duplicate!!!
        // maybe throw an error
      } else {
        // fill value for TQR params
        let val;
        
        // extract value form authoring param
        if (PC.param_type == 'item_meta') {
          val = meta[PC.item_bank_code]
          if(!val && PC.secondary_item_bank_codes){
            const secondaryVal = meta[PC.secondary_item_bank_codes]
            if(secondaryVal){
              val = secondaryVal;
            }
          }
        } else if (PC.param_type == 'item_config'){
          if(lang === 'fr'){
            val = question.configMap.langLink[PC.item_bank_code]
          } else {
            val = question.configMap[PC.item_bank_code]
          }
        } else if (PC.param_type == 'item_score'){
          val = question.scoreInfo?.[PC.item_bank_code]
        } else {
          val = question[PC.item_bank_code]
        }

        // extract and apply parsing rules if any
        if (PC.parsing_rules) {
          const rules = JSON.parse(PC.parsing_rules)

          if (rules.isBinary) val = this.boolToBinary(val);
          if (rules.isParsePossibleObject) val = this.parsePossibleObject(val);
          if (rules.isStringify) val = this.stringify(val);
          if (rules.isNumToChar) val = this.indexToChar(+val);
        }

        // Explicitly defining Nulls as for numeric column in mysql '' maps to 0
        if(typeof val == 'string' && val == '' ) val = null

        registerFields[PC.tqr_col] = val
      }
    });

    return registerFields
  }

  async findTqrMappings(pubAssessmentType: AssessmentType, language: string): Promise<ITqrParamMap[]> {
    
    const tqrMap = await this.app.service('public/test-question-register/tqr-map').find();
    
    const tqrFilteredParams =  tqrMap.filter((PC:any) => {
      const { options } = PC
      if(options){
        const {framework, publish } = JSON.parse(options)
        let checksAll = true;
        if(framework){
          const { assessment_type } = framework

          if(assessment_type && assessment_type != pubAssessmentType ){
            checksAll = false
          }
        }

        if(publish){
          const { lang } = publish
          
          if(lang && lang != language){
            checksAll = false
          }
        }

        if(checksAll) return PC

      } else {
        return PC
      }
    });

    return tqrFilteredParams
  }


  async findTestQuestionsForTestDesign(test_design_framework:Record<string, any>, lang: ILanguage, tqrMap: ITqrParamMap[], test_design_id:number, checks: IRealQuestionCheck, isScoreEntry = false){    

    const frameworkParams = tqrMap.filter(PC => PC.param_type == 'framework').map(PC => PC.item_bank_code);
    const itemBankParams  = tqrMap.filter(PC => PC.param_type == 'item_bank').map(PC => PC.item_bank_code); 
    
    const frameworkParamSet = new Set(frameworkParams);
    const itemBankParamSet = new Set(itemBankParams);

    // extract framework params
    const { componentCode } = test_design_framework 
    const { formCode } = test_design_framework

    
    let sectionItems: Record<string, any> = test_design_framework.sectionItems;
    if (!sectionItems) return [];

    let partitions: Record<string, any>[] = test_design_framework.partitions;
    if (!partitions) return [];

    let dbQuestions = []

    let keys = Object.keys(sectionItems);
    for (let k of keys) {
      let questions = sectionItems[k].questions;
      if (!questions || !Array.isArray(questions)) continue;
      const partition = partitions.filter(partition => partition.id == k)[0];
      if(!partition) {
        throw new Errors.NotFound("NO_PARTITION_MATCH");
      }
      // extract section-params
      const sectionP: any = {} 
      Object.keys(partition).forEach(key => {
        if(frameworkParamSet.has(key)){
          if(objParamToArray.includes(key)) {
            sectionP[key] = JSON.stringify(partition[key]);
          } else {
            sectionP[key] = partition[key] 
          }
        }
      })

      for (let q of questions) {
        if ('label' in q && 'id' in q) {
          let question_id = +(q.id);
          let p: IDBQuestion = await this.app.service('db/read/test-questions').get(question_id);
          let pScore = await this.app
          .service('public/test-auth/test-question-scoring-info')
          .get(question_id, { query: { lang }});

          // extract framework(sectionItems) params      
          const frameworkP:any = {}
          Object.keys(q).map(key => {
            if(frameworkParamSet.has(key)){
              if(objParamToArray.includes(key)) {
                frameworkP[key] = JSON.stringify(q[key]);
              } else {
                frameworkP[key] = q[key]
              }
            }
          });

          dbQuestions.push({
            ...p,
            ...sectionP,
            ...frameworkP,
            componentCode,
            formCode,
            test_design_id,
            lang,
            scoreInfo : pScore?.length ? pScore[0] : null 
          });
        }
      }
    }

    return this.filterQuestions(dbQuestions, lang, checks, isScoreEntry);

  }

  filterQuestions = (dbQuestions: any, lang: string, checks: IRealQuestionCheck, allowNonExpAns = false) => {

    const { dBProp, metaParam, readSelQ, expected_answer_mapping } = checks;
    // filter question : 
    let registeredQuestions = dbQuestions
    .map((q: any) => {
      // Top -level prop to determine if the question should be included in the TQR
      if(dBProp && !q.is_registered) return null;
      if (!q.config) return null;
      let config;
      try {
        config = JSON.parse(q.config);
        // Determine eligibility - currently only excluding readingSelections
        if(readSelQ && this.isReadingSelectionPage(config, lang)) return null;
      } catch {
        return null;
      }

      if (!config.meta) return null;
      if(!allowNonExpAns && expected_answer_mapping && !this.hasExpectedAnswer(expected_answer_mapping, q.id, q.scoreInfo)) {
        return null;
      }

      return {
        ...q,
        configMap: config,
      };
    })
    .filter((q: any) => q != null);

    return registeredQuestions
  }

  private parsePossibleObject = (val:any) => {
    try {
      if ( Array.isArray(val)){
        return val.join()  //JSON.stringify(val)
      }
      return val;
    }
    catch (e){
      return val;
    }
  }

  private stringify = (value: any): string => {
    if (typeof value === 'string') {
      return value;
    }
    if (typeof value === 'number') {
      return value.toString();
    }
    try {
      return JSON.stringify(value);
    } catch {
      return 'NOT_STRING_LIKE_VALUE';
    }
  }

  private boolToBinary(flag: boolean | null | undefined): (1 | 0) {
    if (flag) {
      return 1;
    }
    return 0;
  }
  
  private indexToChar = (index: number) => {
    // assuming options are limitted to 26 chars
    if(typeof index !== 'number' || index > 25) return '';
    
    const chars ='ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('')
    return chars[index];
  }

  async findLanguageForComponentCode(component_code: string): Promise<ILanguage> {
    let records = <any[]>await this.app.service('db/read/assessment-components').find({
      query: {
        assessment_code: component_code,
        $limit: 1,
      },
      paginate: false,
    })

    if (records.length < 1) {
      // console.log('coercing', component_code, 'to french');
      return 'fr';
      throw new Errors.NotFound("COMPONENT_CODE_NOT_FOUND");
    }

    return records[0].language == 'English' ? 'en' : 'fr';
  }

  async findTestDesign(test_design_id: number): Promise<ITestDesign> {
    let record = <ITestDesign>await this.app.service('db/read/test-designs').get(test_design_id);
    if (!record) {
      throw new Errors.NotFound("TEST_DESIGN_NOT_FOUND");
    }
    if (!record.framework) {
      throw new Errors.NotFound("EMPTY_TEST_DESIGN_FRAMEWORK");
    }
    return record;
  }

  isReadingSelectionPage(qConfig:any , lang:string) {
    if(lang === 'en') return qConfig?.isReadingSelectionPage
    return qConfig?.langLink?.isReadingSelectionPage
  }

  hasExpectedAnswer(expected_answer_mapping: IExpectedAnsMap, questionId: number, scoreInfo: any) {
    const hasExpectedAnswer = expected_answer_mapping.hasOwnProperty(questionId);
    // If no expected answer, confirm that question is not human scored
    if(!hasExpectedAnswer) {
      if(!scoreInfo || (scoreInfo && scoreInfo.is_human_scored != 1)) {
        return false;  
      }
    }
    return true;
  }
}
