export interface IDBQuestion {
    id: number,
    question_label: string,
    config: string,
    order: number,
    [key: string]: any,
}


export interface UpsertResult {
    created: number[],
    updated: number[],
    failed: number[],
}



export type ILanguage = 'en' | 'fr';

export interface ITestDesign {
    id: number,
    framework: string,
    source_item_set_id: number,
    [key: string]: any,
}


export enum TestFormConstructionMethod {
    LINEAR = 'LINEAR',
    TLOFT = 'TLOFT',
    MSCAT = 'MSCAT',
    BUCKETS = 'BUCKETS',
    SCORE_ENTRY = 'SCORE_ENTRY'
  }
