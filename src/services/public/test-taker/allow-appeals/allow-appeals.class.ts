import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Knex } from 'knex';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { getSysConstNumeric } from '../../../../util/sys-const-numeric';

interface Data {}

interface ServiceOptions {}

enum EAppealType {
  attempt = 'attempt',
  session = 'session'
}

export class AllowAppeals implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    const value = await getSysConstNumeric(this.app, 'SHOW_APPEALS');
    return value;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {

    throw new Errors.MethodNotAllowed();

    // if(!params || !params.query || !params.query.appealType) {
    //   throw new Errors.BadRequest('REQ_PARAMS_MISS');
    // }

    // const appealType = params.query.appealType;

    // let sessionId;
    // if(appealType === EAppealType.session) {
    //   sessionId = id;
    // } else if(appealType === EAppealType.attempt) {
    //   const attempt = await this.app.service('db/read/test-attempts').get(id);
    //   if(!attempt) {
    //     throw new Errors.BadRequest('INVALID_ID')
    //   }
    //   sessionId = attempt.test_session_id;
    // }

    // if(!sessionId) {
    //   throw new Errors.BadRequest('INVALID_ID');
    // }

    // const db:Knex = this.app.get('knexClientRead');
    // const getData = async (props:any[], query:string) => {
    //   const res = await db.raw(query, props);
    //   return <any[]> res[0];
    // }

    // const testWindow = await getData([sessionId],`
    // SELECT tw.is_allow_appeals
    // FROM mpt_dev.test_windows tw
    // JOIN mpt_dev.test_sessions ts ON ts.test_window_id = tw.id
    // WHERE ts.id = ?`);

    // if(!testWindow || testWindow.length === 0) {
    //   throw new Errors.BadRequest('INVALID_ID');
    // }
    // return testWindow[0].is_allow_appeals;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()

  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }
}
