import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import axios from 'axios';
import { generateS3DownloadUrl } from '../../../../upload/upload.listener';

interface Data {}

interface ServiceOptions {}

export class Download implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    const {lang} = (<any>params).query;
    const files:{[key:number]:string} = {
      0: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/2329038/authoring/help/1666375873474/help.json',
    }
    files[1] = lang == 'en' ? 'test_forms/pregen/8/new-practice-test/updated-practice-test-1.json' : 'test_forms/pregen/8/new-practice-test/updated-practice-test-1-fr.json' // updated practice test 1
    files[2] = lang == 'en' ? 'test_forms/pregen/8/new-practice-test/updated-practice-test-2-en.json' : 'test_forms/pregen/8/new-practice-test/updated-practice-test-2-fr.json'; // updated practice test 2 (en/fr)
    if (id){
      let fileUrl;
      if((<number>id)==0){
        fileUrl = files[<number>id];
      }else{
        fileUrl = generateS3DownloadUrl(files[<number>id], 60);  
      }
      if (fileUrl){
        const payload = await axios.get(fileUrl, {})
          .catch((e) => {
            throw new Errors.BadRequest(e);
          });
        return payload.data;
      }
    }
    throw new Errors.BadRequest();
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
