import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { ITestAttempt } from '../../../../db/schemas/test_attempts.schema';
import { TestAttempt } from '../test-attempt/test-attempt.class';
import { dbDateNow } from '../../../../../util/db-dates';
import { currentUid } from '../../../../../util/uid';
import { lzDecompressProps } from '../../../../../util/lzstring';

interface Data {}

interface ServiceOptions {}

export interface IEntry {
  uid:number, // who's test is this?
  created_by_uid?: number, // who is taking the action?
  test_attempt_id:number,
  test_question_id: number,
  test_question_version_id?:number,
  question_index: number,
  question_caption: string,
  section_index: number,
  module_id?: number,
  dest_section_index?:number,
  dest_question_index?:number,
  response_raw: string,
  response: number,
  isPaper: boolean,
  // isValidationOverride
}

export type QuestionResponseEntry = IEntry;

export class QuestionResponse implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create(data: QuestionResponseEntry, params?: Params): Promise<Data> {
    // to do: validate
    if (params && data) {
      // const { schl_class_group_id } = (<any>params).query;
      const uid = await currentUid(this.app, params);
      return this.submitQuestionReq(uid, data);
    }
    throw new Errors.BadRequest();
  }
  submitQuestionReq(uid:number, data: QuestionResponseEntry){
    data = lzDecompressProps(data, ['response_raw', 'response'])
    return this.submitQuestion(
      {
        ... this.getReqData(data),
        uid
      },
        // this.validateAttId
    )    
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if (params){
      return this.submitTest({
        ... this.getReqData(data),
        uid: await currentUid(this.app, params)
      })
    }
    throw new Errors.BadRequest();
  }

  getReqData(data:any){
    const {
      test_attempt_id,
      test_question_id,
      test_question_version_id,
      response_raw,
      response,
      question_index,
      question_caption,
      section_index,
      module_id,
      dest_section_index,
      dest_question_index,
      subsession_slug,
      subsession_order,
      isPaper
    } = data;

    return {
      test_attempt_id,
      test_question_id,
      test_question_version_id,
      response_raw,
      response,
      question_index,
      question_caption,
      section_index,
      module_id,
      dest_section_index,
      dest_question_index,
      subsession_slug,
      subsession_order,
      isPaper
      // subsession_index,
      // sections_allowed
    }
  }

  async submitQuestion(data:IEntry, validate:boolean=true){

    const {
      test_attempt_id,
      test_question_id,
      test_question_version_id,
      response_raw,
      response,
      question_index,
      question_caption,
      section_index,
      module_id,
      dest_section_index,
      dest_question_index,
      uid
    } = data;

    const created_by_uid = data.created_by_uid || uid;
    const isOnBeHalf = (created_by_uid !== uid);

    // validate
    if (validate){
      await this.app
        .service('public/test-taker/invigilation/test-attempt')
        .validateAttemptId(<number>uid, test_attempt_id, isOnBeHalf);
    }
    
    // update location values in attempt
    
    const testAttemptPatch:any = {
      question_index,
      question_caption,
      section_index,
    }
    if (module_id){
      testAttemptPatch.module_id = module_id;
    }
    await this.app
      .service('db/write/test-attempts')
      .patch(test_attempt_id, testAttemptPatch)

    // update the question responses
    const questionResponse = {
      test_attempt_id,
      test_question_id,
      test_question_version_id,
      section_id: section_index,
      response_raw,
      response,
      updated_on: dbDateNow(this.app),
      updated_by_uid: created_by_uid,
    }
    const attempts_q_res =  <any[]> await this.app
      .service('db/write/test-attempt-question-responses')
      .find({query:{test_attempt_id, test_question_id}, paginate: false});

    await Promise.all(
      attempts_q_res.map(aqr =>{
        return this.app
          .service('db/write/test-attempt-question-responses')
          .patch(aqr.id, questionResponse)
      })
    )
    if (attempts_q_res.length === 0){
      await this.app
        .service('db/write/test-attempt-question-responses')
        .create(questionResponse)
    }
    return true
  }

  async submitTest(data:Partial<IEntry>, validate:boolean=true){

    const {
      test_attempt_id,
      uid,
    } = <IEntry> data;

    const created_by_uid = data.created_by_uid || uid;
    const isOnBeHalf = (created_by_uid !== uid);

    // validate
    if (validate){
      await this.app
        .service('public/test-taker/invigilation/test-attempt')
        .validateAttemptId(uid, test_attempt_id, isOnBeHalf);
    }

    // update location values in attempt
    this.app.service('public/test-taker/invigilation/test-attempt').closeAttempt(test_attempt_id, created_by_uid);

    return []
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
