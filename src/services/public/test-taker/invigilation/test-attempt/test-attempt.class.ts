import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { ITestDesignPayload } from './test-design.data';
import {HEADER_SEB_REQ} from '../../../../../constants/headers'
import { ITestSessionBookingUser } from '../../../../db/schemas/test_session_booking_users.schema';
import { ITestAttempt, ITestAttemptInfo } from '../../../../db/schemas/test_attempts.schema';
import { generateSecretCode, hashValues } from '../../../../../util/secret-codes';

import { dbDateToMoment } from '../../../../../hooks/_util';
import { ITestSession } from '../../../../db/schemas/test_sessions.schema';
import { isDatePast, dbDateNow } from '../../../../../util/db-dates';
import { ITestWindow } from '../../../../db/schemas/test_windows.schema';
import { currentUid } from '../../../../../util/uid';
const _ = require('lodash');
import Bluebird from 'bluebird';
import { generateS3DownloadUrl } from '../../../../upload/upload.listener';
import axios from 'axios';

// import frameworkFromFile from './temp-framework.json';

interface Data {}

interface ServiceOptions {}

interface IGetTestAttemptOptions {
  isCreateNewIfEmpty:boolean,
  isTestTaker:boolean,
  isPresent: boolean,
  setBookingLang?: string
}

export interface IAttemptPayload {
  uid: number,
  test_session_id: number,
  lang: string,
  created_by_uid?: number,
}

interface IQueryConfig {
  test_session_id:number,
  lang:string
}

export class TestAttempt implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // private isSeedRunning:boolean = false;
  // async seed (){
  // }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const { test_session_id, lang } = params.query;
      return this.findAttemptPayload({
        uid: await currentUid(this.app, params),
        test_session_id,
        lang
      })
    }
    throw new Errors.BadRequest();
  }

  public async findAttemptPayload(data: IAttemptPayload){

    const {
      uid,
      test_session_id,
    } = data;

    const created_by_uid = data.created_by_uid || uid;
    const isOnBeHalf = (created_by_uid !== uid);

    const bookingRecord = await this.validateTSBooking(uid, test_session_id);
    const date_time_start = bookingRecord.date_time_start;

    // look for an active attempt, else create one
    let currentAttempt:ITestAttempt = await this.getCurrentAttempt(uid, test_session_id, {isCreateNewIfEmpty:true, isTestTaker:!isOnBeHalf, isPresent: true});
    await this.identifyPreAssessmentStep(currentAttempt, true); // if this does not throw an error, it means there are no more pre-setup steps left to be done

    /*
    if (!currentAttempt.started_on){
     currentAttempt = await this.app
      .service('db/read/test-attempts')
      .patch(currentAttempt.id, {started_on: dbDateNow(this.app)});
    }
    */

    let testFormId = currentAttempt.test_form_id; // = await this.getTestFormId(uid);
    let testDesign:ITestDesignPayload = await this.getTestDesign(testFormId, currentAttempt.lang);

    // load question responses / states
    const questionResponseStates =  <any[]> await this.app
      .service('db/write/test-attempt-question-responses')
      .find({query:{test_attempt_id: currentAttempt.id}, paginate: false})
    const questionStates:{[key:string]: any} = {};
    questionResponseStates.forEach(q => {
      questionStates[q.test_question_id] = JSON.parse(q.response_raw)
    })

    // move this into the test window configuration
    const is_issue_reporting_enabled =  0;

    const test_session = <ITestSession[]> await this.app
      .service('db/read/test-sessions')
      .db()
      .where('id', test_session_id)
      .limit(1);
    const test_session_time_ext_m = test_session[0].time_ext_m || 0;
    const user_time_ext_m = currentAttempt.time_ext_m || 0;
    const sections_meta = currentAttempt.sections_meta

    const test_window =  <ITestWindow[]> await this.app
      .service('db/read/test-windows')
      .db()
      .where('id', test_session[0].test_window_id)
      .limit(1);

    const test_window_time = test_window[0].duration_m || 0;

    let testDesignRecord = {framework: {}};
    if(test_window[0].test_design_id != undefined){
      testDesignRecord = await this.app .service('db/read/test-designs').get(test_window[0].test_design_id);
      if (!testDesignRecord){
        throw new Errors.NotFound();
      }
    }
    const session = await this.app.service('db/read/test-sessions').get(test_session_id);
    // return the payload
    return [
      {
        lang: currentAttempt.lang,
        question_index: currentAttempt.question_index||0,
        section_index: currentAttempt.section_index||0,
        attempt_key: currentAttempt.attempt_key,
        attemptId: currentAttempt.id,
        testDesign: testDesign,
        startedOn: currentAttempt.started_on,
        is_issue_reporting_enabled,
        date_time_start,
        questionStates,
        test_window_time,
        time_ext_m: user_time_ext_m + test_session_time_ext_m,
        sections_meta,
        framework: testDesignRecord.framework,
        is_test_session_softlock_disabled: session.is_soft_lock_disabled,
      }
    ];
  }

  private async identifyPreAssessmentStep(currentAttempt:ITestAttempt, markAsPresent:boolean){
    // ensure tt is marked as present
    if (markAsPresent && currentAttempt.is_present === 0){
      await this.app
        .service('db/write/test-attempts')
        .patch(currentAttempt.id, {
          is_present: 1,
          is_absent: 0,
        });
    }
    // ensure tt has accepted attestation (to do: this should only apply if the test session requires attestation)
    if (!currentAttempt.is_attested){
      throw new Errors.Forbidden('ACCEPT_ATTEST');
    }
    // ensure tt has identified their language (to do: this might be fine to leave as is when language is pre-selected for tt, as long as it is indicated in the test-attempt entry by the system before this request is made)
    if (!currentAttempt.lang){
      throw new Errors.Forbidden('SELECT_LANG');
    }
  }

  public async validateTSBooking(uid:number, test_session_id:number){
    const bookingRecords = <ITestSessionBookingUser[]> await this.app
      .service('db/read/test-session-booking-users')
      .db()
      .where('uid', uid)
      .where('test_session_id', test_session_id)
      .limit(1);
    if (bookingRecords.length === 0){
      throw new Errors.Forbidden('NOT_BOOKED_APPL');
    }
    return bookingRecords[0];
  }

  public async getTestFormId(uid:number){
    const testFormId = 1*uid;
    return testFormId;
  }

  public async getTestDesignFramework(testDesignId:number){
    const testDesign = await this.app.service('db/read/test-designs').get(testDesignId);
    return testDesign.framework;
  }

  public async getTestDesign(testFormId:number, lang:string): Promise<ITestDesignPayload>{
    return this.getTestForm(testFormId);
  }

  public async getTestForm(testFormId:number): Promise<ITestDesignPayload>{
    const testFormRecord = await this.app.service('db/read/test-forms').get(testFormId);
    const url = generateS3DownloadUrl(testFormRecord.file_path, 60);
    const res = await axios.get(url, {});
    return res.data;
  }

  public async validateAttemptId(uid:number, attempt_id:number, isOnBehalf:boolean=false){
    const attemptRecord = <ITestAttemptInfo> await this.app
      .service('db/read/test-attempts-info')
      .get(attempt_id);

    if (!attemptRecord){
      throw new Errors.Forbidden('NOT_BOOKED_APPL');
    }
    if (attemptRecord.is_identity_missing && !isOnBehalf){
      throw new Errors.Forbidden('MARKED_NO_ID');
    }
    if (attemptRecord.is_absent && !isOnBehalf){
      throw new Errors.Forbidden('MARKED_ABSENT');
    }
    if (!attemptRecord.is_identity_verified && !isOnBehalf){
      throw new Errors.Forbidden('NOT_VERIFIED');
    }
    if (attemptRecord.is_paused) {
      throw new Errors.Forbidden('ATTEMPT_PAUSED')
    }
    if(attemptRecord.is_session_paused) {
      throw new Errors.Forbidden('SESSION_PAUSED')
    }
    if (attemptRecord.is_closed){
      throw new Errors.Forbidden('ATTEMPT_CLOSED');
    }
    if (attemptRecord.is_session_closed){
      throw new Errors.Forbidden('SESSION_CLOSED');
    }

    const test_session_time_ext_m = attemptRecord.session_time_ext_m || 0;
    const user_time_ext_m = attemptRecord.time_ext_m || 0;
    let momentTimeStart = dbDateToMoment(attemptRecord.date_time_start);

    const test_sessions = <ITestSession[]> await this.app
    .service('db/read/test-sessions')
    .db()
    .where('id', attemptRecord.test_session_id)
    .limit(1);

    const test_window =  <ITestWindow[]> await this.app
    .service('db/read/test-windows')
    .db()
    .where('id', test_sessions[0].test_window_id)
    .limit(1);

    const isActive = test_window[0].is_active;
    const isArchived = test_window[0].is_archived;

    if (!isActive){
      throw new Errors.Forbidden('TEST_WINDOW_INACTIVE');
    }
    if (isArchived){
      throw new Errors.Forbidden('TEST_WINDOW_ARCHIVED');
    }

    const test_window_time = test_window[0].duration_m || 0;

    const timeClose = momentTimeStart.add(test_session_time_ext_m+user_time_ext_m+test_window_time, 'minutes');

    if (isDatePast(timeClose.format())){
      throw new Errors.Forbidden('TIME_OUT');
    }

    // this would indicate that it is the first time that this student is accessing this attempt

    return attemptRecord;
  }


  public async generateTestFormId(test_session_id: number, lang: string|null) {
    let query:any = {
      test_session_id,
      $limit:1000
    }
    if(lang !== null) {
      query.test_form_lang = lang;
    }

    const availableTestForms = <Paginated<any>> await this.app.service('db/read/test-session-test-forms').find({query});
    const i = Math.floor(availableTestForms.data.length * Math.random());
    return availableTestForms.data[i].test_form_id;
  }

  public async createAttempt(uid:number, isTestTaker:boolean, test_session_id:number, lang:string | null, booking_lang: string | null, is_present:number = 1){
    const test_form_id = await this.generateTestFormId(test_session_id, lang);

    const createFields:Partial<ITestAttempt> = {
      uid,
      test_session_id,
      lang: <any>lang,
      booking_lang: <any>booking_lang,
      section_index: 0,
      question_index: 0,
      test_form_id,
      attempt_key: generateSecretCode(5),
      test_form_cache: '',
      is_present,
      is_absent: 0,
    }

    /*
    if (isTestTaker){
      createFields.started_on = dbDateNow(this.app);
    }
    */

    // console.log('CREATE');

    return this.app
      .service('db/write/test-attempts')
      .create(createFields)
  }

  public validateSessionKey(uid:number, test_session_id:number, timestamp_start:number, sessionHash:string){
    return (sessionHash === this.generateSessionKey(uid, test_session_id, timestamp_start));
  }

  public  generateSessionKey(uid:number, test_session_id:number, timestamp_start:number){
    const sessionHash = hashValues([uid, test_session_id, timestamp_start])
    return sessionHash
  }

  public async getCurrentValidAttempt(uid:number, test_session_id:number, options:IGetTestAttemptOptions)  {
    await this.validateTSBooking(uid, test_session_id);
    return this.getCurrentAttempt(uid, test_session_id, options);
  }

  public async markAttemptAsAbsent (uid: number, test_session_id: number) {
    return await this.app
      .service('db/write/test-attempts')
      .db()
      .where({uid: uid, test_session_id: test_session_id})
      .limit(1)
      .update({
        is_absent: 1
      });
  }

  public async getCurrentAttempt(uid:number, test_session_id:number, options:IGetTestAttemptOptions)  {
    const attempts = <ITestAttempt[]> await this.app
      .service('db/read/test-attempts')
      .find({query: {uid, test_session_id, is_invalid: {$ne: 1}, $limit:1}, paginate: false })
    if (attempts.length > 0){
      const attempt = attempts[0];
      if(options.setBookingLang) {
        await this.app
        .service('db/write/test-attempts')
        .patch(attempt.id, {booking_lang: options.setBookingLang});
      }
      return attempt;
    }
    if (options.isCreateNewIfEmpty){
      const present_bool_int = options.isPresent ? 1 : 0;
      return await this.createAttempt(uid, options.isTestTaker, test_session_id, null, options.setBookingLang? options.setBookingLang : null, present_bool_int);
    }
  }

  private getItemDataMap (testDesign:any, testDesignFramework:any) {
    const testlets = _.filter(testDesignFramework.testlets, (testlet:any) => _.includes(testDesign.testletIds, testlet.id));
    const itemDataMap:any = {};
    _.each(testlets, (testlet:any) => {
      _.each(testlet.questions, (question:any) => {
        const section = _.find(testDesign.sections, (section:any) => _.includes(section.questions, question.id));
        itemDataMap[question.label] = {
          section_id: testlet.section,
          section_form_order: section.questions.indexOf(question.id),
          testlet_id: testlet.id,
          quadrant_id: testlet.quadrant
        }
      })
    });
    return itemDataMap;
  }

  public async closeAttempt (attempt:ITestAttempt|number, closedByUid:number, forceClose:boolean = false, cache:any = {}) {
    let resolvedAttempt:ITestAttempt;
    if (typeof attempt === 'number') {
      resolvedAttempt = await this.app.service('db/write/test-attempts').get(attempt);
    } else {
      resolvedAttempt = attempt;
    }

    try {
      await this.app.service('db/write/test-attempts').patch(resolvedAttempt.id, {
        is_closed: 1,
        is_submitted: forceClose ? 1 : undefined,
        closed_on: dbDateNow(this.app),
        last_updated_by_uid: closedByUid
      });
    } catch (e) {
      // TODO what if attempt is already closed?
    }

    if (!cache.testSessions) {
      cache.testSessions = {};
    }
    if (!cache.testSessions[resolvedAttempt.test_session_id]) {
      cache.testSessions[resolvedAttempt.test_session_id] = await this.app.service('db/read/test-sessions').get(resolvedAttempt.test_session_id);
    }
    const testSession = cache.testSessions[resolvedAttempt.test_session_id];

    if (!cache.testWindow) {
      cache.testWindow = await this.app.service('db/read/test-windows').get(testSession.test_window_id);
    }
    const testWindow = cache.testWindow;

    if (!cache.testDesignFramework) {
      cache.testDesignFramework = await this.getTestDesignFramework(testWindow.test_design_id);
      if (_.isString(cache.testDesignFramework)) {
        cache.testDesignFramework = JSON.parse(cache.testDesignFramework);
      }
      if (!cache.testDesignFramework.testlets) {
        cache.testDesignFramework.testlets = [];
      }
    }
    const testDesignFramework = cache.testDesignFramework;


    const testDesign = await this.getTestDesign(resolvedAttempt.test_form_id, resolvedAttempt.lang);
    const itemDataMap = this.getItemDataMap(testDesign, testDesignFramework);
    const existingResps = <any[]> await this.app.service('db/write/test-attempt-question-responses').find({
      query: {
        test_attempt_id: resolvedAttempt.id
      },
      paginate: false
    });
    await Bluebird.mapSeries(existingResps, (response) => {
      const testletData = itemDataMap[response.test_question_id];
      if (!testletData) {
        return;
      }
      return this.app.service('db/write/test-attempt-question-responses').patch(response.id, testletData)
    })
    const skippedQuestionIds = _(testDesign.questionDb)
      .keys()
      .differenceWith(existingResps, (idFromTestDesign:number, response:{test_question_id:number}) => {
        return idFromTestDesign == response.test_question_id
      })
      .value()
    await Bluebird.mapSeries(skippedQuestionIds, (qId:number) => {
      return this.app.service('db/write/test-attempt-question-responses').create(
        _.assign(
          {
            test_attempt_id: resolvedAttempt.id,
            test_question_id: qId,
            test_question_version_id: qId,
            response_raw: null,
            response: null,
            updated_on: dbDateNow(this.app),
            is_not_seen: true
          },
          itemDataMap[qId]
        )
      )
    });
    //generate report
    const isAuto:boolean = (closedByUid !== resolvedAttempt.id );
    await this.app.service('public/test-taker/report/results').ensureReport(resolvedAttempt.id , closedByUid, isAuto);
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    const sessionMeta = JSON.stringify(data)
    return this.app.service('db/write/test-attempts').patch(id, { sections_meta: sessionMeta });
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }
}
