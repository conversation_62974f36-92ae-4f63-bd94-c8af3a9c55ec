import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { ITestSessionDashboardInfo } from '../../../../db/schemas/test_sessions_dashboard_info.schema';
import { dbDateOffsetHours, dbDateOffsetDays, isDatePast } from '../../../../../util/db-dates';
import { SESSION_REGISTRATION_BUFFER_DAYS } from '../../../../../constants/db-limits';
import { DB_MAX_INSTITUIONS_LIST_TT, DB_MAX_SESSIONS_LIST_TT } from '../../../../../constants/db-limits';
import { IInstitution } from '../../../../db/schemas/institutions.schema';
import { ITestSession } from '../../../../db/schemas/test_sessions.schema';
import { ITestSessionAvailTT, ITestSessionAvailTTExt } from '../../../../db/schemas/test-session-avail-tt.schema';
import { Agent } from 'http';
import { MemoGet } from '../../../../../util/memo';
import { ITestWindow } from '../../../../db/schemas/test_windows.schema';
import { MPT_TEST_CTRL_GROUP_ID } from '../../../../../constants/test-ctrl-constant';
import { dbRawRead } from '../../../../../util/db-raw';
import { getSysConstNumeric } from '../../../../../util/sys-const-numeric';
import { getSysConstString } from '../../../../../util/sys-const-string';
//import { MPT_TEST_CTRL_GROUP_ID } from '../../../test-admin/test-session-setup/test-windows/test-windows.class';

const _ = require('lodash');
interface ITestWindowSummary {
  test_window_id: number,
  test_window_date_start: string,
  test_window_date_end: string,
  is_allow_new_bookings: boolean,
}



let memoPayload = new MemoGet(1000*60*10);
const WAITLIST_FLAG_KEY = 'ENABLE_WAITLIST';

interface Data extends IAvailableBookings { }
  
export interface IAvailableBookings {
  institutions:IInstitution[],
  sessions:ITestSessionAvailTT[],
  age?: number
}

interface ServiceOptions {}

export class All implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
    // const query = (<any>params).query;
    // const institutions = await this.app
    //   .service('db/read/institutions')
    //   .db()
    //   .select([
    //     'date_start',
    //     'instit_group_id',
    //     'address',
    //     'phone',
    //     'capacity',
    //     'num_bookings',
    //   ])
    // const sessions = await this.app
    //   .service('db/read/test-sessions')
    //   .db()

    // const queryTestSessionFields:Partial<ITestSession> = { 
    //   is_cancelled: 0,
    //   date_time_start: <any> {
    //     $gt: dbDateOffsetDays(this.app, -SESSION_REGISTRATION_BUFFER_DAYS)
    //   }
    // }

    // DB_MAX_TT_SESSION_LOAD
    // const sessionsRecords = <Paginated<ITestSessionDashboardInfo>> await dbTestSessionsRead.find({ query:queryTestSessionFields })
    // const sessions = sessionsRecords.data
    // return sessions;
    throw new Error();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    
    if (memoPayload.isStillFresh()){
      return memoPayload.getCachedPayload();
    }
    const MAX_WINDOWS_TO_LIST:number = 30;
    const testWindows:ITestWindowSummary[] = [];
    const tw_records = <ITestWindow[]> await this.app
      .service('db/read/test-windows')
      .db()
      .where('is_archived', 0)
      .where('test_ctrl_group_id', MPT_TEST_CTRL_GROUP_ID) 
      .limit(MAX_WINDOWS_TO_LIST);
    tw_records.forEach(twr => {
      const test_window_id = <number> twr.id
      const testWindowSummary:ITestWindowSummary = {
        test_window_id,
        test_window_date_start: <string> twr.date_start,
        test_window_date_end: <string> twr.date_end,
        is_allow_new_bookings: twr.is_allow_new_bookings ? true: false,
      }
      testWindows.push(testWindowSummary);
    })


    // get the buffer days
    const TEST_SESSION_REG_BUFFER_DAYS = +(await getSysConstString(this.app, 'INSTIT_BUFFER_POLICY'));

    // pull the institutions
    const institutions = await this.app
      .service('db/read/institutions')
      .db()
      .where('is_active', 1)
      .where('is_shown', 1)
      .limit(DB_MAX_INSTITUIONS_LIST_TT)
      
    // pull the test sessions
    const sessions = <ITestSessionAvailTTExt[]> await this.app
      .service('db/read/test-session-avail-tt')
      .db()
      .where('is_hidden', 0)
      .where('date_time_start', '>', dbDateOffsetDays(this.app, 0)) // cannot be in the past
      // .where('date_time_start', '>', dbDateOffsetDays(this.app, TEST_SESSION_REG_BUFFER_DAYS))
      .limit(DB_MAX_SESSIONS_LIST_TT)

    sessions.forEach(session => {
      session.videostream_link = undefined;
      session.videostream_password = undefined;
    })

    const sessionsSanitized:ITestSessionAvailTTExt[] = [];

    const creditsEnabled: any = await this.app
      .service('public/credits/credit-system')
      .find()
    
    if (creditsEnabled.isEnabled) {
      await Promise.all(
        sessions.map(async (ts) => {
          let institution = _.find(institutions, {group_id: ts.instit_group_id});
          if (institution){
            let ts_reg_buffer = institution.reg_buffer_policy || TEST_SESSION_REG_BUFFER_DAYS;
            if (isDatePast(ts.date_time_start, ts_reg_buffer) ){
              ts.is_expired = 1;
            }
          }
          
          const sessionPendings = await dbRawRead(this.app, [ts.id], `
            SELECT ts.id as test_session_id, ur.group_id, ur.pending
            FROM test_sessions as ts, 
              (SELECT group_id, count(*) as 'pending' from user_roles
              WHERE role_type = 'mpt_pending_booking_applicant' and is_revoked = 0
              group by group_id) as ur
            WHERE ts.test_session_group_id = ur.group_id
              AND ts.id = ?
          ;`)
          ts.pending = sessionPendings[0] ? sessionPendings[0].pending : 0;
          ts.pending = await this.app
            .service('public/test-taker/test-sessions/pending')
            .getNumPendingsForSession(ts.id)
          sessionsSanitized.push(ts)
        })
      )
    } else {
      // mark the ones that have closed
      sessions.forEach(ts => {
        let institution = _.find(institutions, {group_id: ts.instit_group_id});
        if (institution){
          let ts_reg_buffer = institution.reg_buffer_policy || TEST_SESSION_REG_BUFFER_DAYS;
          if (isDatePast(ts.date_time_start, ts_reg_buffer) ){
            ts.is_expired = 1;
          }
          sessionsSanitized.push(ts)
        }
      })
    }

    const paymentsEnabled = await this.app
      .service('public/credits/payment-system')
      .get(6);

    let isWaitlistEnabled = true;

    if (creditsEnabled.isEnabled && paymentsEnabled) isWaitlistEnabled = await getSysConstNumeric(this.app, WAITLIST_FLAG_KEY);

    return memoPayload.cachePayload({
      institutions: institutions,
      sessions: sessionsSanitized,
      testWindows: testWindows,
      isWaitlistEnabled
    });
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Error();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Error();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Error();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Error();
  }
}
