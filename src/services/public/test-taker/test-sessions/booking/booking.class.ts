const _ = require('lodash');

import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { DBD_U_ROLE_TYPES } from '../../../../../constants/db-extracts';
import { ITestSessionBookingUser } from '../../../../db/schemas/test_session_booking_users.schema';
import { ITestSessionAvailTT } from '../../../../db/schemas/test-session-avail-tt.schema';
import { Knex } from 'knex';
import { ITestWindow } from '../../../../db/schemas/test_windows.schema';
import { dbDateNow, isDatePast } from '../../../../../util/db-dates';
import { DB_MAX_TEST_WINDOWS_ACTIVE_OVERLAP } from '../../../../../constants/db-limits';
import { IInstitution } from '../../../../db/schemas/institutions.schema';
import { ITestSession } from '../../../../db/schemas/test_sessions.schema';
import { ICreditTransaction } from '../../../../db/schemas/credit-transactions.schema';
import { ITestAttempt, ITestAttemptInfo } from '../../../../db/schemas/test_attempts.schema';
import { renderDateTime } from '../../../../../hooks/_util';
import * as DBT from "../../../../../types/db-types";
//import { MPT_TEST_CTRL_GROUP_ID } from '../../../test-admin/test-session-setup/test-windows/test-windows.class';
import { dbRawRead } from '../../../../../util/db-raw';
import { MPT_TEST_CTRL_GROUP_ID } from '../../../../../constants/test-ctrl-constant';
import { ICapObject } from '../../../../auth/oct-cap/oct-cap.class';
import { ITransaction } from '../../../transactions/stripe/stripe.class';
import { getSysConstNumeric } from '../../../../../util/sys-const-numeric';

interface Data {}
const INSTIT_BUFFER_POLICY = 'INSTIT_BUFFER_POLICY'

export interface IBookedSession {
  isNoBookings?: boolean;
  testSession?: ITestSessionAvailTT,
  institution?: IInstitution,
  isWaitlist?: boolean,
  isResultsReceived?: boolean,
  isWaitingForResults?: boolean,
  isTestDay?: boolean,
  isTestPast?: boolean,
  isAccommPending?: boolean,
  attemptKey?: string,
  responses?: DBT.DATETIME
}
export interface IBookingRecord {
  booking? :  Partial<ITestSessionBookingUser>, 
  smcsBooking? :  Partial<ITestSessionBookingUser | ITestSessionBookingUser[]>, 
  waitlist? : Partial<ITestSessionBookingUser>
  pending? : any
}

export interface ICreateBooking {
  test_session_id: number,
  access_code: string,
  lang: string
}

export interface IPatchCredit {
  uid: number,
  creditId: number
}

export interface ICreditInfo {
  uid: number,
  oct_id: number,
  credit_id: number
}

interface ServiceOptions {}

export class Booking implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // dashboard
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {

    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS_REQ');
    }
    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    const uid = <number> userInfo.uid;

    let isSmcs = params.query?.smcs;
    // get bookings
    let test_window_ids:number[];
    try {
      test_window_ids = await this.getUpcomingTestWindowIds();
    }
    catch (e){
      throw new Errors.Forbidden('NO_VALID_TEST_WINDOWS')
    }

    const creditsEnabled:any = await this.app
        .service('public/credits/credit-system')
        .find();

    //console.log(test_window_ids)
    const activeBooking = await this.getActiveBooking(uid, test_window_ids, creditsEnabled.isEnabled, isSmcs);
    let payload:IBookedSession = {};

    let accom;
    if(activeBooking.booking && activeBooking.booking.test_session_id) {
      accom = await this.app.service('public/test-taker/accommodations/pending-req').findAccommReqForTestSession(uid, activeBooking.booking.test_session_id);
      if(accom && accom.id) {
        payload = {
          isAccommPending: true,
          ... await this.app.service('public/test-taker/accommodations/pending-req').getActiveRequestResponse(uid, accom.group_id, <number>activeBooking.booking.test_session_id),
        };
      }
    }

    const includeInstitInfo = !accom || (params.query && params.query.include_instit_info );

    if (activeBooking.booking && !activeBooking.waitlist){
      
      let smcsBookings = [];
      let smcsTestSession;
      let smcsIsTestDay;
      let smcsIsTestPast;
      let smcsIsPastBufferStart
      if(isSmcs && activeBooking.smcsBooking && !Array.isArray(activeBooking.smcsBooking)) activeBooking.booking = activeBooking.smcsBooking;
      else if(isSmcs && activeBooking.smcsBooking && Array.isArray(activeBooking.smcsBooking)){
        for(let booking of activeBooking.smcsBooking){
          if(booking == undefined) continue;
          smcsTestSession = await this.getTestSessionByGroupId(<number> booking.group_id);
          smcsIsTestDay = isDatePast(smcsTestSession.date_time_start, 12/24 ); // 12 hours before
          smcsIsTestPast = isDatePast(smcsTestSession.date_time_start, -12/24 ); // 12 hours after
    
    
          // check if they have completed an attempt associated with this test sessions
          const completedAttempts = <ITestAttemptInfo[]> await this.app
            .service('db/read/test-attempts-info') //
            .db()
            .where('uid', uid)
            .where('test_session_id', smcsTestSession.id)
            .whereRaw(`(is_closed = 1 OR is_session_closed = 1)`)
            // .limit(1);
    
          let isWaitingForResults = false;
          let isResultsReceived = false;
          let test_attempt_id;
          if (completedAttempts.length > 0){
            const lastCompletedAttempt = completedAttempts[0];
            test_attempt_id = lastCompletedAttempt.id;
            const testWindow = await this.getTestWindow(smcsTestSession.id);
            if (lastCompletedAttempt.is_results_released === 1 && testWindow.is_allow_results_tt === 1) {
              isResultsReceived = true;
            }
            else{
              isWaitingForResults = true;
            }
          }
          const extension = await this.getTestWindow(smcsTestSession.id, true);
          let bookingBuffer = await this.getBufferDays(smcsTestSession);
          smcsIsPastBufferStart = creditsEnabled ? isDatePast(smcsTestSession.date_time_start, bookingBuffer) : null;
          smcsBookings.push({
            smcsTestSession,
            smcsIsTestDay,
            smcsIsTestPast,
            isWaitingForResults,
            isResultsReceived,
            test_attempt_id,
            smcsIsPastBufferStart,
            extension: extension.time_ext_m
          })
        }
      }
      const testSession = await this.getTestSessionByGroupId(<number> activeBooking.booking.group_id);
      const isTestDay = isDatePast(testSession.date_time_start, 12/24 ); // 12 hours before
      const isTestPast = isDatePast(testSession.date_time_start, -12/24 ); // 12 hours after

      // check if they have completed an attempt associated with this test sessions
      const completedAttempts = <ITestAttemptInfo[]> await this.app
        .service('db/read/test-attempts-info') //
        .db()
        .where('uid', uid)
        .where('test_session_id', testSession.id)
        .whereRaw(`(is_closed = 1 OR is_session_closed = 1)`)
        // .limit(1);

      let isWaitingForResults = false;
      let isResultsReceived = false;
      let test_attempt_id;
      if (completedAttempts.length > 0){
        const lastCompletedAttempt = completedAttempts[0];
        test_attempt_id = lastCompletedAttempt.id;
        const testWindow = await this.getTestWindow(testSession.id);

        if (lastCompletedAttempt.is_results_released === 1 && testWindow.is_allow_results_tt === 1) {
          isResultsReceived = true;
        }
        else{
          isWaitingForResults = true;
        }
      }

      let bookingBuffer = await this.getBufferDays(testSession);
      const isPastBufferStart = creditsEnabled.isEnabled ? isDatePast(testSession.date_time_start, bookingBuffer) : null;
      const booking = {
        smcsBookings,
        testSession,
        isTestDay,
        isTestPast,
        isWaitingForResults,
        isResultsReceived,
        test_attempt_id,
        isPastBufferStart,
      }
      _.extend(payload, booking)

      if(includeInstitInfo){
        const institutionRecords = await this.app
        .service('db/read/institutions')
        .db()
        .where('group_id', <number> testSession.instit_group_id)
        .limit(1)
        const institutionRecord = <IInstitution> institutionRecords[0];
        _.extend(payload, {
          institution: institutionRecord
        })
      }

      const attempt = completedAttempts[0];
      if (attempt){
        payload.attemptKey = attempt.attempt_key;
      }

    }
    else if (activeBooking.waitlist){
      const testSession = await this.getTestSessionByGroupId(<number> activeBooking.waitlist.group_id);
      _.extend(payload,{
        testSession,
        isWaitlist: true,
      })
      if(includeInstitInfo){
        const institutionRecords = await this.app
        .service('db/read/institutions')
        .db()
        .where('group_id', <number> testSession.instit_group_id)
        .limit(1)
        const institutionRecord = <IInstitution> institutionRecords[0];
        _.extend(payload, {
          institution: institutionRecord
        })
      }
    }

    // user pending
    else if (activeBooking.pending) {
      const testSessionGroupId = <{group_id: number}[]> await this.app
        .service('db/read/user-roles')
        .db()
        .where('uid', uid)
        .where('is_revoked', 0)
        .where('role_type', 'mpt_pending_booking_applicant')
        .orderBy('created_on', 'desc')
        .limit(1);

      const testSession = await this.getTestSessionByGroupId(<number> testSessionGroupId[0].group_id);
      _.extend(payload,{
        testSession,
        isPending: true,
      })
      if(includeInstitInfo){
        const institutionRecords = await this.app
        .service('db/read/institutions')
        .db()
        .where('group_id', <number> testSession.instit_group_id)
        .limit(1)
        const institutionRecord = <IInstitution> institutionRecords[0];
        _.extend(payload, {
          institution: institutionRecord
        })
      }
    }
    else if(!accom){
      payload = {
        isNoBookings: true,
      };
    }

    // return payload
    return [
      <any> payload
    ];
  }
  
  public async getTestWindow(testSessionId: number, testSessionOnly: boolean = false){
    const testSessionRecord = await this.app
      .service('db/read/test-sessions')
      .get(testSessionId);
    if(testSessionOnly) return testSessionRecord;
    const testWindow = await this.app
      .service('db/read/test-windows')
      .get(testSessionRecord.test_window_id)
    return testWindow
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  private async getTestSessionByGroupId(group_id: number) {
    const ts = <ITestSessionAvailTT[]> await this.app
      .service('db/read/test-session-avail-tt')
      .db()
      .where('test_session_group_id', group_id);
    if (ts.length === 0 ){
      throw new Errors.Forbidden('TEST_SESSION_NOT_AVAIL');
    }
    return ts[0]
  }

  public async getUpcomingTestWindowIds(){
    const testWindowRecords = <ITestWindow[]> await this.app
      .service('db/read/test-windows')
      .db()
      .where('is_active', 1)
      .where('date_end',   '>', dbDateNow(this.app))
      .where('test_ctrl_group_id', MPT_TEST_CTRL_GROUP_ID)
      .limit(DB_MAX_TEST_WINDOWS_ACTIVE_OVERLAP);
    const test_window_ids = testWindowRecords.map(record => <number> record.id);
    if (test_window_ids.length === 0){
      throw new Errors.GeneralError('NO_UPCOMING_TEST_WINDOWS');
    }
    return test_window_ids;
  }

  public async getActiveTestWindowIds(){
    const testWindowRecords = <ITestWindow[]> await this.app
      .service('db/read/test-windows')
      .db()
      .where('is_active', 1)
      .where('date_start', '<', dbDateNow(this.app))
      .where('date_end',   '>', dbDateNow(this.app))
      .where('test_ctrl_group_id', MPT_TEST_CTRL_GROUP_ID)
      .limit(DB_MAX_TEST_WINDOWS_ACTIVE_OVERLAP);
    const test_window_ids = testWindowRecords.map(record => <number> record.id);
    if (test_window_ids.length === 0){
      throw new Errors.GeneralError('NO_ACTIVE_TEST_WINDOW');
    }
    return test_window_ids;
  }

  public async getActiveBooking(uid:number, test_window_ids:number[], creditsEnabled: any, isSmcs: boolean = false):Promise<IBookingRecord>{


    const pullRecords = async (query:Knex, errorCode:string) => {
      const records = <ITestSessionBookingUser[]> await query
        .where('uid', uid)
        .whereIn('test_window_id', test_window_ids)
        .orderBy('date_time_start')
      // .limit(2)
      if (records.length > 1){
          const reportRecord = <Paginated<any>> await this.app
          .service('db/read/test-reports')
          .find({query: {uid}})
        if(reportRecord.data.length > 0){
          return records[records.length -1];
        }
      }
      return records[0];
    }

    const pullAllRecords = async (query:Knex, errorCode:string)=> {
      const records = <ITestSessionBookingUser[]> await query
        .where('uid', uid)
        .whereIn('test_window_id', test_window_ids)
        .orderBy('date_time_start')
      return records;
    }
    
    const pullWaitlistRecords = async (query:Knex, errorCode:string) => {
      const records = <ITestSessionBookingUser[]> await query
        .where('uid', uid)
        .whereIn('test_window_id', test_window_ids)
        .orderBy('date_time_start')
      // .limit(2)
      if (records.length > 0){
          const reportRecord = <Paginated<any>> await this.app
          .service('db/read/test-reports')
          .find({query: {uid}})
        if(reportRecord.data.length > 0){
          return records[records.length -1];
        }

      }
      return records[0];
    }

    const booking = await pullRecords(
      this.app.service('db/read/test-session-booking-users').db(),
      'TOO_MANY_SIMUL_BOOKINGS'
    );
    let allBooking;
    if(isSmcs){
      allBooking = await pullAllRecords(
        this.app.service('db/read/test-session-booking-users').db(),
        'TOO_MANY_SIMUL_BOOKINGS',
      );
    }

    const waitlist = await pullWaitlistRecords(
      this.app.service('db/read/test-session-waitlist-users').db(),
      'TOO_MANY_SIMUL_WAITLIST'
    );

    let pending = null;

    if (creditsEnabled) {
      const testSessionGroupId = <{group_id: number}[]> await this.app
      .service('db/read/user-roles')
      .db()
      .where('uid', uid)
      .where('is_revoked', 0)
      .where('role_type', 'mpt_pending_booking_applicant')
      .orderBy('created_on', 'desc')
      .limit(1);

      if (testSessionGroupId.length > 0) {
        pending = await this.app
          .service('db/read/user-roles')
          .db()
          .where('uid', uid)
          .where('is_revoked', 0)
          .where('role_type', 'mpt_pending_booking_applicant')
          .where('group_id', testSessionGroupId[0].group_id)
          .orderBy('created_on', 'desc')
          .limit(1);
      }
    }

    return {
      booking,
      smcsBooking: allBooking,
      waitlist,
      pending
    }

  }

  async getTestSessionAccessCode(testSessionId:number){
    const ts = <ITestSession> await this.app.service('db/read/test-sessions').get(testSessionId);
    return <string> ts.access_code;
  }

  async allocateCredit (uid: number, testSessionId: number, validatedCap?: any): Promise<void> {
    const currentTime = new Date();

    let currCredit = <ICreditInfo> await this.app
      .service('credits/credits')
      .getUserCurrentHoldingCredit(uid);

    let createdCredit;

    const paymentsEnabled = await this.app
      .service('public/credits/payment-system')
      .get(6);

    if (!paymentsEnabled) {
      // not holding credit but has valid mpt status --> give test applicant a free credit
      if (!currCredit && validatedCap.freeCreditEligible) {
        const currentDate = new Date();
        const freeCreditReason = 'mpt status eligible'

        createdCredit = await this.app
          .service('credits/credits')
          .createCredit(uid, 1, freeCreditReason, currentDate, validatedCap.mptStatus);

        await this.app
          .service('credits/credit-transactions')
          .createAcquiredTransaction(createdCredit.id, currentDate, uid, freeCreditReason);
      }
      // not holding credit or not free credit eligible --> throw error
      else if (!validatedCap.freeCreditEligible) throw new Errors.BadRequest('MPT_STATUS_NOT_ELIGIBLE');
      else if (!currCredit && !validatedCap.freeCreditEligible) throw new Errors.BadRequest('USER_NOT_HOLDING_CREDIT');

    } else {
      // not holding credit --> throw error
      if (validatedCap.freeCreditEligible === null) throw new Errors.BadRequest('MPT_STATUS_NOT_ELIGIBLE');
      else if (!currCredit && validatedCap.freeCreditEligible === null) throw new Errors.BadRequest('USER_NOT_HOLDING_CREDIT');
    }

    let creditData: IPatchCredit = {
      uid: uid,
      creditId: createdCredit ? createdCredit.id : currCredit.credit_id  // use newly acquired credit if it was made
    }

    // update credits table
    await this.app
      .service('credits/credits')
      .allocateCredit(creditData.creditId, creditData.uid)
      .catch(e => {
        throw new Errors.NotFound(e);
      })

    await this.app
      .service('credits/credit-transactions')
      .createAllocatedTransaction(createdCredit ? createdCredit.id : currCredit.credit_id, currentTime, uid, testSessionId);
  }

  // booking / registration
  async create (data: ICreateBooking, params?: Params): Promise<Data> {

    const {
      test_session_id,
      access_code,
      lang
    } = data;

    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    const uid = <number> userInfo.uid;

    // verify available booking slots in session (using summary view)
    const ts:ITestSessionAvailTT = await this.getValidateTestSession(test_session_id, access_code, false);
    const test_window_id = ts.test_window_id;
    const group_id = <number> ts.test_session_group_id

    await this.validateDoubleBooking(uid, test_session_id,  test_window_id); 

    const creditsEnabled:any = await this.app
      .service('public/credits/credit-system')
      .find();

    const paymentsEnabled = await this.app
    .service('public/credits/payment-system')
    .get(6);

    // check if they are already booked into another session (for now, don't care if the test was completed, but filter to test window)
    const activeBookingRecords = <IBookingRecord> await this.getActiveBooking(uid, [test_window_id], creditsEnabled.isEnabled);
    // if (activeBookingRecords.booking){ 
    //   if(activeBookingRecords.booking.is_closed) {
    //     throw new Errors.Forbidden('TEST_COMPLETED');
    //   } else {
    //     throw new Errors.Forbidden('EXISTING_BOOKING')
    //   }
    // }

    if (activeBookingRecords.waitlist){
      await this.app
        .service('public/test-taker/test-sessions/waitlist')
        .cancelWaitlist(<number> activeBookingRecords.waitlist.test_session_id, uid);
    }

    let validatedCap: string | ICapObject;

    const isBypassOctValidator = await this.app.service('public/test-taker/test-sessions/oct-validation').isBypassOctValidator();
    if (creditsEnabled.isEnabled && !isBypassOctValidator) {
      // oct API validation check
      const octRecord = await this.app
        .service('db/read/oct-applicant-ids')
        .db()
        .where({
          uid: uid
        })
        .limit(1);

      const user = await this.app
        .service('db/read/users')
        .db()
        .where('id', uid)
        .limit(1);

      const lastName = <string> user[0].last_name;

      validatedCap = await this.app
        .service('auth/oct-cap')
        .validateOctCap(octRecord[0].oct_id, lastName, uid)
        .catch(e => {
          throw new Errors.NotFound('INVALID_OCT_ID');
      });
    } else {
      validatedCap = {
        capNum: -1,
        statusCode: -1,
        capString: '',
        freeCreditEligible: false,
        mptStatus: -1
      };
    }

    // COMMITTED

    if ( creditsEnabled.isEnabled || paymentsEnabled) await this.allocateCredit(uid, test_session_id, validatedCap); // creditsEnabled.isEnabled  

    // create new role
    const newRole =  await this.app
      .service('auth/user-role-actions')
      .assignUserRoleToGroup({
        uid,
        role_type : DBD_U_ROLE_TYPES.mpt_booked_applicant,
        group_id,
        created_by_uid : uid,
      })

    // run the check here:
    // 1. pull non-revoked accounts
    const bookingRecords = <ITestSessionBookingUser[]> await this.app
    .service('db/write/user-roles')
    .db()
    .where('group_id', ts.test_session_group_id)
    .where('is_revoked', 0)
    .where('role_type', DBD_U_ROLE_TYPES.mpt_booked_applicant)
    .orderBy('created_on', 'asc')

    // 2. check if list exceeds capacity (revoke the extras)
    let disallowed_bookings:ITestSessionBookingUser[] = [];

    // 3. make sure that the current user's account is within the array from (1), in an entry below the capacity ( for example, userRoles.slice(0, capacity).indexOf(uid)   )
    if (bookingRecords && bookingRecords.length > ts.capacity){
      disallowed_bookings = bookingRecords.splice(ts.capacity, bookingRecords.length);
    }

    const userAppearanceMap = new Map();
    bookingRecords.forEach((booking, i) => {
      if (userAppearanceMap.has(booking.uid)){
        disallowed_bookings.push(bookingRecords.splice(i)[0])
      }
      userAppearanceMap.set(booking.uid, true);
    })

    let allowed = true;
    await Promise.all(
      disallowed_bookings.map(async (user_booking) => {
        if(user_booking.uid === uid){
          allowed = false;
        }
        await this.app
        .service('db/write/user-roles')
        .db()
        .where('id', user_booking.id)
        .update({
          is_revoked: 1,
          revoked_on: dbDateNow(this.app)
        })
      })
    )

    if(!allowed){
      throw new Errors.Forbidden('TEST_SESSION_FULL');
    }

    //Ensure attempt is created
    // this.app.service('public/test-taker/invigilation/test-attempt').getCurrentAttempt(uid,test_session_id,{
    //   isCreateNewIfEmpty: true,
    //   isTestTaker: true,
    //   isPresent: false,
    //   setBookingLang: lang
    // });

    //sending email
    const institutionRecords = await this.app
        .service('db/read/institutions')
        .db()
        .where('group_id', <number> ts.instit_group_id)
        .limit(1)
    const institutionRecord = <IInstitution> institutionRecords[0];

    const langCode = lang;
    const subject =  await this.app.service('public/translation').getOneBySlug('subj_email_applicant_notif', langCode);
    const emailAddress = userInfo.email?userInfo.email:'';
    const SESSION_DATE = renderDateTime(ts.date_time_start, undefined, lang);
    const TEST_CENTRE = institutionRecord.name;
    let emailTemplate;
    let BUILDING;
    let ROOM;
    if(ts.room === 'REMOTE'){
      emailTemplate = await this.app.service('public/translation').getOneBySlug('email_applicant_booked_session_remote', langCode);
      BUILDING = await this.app.service('public/translation').getOneBySlug('tra_remote', langCode);
    }else{
      emailTemplate = await this.app.service('public/translation').getOneBySlug('email_applicant_booked_session_person', langCode);
      BUILDING = ts.campus_building;
      ROOM = ts.room
    }

    this.app
      .service('mail/core')
      .sendEmail({
        subject: subject,
        emailTemplate: emailTemplate,
        emailAddress: emailAddress,
        parameterMapping: {
          SESSION_DATE,
          TEST_CENTRE,
          BUILDING,
          ROOM
        },
        whitelabel    : params && params.headers ? params.headers.host : undefined
      });

    // return payload
    const booking:Partial<ITestSessionBookingUser> = {
      uid,
      group_id,
      test_session_id,
      created_on: newRole.created_on,
      test_window_id
    };
    return {
      booking
    };
  }

  translateRemoteRoom(room: string, langCode: string) {
    if (langCode === 'fr') {
      return
    }
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async validateDoubleBooking(uid:number, booking_test_session_id: number, test_window_id:number){

    // get booking limits
    const bookingLimit = await getSysConstNumeric(this.app, "APPLICANT_SESSION_BOOKING_LIMIT", true);
    const bookedRecords = await dbRawRead(this.app, [uid, uid], `
      SELECT tsbu.role_type
          , tsbu.is_closed
          , tsbu.group_id
          , tsbu.test_session_id
          , tsbu.test_window_id
          , trr.attempt_key
          , trr.is_successful
          , trr.is_results_released
          , trr.id as report_id
      FROM mpt_dev.test_session_booking_users tsbu
      left join (
        SELECT tr.attempt_key
            , tr.is_successful
            , tr.uid
            , tr.id
            , ta.is_results_released
            , ta.test_session_id
        FROM mpt_dev.test_reports tr
        join mpt_dev.test_attempts ta on ta.id = tr.id
        where tr.uid = ?
      ) trr
        on tsbu.test_session_id = trr.test_session_id
      where tsbu.uid = ?
    ;`);
    if(bookedRecords.length > 0){ 

      if(bookedRecords.length >= bookingLimit)  throw new Errors.Forbidden('BOOKING_EXCEEDS_TOTAL_ALLOWED_BOOKING_LIMITS');

      bookedRecords.forEach(record =>{
        if(booking_test_session_id === record.test_session_id){
          //  if(record.test_window_id === test_window_id){
          //   throw new Errors.Forbidden('EXISTING_BOOKING') 
          //  }
          if(record.is_results_released != 1){ // if the results have not been released, do not allow to rebook
            throw new Errors.Forbidden('EXISTING_BOOKING') 
          }
          if(record.is_successful =="1"){ // if they have passed before
            throw new Errors.Forbidden('TEST_COMPLETED') 
          }
        }
      })
    }
    // also check for active waitlists
    const waitlistRecords = await dbRawRead(this.app, [uid, booking_test_session_id], `
      SELECT * FROM mpt_dev.test_session_waitlist_users where uid = ? and test_session_id = ?
    ;`);
    if(waitlistRecords.length > 0){
      throw new Errors.Forbidden('EXISTING_BOOKING')
    }
  }

  async getValidateTestSession(test_session_id:number, access_code?:string, isWaitlistReq?:boolean){
    const ts = <ITestSessionAvailTT> await this.app
      .service('db/read/test-session-avail-tt')
      .get(test_session_id);
    if (!ts){
      throw new Errors.Forbidden('TEST_SESSION_NOT_AVAIL');
    }

    if (!isWaitlistReq && (ts.booked && ts.booked >= ts.capacity)){
      throw new Errors.Forbidden('TEST_SESSION_FULL');
    }

    let bookingBuffer = await this.getBufferDays(ts);
    if (isDatePast(ts.date_time_start, bookingBuffer)){
      throw new Errors.Forbidden('TEST_SESSION_BUFFER_CLOSED');
    }

    if (ts.is_access_code_enabled){
      const testSessionAccessCode = await this.getTestSessionAccessCode(ts.id);
      if (testSessionAccessCode !== (access_code || '').trim() ){
        throw new Errors.Forbidden('INVALID_ACCESS_CODE')
      }
    }

    return ts
  }

  public async convertWaitlistersToPending(created_by_uid: number, test_session_id: number, params: any) {
    const ts = <ITestSessionAvailTT> await this.app
    .service('db/write/test-session-avail-tt')
    .get(test_session_id); // need the latest version, so reading from `db/write`
    if (!ts){
      throw new Errors.Forbidden('TEST_SESSION_NOT_AVAIL');
    }
    const group_id = ts.test_session_group_id;
    const numAvailableSlots = ts.capacity - (ts.booked || 0);
    const usersToUpgrade = <{id: number, uid:number}[]> await this.app
      .service('db/read/test-session-waitlist-users')
      .db()
      .where('group_id', ts.test_session_group_id)
      .orderBy('created_on', 'asc')
      .limit(numAvailableSlots);

    // upgrade
    await Promise.all(
      usersToUpgrade.map(async userRoleRec => {
        // remove/revoke old waitlist status
        // await this.app
        //   .service('db/write/user-roles')
        //   .patch(userRoleRec.id, {
        //     is_revoked: 1,
        //     revoked_on: dbDateNow(this.app)
        // })
        return await this.app
          .service('public/test-taker/test-sessions/pending')
          .upgradeNextPersonOnWaitlist(test_session_id, group_id, created_by_uid, userRoleRec.uid, params)
      })
    )
  }

  async fillNewBookingSlots(test_session_id:number, created_by_uid:number, params?: Params){
    const ts = <ITestSessionAvailTT> await this.app
      .service('db/write/test-session-avail-tt')
      .get(test_session_id); // need the latest version, so reading from `db/write`
    if (!ts){
      throw new Errors.Forbidden('TEST_SESSION_NOT_AVAIL');
    }
    const group_id = ts.test_session_group_id;
    const numAvailableSlots = ts.capacity - (ts.booked || 0);
    const usersToUpgrade = <{id: number, uid:number}[]> await this.app
      .service('db/read/test-session-waitlist-users')
      .db()
      .where('group_id', ts.test_session_group_id)
      .orderBy('created_on', 'asc')
      .limit(numAvailableSlots);

    // upgrade
    await Promise.all(
      usersToUpgrade.map(async userRoleRec => {
        const uid = userRoleRec.uid;
        await this.app
          .service('auth/user-role-actions')
          .assignUserRoleToGroup({
            uid,
            role_type : DBD_U_ROLE_TYPES.mpt_booked_applicant,
            group_id,
            created_by_uid,
          })

        const bookedApplicants = <Paginated<any>> await this.app
        .service('db/read/test-session-waitlist-users-info')
        .find({query: {uid: userRoleRec.uid, test_session_id: test_session_id}})

        let lang='en';
        const attempts = <ITestAttempt[]>await this.app
          .service('db/read/test-attempts')
          .find({ query: { uid, test_session_id, $limit: 1, is_invalid: {$ne: 1} }, paginate: false })
          console.log(attempts)
        if (attempts.length > 0) {
          const attempt = attempts[0];
          lang = attempt.booking_lang || lang;
        }

        const bookedApplicant = bookedApplicants.data[0];

        const applicant_email              = bookedApplicant.contact_email;
        const userFirstName                = bookedApplicant.first_name;
        const testSessionId                = ts.id;
        const testSessionCampusBuilding    = ts.campus_building;
        const testSessionRoom              = ts.room;
        const testSessionStartTime         = renderDateTime(ts.date_time_start);
        const langCode                     = lang; //NEED TO SET THIS
        const subject =  await this.app.service('public/translation').getOneBySlug('subject_email_booking_success', langCode);
        const emailTemplate = await this.app.service('public/translation').getOneBySlug('email_booking_success', langCode);
        // const emailTemplate = `Hello {{userFirstName}},

        // You have been added to the following test session and removed from the waitlist:

        // Campus Building:{{testSessionCampusBuilding}}
        // Room:{{testSessionRoom}}
        // Start Time:{{testSessionStartTime}}
        // Session ID:{{testSessionId}}

        // This is an automated email message. Please do not reply.`//await this.app.service('public/translation').getOneBySlug('email_waitlist_upgrade', langCode);

        return  this.app
          .service('mail/core')
          .sendEmail({
            subject: subject,
            emailTemplate: emailTemplate,
            emailAddress: applicant_email,
            parameterMapping: {
              userFirstName,
              testSessionId,
              testSessionCampusBuilding,
              testSessionRoom,
              testSessionStartTime,
            },
            whitelabel    : params && params.headers ? params.headers.host : undefined
          });
      })
    )



    // remove/revoke old waitlist status
    await Promise.all(
      usersToUpgrade.map(userRoleRec => {
        return this.app
          .service('db/write/user-roles')
          .patch(userRoleRec.id, {
            is_revoked: 1,
            revoked_on: dbDateNow(this.app)
          })
      })
    )
  }

  async refundTokenAfterCancelling(uid: number): Promise<void> {
    const recentCreditTransaction = await this.app
      .service('credits/credit-transactions')
      .getUserMostRecentCreditTransaction(uid)
      .catch(e => {
        throw new Errors.NotFound(e);
      });

    const creditId = <number> recentCreditTransaction.credit_id;

    await this.app
      .service('credits/credits')
      .deallocateCredit(creditId, uid);

    const currentTime = new Date();

    await this.app
      .service('credits/credit-transactions')
      .createRefundedTransaction(creditId, currentTime, uid);
  }

  async completePartialRefund(uid: number): Promise<void> {
    // get most recent purchase
    const recentPurchaseTransaction = await this.app
      .service('credits/credit-transactions')
      .getUserMostRecentCreditPurchaseTransaction(uid)
      .catch(e => {
        throw new Errors.BadRequest('USER_NOT_PURCHASE_RECENT_CREDIT');
      });

    const stripeId = await this.app
      .service('db/read/transactions')
      .db()
      .where('id', recentPurchaseTransaction.transaction_id);

    await this.app
      .service('public/transactions/stripe')
      .completePartialRefund(uid, stripeId[0].stripe);

    const newTransaction: ITransaction = await this.app
    .service('public/transactions/transactions')
    .create({stripeId: stripeId});

    // hardcoded refund value for now
    // this.refundTokenAfterCancelling(uid, newTransaction.id, 10);
  }

  async revokeCreditAfterCancellingWithinBuffer(uid: number, testSessionId: number): Promise<void> {
    const currCredit = <ICreditTransaction> await this.app
      .service('credits/credit-transactions')
      .getUserMostRecentCreditTransaction(uid)

    if (!currCredit) throw new Errors.BadRequest('USER_NOT_HOLDING_CREDIT');

    await this.app
      .service('credits/credits')
      .revokeCredit(currCredit.credit_id, uid);

    await this.app
      .service('credits/credit-transactions')
      .createConsumedTransaction(currCredit.credit_id, new Date(), uid, testSessionId)
  }

  // cancellations
  async remove (id: NullableId, params?: Params): Promise<Data> {
    const test_session_id = <number> id;

    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    const uid = <number> userInfo.uid;

    const ts = <ITestSessionAvailTT> await this.app
      .service('db/read/test-session-avail-tt')
      .get(test_session_id);
    if (!ts){
      throw new Errors.Forbidden('TEST_SESSION_NOT_AVAIL');
    }

    const paymentsEnabled = await this.app
      .service('public/credits/payment-system')
      .get(6);
    
    const creditsEnabled:any = await this.app
      .service('public/credits/credit-system')
      .find();

    // can cancel within buffer
    let bookingBuffer = await this.getBufferDays(ts);

    // COMMITTED
    if (!paymentsEnabled) {
      if (isDatePast(ts.date_time_start, bookingBuffer) ){
        let bookingBuffer;
        let bookingBufferUnit = 'txt_days';
        if(ts.custom_booking_buffer_d === 0 ||
          (ts.custom_booking_buffer_d !== null && ts.custom_booking_buffer_d !== undefined)) {
            bookingBuffer = ts.custom_booking_buffer_d;
            bookingBufferUnit = 'txt_days'
          } else {
            bookingBuffer = ts.reg_buffer_policy;
            bookingBufferUnit = ts.reg_buffer_policy_unit
          }
        throw new Errors.Forbidden('TEST_SESSION_BUFFER_CLOSED', {
          bookingBuffer,
          bookingBufferUnit
        });
      }
    }

    await this.cancelBooking(uid, uid,  ts.test_session_group_id, test_session_id, false, ts.date_time_start, bookingBuffer, params);
    
    if (creditsEnabled.isEnabled) {
    // refund token only if the cancellation is before the buffer period
      if (!isDatePast(ts.date_time_start, bookingBuffer)) {
        await this.refundTokenAfterCancelling(uid)
          .catch(e => {
            throw new Errors.BadRequest(e);
          });
      }
      else {
        await this.revokeCreditAfterCancellingWithinBuffer(uid, test_session_id);
        if (paymentsEnabled) {

          // mark as no show by setting test attempt is_absent to 1
          await this.app
            .service('public/test-taker/invigilation/test-attempt')
            .markAttemptAsAbsent(uid, test_session_id);

          const transactionPurchase = await this.app
            .service('credits/credit-transactions')
            .getUserMostRecentCreditPurchaseTransaction(uid);

          const transactionToFind = await this.app
            .service('db/read/transactions')
            .db()
            .where('id', transactionPurchase.transaction_id);

          await this.app
            .service('public/transactions/stripe')
            .completePartialRefund(uid, transactionToFind[0].stripe_id);

          const dataToPass = {stripeId: transactionToFind[0].stripe_id};

          // track refund in our system
          const transactionRecord = await this.app
            .service('public/transactions/transactions')
            .create(dataToPass);
        }
      }
    }

    return {}
  }

  async cancelBooking(uid: number, revoked_by_id: number, test_session_group_id: number, test_session_id: number, isWaitlist: boolean, dateTimeStart: any, bookingBuffer: number = 0, params?: Params) {
    await this.app
      .service('auth/user-role-actions')
      .revokeUserFromGroup(
        uid,
        test_session_group_id,
        revoked_by_id
      );
    if(!isWaitlist) {
      const creditsEnabled:any = await this.app
      .service('public/credits/credit-system')
      .find()
      if (creditsEnabled.isEnabled) {
        // checks if the cancellation occurred within the registration buffer and 24 hours before registration buffer begins
        // if it falls into these cases, do not change waitlist applicants to pending
        if (!isDatePast(dateTimeStart, bookingBuffer) && !isDatePast(dateTimeStart, bookingBuffer + 1)) await this.convertWaitlistersToPending(uid, test_session_id, params);
      }
      else await this.fillNewBookingSlots(test_session_id, uid, params);
    }
    await this.app.service('public/test-taker/accommodations/pending-req').revokeAccommReqForTestSession(uid, test_session_id);
  }

  public async getBufferDays(testSession: ITestSession | ITestSessionAvailTT, institution? :IInstitution) {

    //If explicitly set to 0, return this.
    if(testSession.custom_booking_buffer_d === 0) {
      return testSession.custom_booking_buffer_d;
    }

    const bufferPolicy = await this.app.service('db/read/sys-constants-string')
    .db()
    .where('key', INSTIT_BUFFER_POLICY);

    const bufferPolicyNum = parseInt(bufferPolicy[0].value);

    // if(!institution) {
    //   const institutions = await this.app
    //   .service('db/read/institutions')
    //   .db()
    //   .where('group_id', <number> testSession.instit_group_id)
    //   .limit(1)
    //   institution = <IInstitution> institutions[0];
    // }

    // const bookingBuffer = testSession.custom_booking_buffer_d || institution.reg_buffer_policy;

    // explicitly set the booking buffer to 4 days
    const bookingBuffer = bufferPolicyNum;

    if(bookingBuffer === undefined) {
      throw new Errors.NotFound('Booking buffer undefined');
    }

    return bookingBuffer;
  }
}
function getData(arg0: never[], arg1: string) {
  throw new Error('Function not implemented.');
}
