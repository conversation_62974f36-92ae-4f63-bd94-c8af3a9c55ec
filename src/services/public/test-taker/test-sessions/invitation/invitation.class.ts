import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { IAvailableBookings } from '../all/all.class';
import { ITestSessionAvailTT, ITestSessionAvailTTExt } from '../../../../db/schemas/test-session-avail-tt.schema';
import { IInstitution } from '../../../../db/schemas/institutions.schema';
import { ITestSession } from '../../../../db/schemas/test_sessions.schema';
import { isDatePast, dbDateOffsetDays } from '../../../../../util/db-dates';
import { TEST_SESSION_REG_BUFFER_DAYS } from '../../../../../constants/db-constants';
import { Errors } from '../../../../../errors/general';
import { getSysConstNumeric } from '../../../../../util/sys-const-numeric';
import { MemoGet } from '../../../../../util/memo';

interface Data extends IAvailableBooking {}

export interface IAvailableBooking {
  institution:IInstitution,
  session:ITestSessionAvailTT,
}

interface ServiceOptions {}

let memoPayload = new MemoGet(1000*60*10);
const WAITLIST_FLAG_KEY = 'ENABLE_WAITLIST';

export class Invitation implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.BadRequest()
  }

  async get (id: Id, params?: Params): Promise<Data> {

    let invitation_code:string = '';
    if (params && params.query){
      invitation_code = params.query.invitation_code;
    }

    // validate invitation code
    const sessionInvitations = <ITestSession[]> await this.app
      .service('db/read/test-sessions')
      .db()
      .where('id', id)
      .where('invitation_code', invitation_code)
      .limit(1)
    const sessionInvitation = sessionInvitations[0];

    if (!sessionInvitation){
      throw new Errors.NotFound('invalid pair')
    }

    let bookingBuffer = await this.app.service('public/test-taker/test-sessions/booking').getBufferDays(sessionInvitation);

    if (isDatePast(sessionInvitation.date_time_start, -1*bookingBuffer)){
      throw new Errors.Forbidden('expired')
    }

    // pull session 
    const sessions = <ITestSessionAvailTTExt[]> await this.app
      .service('db/read/test-session-avail-tt')
      .db()
      .where('id', id)
      .where('date_time_start', '>', dbDateOffsetDays(this.app, -1*bookingBuffer))
      .limit(1)
    const session = sessions[0];
    if (!session){
      throw new Errors.Forbidden('not avail')
    }

    const creditsEnabled:any = await this.app
      .service('public/credits/credit-system')
      .find();

    if (creditsEnabled.isEnabled) {
      const numPendings = await this.app
      .service('public/test-taker/test-sessions/pending')
      .getNumPendingsForSession(session.id);

      session.pending = numPendings;
    }
    
    // and associated instit
    const institutions = <IInstitution[]> await this.app
      .service('db/read/institutions')
      .db()
      .where('group_id', session.instit_group_id)
      .where('is_active', 1)
      .where('is_shown', 1)
      .limit(1)
    
    const paymentsEnabled = await this.app
      .service('public/credits/payment-system')
      .get(6);
    
    let isWaitlistEnabled = true;

    if (creditsEnabled && paymentsEnabled) isWaitlistEnabled = await getSysConstNumeric(this.app, WAITLIST_FLAG_KEY);

    // payload
    return memoPayload.cachePayload({
      institution: institutions[0],
      session,
      isWaitlistEnabled
    });

  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.BadRequest()
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.BadRequest()
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.BadRequest()
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Error()
  }
}
