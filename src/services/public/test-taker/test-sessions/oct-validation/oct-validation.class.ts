import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { dbRawRead } from '../../../../../util/db-raw';
import { Errors } from '../../../../../errors/general';
import { getSysConstNumeric } from '../../../../../util/sys-const-numeric';
//import { MPT_TEST_CTRL_GROUP_ID } from '../../../test-admin/test-session-setup/test-windows/test-windows.class';

interface Data { }

interface ServiceOptions {}

const FLAG_KEY = 'BYPASS_OCT_VALIDATOR'

export class OctValidation implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  private async getOctNumber(uid: number) {
    return await this.app
      .service('db/read/oct-applicant-ids')
      .db()
      .where('uid', uid)
      .limit(1);
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {

    // endpoint for OCT validation with changing of last name cases
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS_REQ');
    }
    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    const uid = <number> userInfo.uid;
    let newLastName;
    if (params) {
      newLastName = params.query ? params.query.lastName : '';
    }

    let octRecord = await this.getOctNumber(uid);
    let octNumber;
    
    if (octRecord.length > 0) octNumber = octRecord[0].oct_id;
    else throw new Errors.NotFound('NO_OCT_ID_FOR_LAST_NAME');

    const validated = await this.app
      .service('auth/oct-cap')
      .validateOctCap(octNumber, newLastName, uid)
      .catch(e => {
        throw new Errors.BadRequest(e);
      })
    
    if (validated) {
      return this.app
        .service('db/write/users')
        .db()
        .where('id', uid)
        .limit(1)
        .update({
          last_name: newLastName
        })
    }
    throw new Errors.BadRequest('INVALID_OCT');
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async isBypassOctValidator() {
    let isOpen;
    isOpen = await getSysConstNumeric(this.app, FLAG_KEY).catch((e) => {
      isOpen = false;
    });
    return isOpen;
  }
}
