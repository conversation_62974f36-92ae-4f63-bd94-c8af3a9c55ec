import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { ITestSessionBookingUser } from '../../../../db/schemas/test_session_booking_users.schema';
import { DBD_U_ROLE_TYPES } from '../../../../../constants/db-extracts';
import { IBookingRecord, ICreateBooking } from '../booking/booking.class';
import { Errors } from '../../../../../errors/general';
import { ITestSessionAvailTT } from '../../../../db/schemas/test-session-avail-tt.schema';
import { TEST_SESSION_REG_BUFFER_DAYS } from '../../../../../constants/db-constants';
import { dbDateNow, isDatePast } from '../../../../../util/db-dates';
import { IInstitution } from '../../../../db/schemas/institutions.schema';
import { dbRawRead } from '../../../../../util/db-raw';
import { ITestSession } from '../../../../db/schemas/test_sessions.schema';
import { IUserRole } from '../../../../db/schemas/user-roles.schema';
import { ITestAttempt } from '../../../../db/schemas/test_attempts.schema';
import { normalizeDomain } from '../../../../../util/domain-whitelist';
import { renderDateTime } from '../../../../../hooks/_util';

interface Data {}

const MAX_WAITLIST_COUNT = 4;

interface ServiceOptions {}

export interface IPendingDataCurrentReq {
  testSessionId: number,
  groupId: number,
  applicantUid: number
}

export class Pending implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;
  pendingDataCurrentReq: IPendingDataCurrentReq;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
    this.pendingDataCurrentReq = {testSessionId: -1, groupId: -1, applicantUid: -1}
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create (data: ICreateBooking, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async getNumPendingsForSession (testSessionId: number): Promise<number> {
    let sessionPendings = await dbRawRead(this.app, [testSessionId], `
      SELECT ts.id as test_session_id, ur.group_id, ur.pending
      FROM test_sessions as ts, 
        (SELECT group_id, count(*) as 'pending' from user_roles
        WHERE role_type = 'mpt_pending_booking_applicant' and is_revoked = 0
        group by group_id) as ur
      WHERE ts.test_session_group_id = ur.group_id
        AND ts.id = ?
      ;`)
    let numPendings = sessionPendings[0] ? sessionPendings[0].pending : 0;
    return numPendings;
  }

  async upgradeNextPersonOnWaitlist (testSessionId: number, groupId: number, createdByUid: number, applicantUid: number, params: any) {
    if (this.pendingDataCurrentReq.testSessionId !== testSessionId && this.pendingDataCurrentReq.groupId !== groupId && this.pendingDataCurrentReq.applicantUid !== applicantUid) {
      this.pendingDataCurrentReq.testSessionId = testSessionId;
      this.pendingDataCurrentReq.groupId = groupId;
      this.pendingDataCurrentReq.applicantUid = applicantUid;
      const ts = await this.app
      .service('db/read/test-sessions')
      .db()
      .where('id', testSessionId)
      .where('test_session_group_id', groupId);
    
      // upgrade user to pending booking applicant
      await this.app
        .service('auth/user-role-actions')
        .assignUserRoleToGroup({
          uid: applicantUid,
          role_type: DBD_U_ROLE_TYPES.mpt_pending_booking_applicant,
          group_id: groupId,
          created_by_uid: createdByUid
        })
      

      const bookedApplicants = <Paginated<any>> await this.app
      .service('db/read/test-session-waitlist-users-info')
      .find({query: {uid: applicantUid, test_session_id: testSessionId}})
    
      // revoke waitlist role
      await this.app
        .service('auth/user-role-actions')
        .revokeUserRoleFromGroup(
          applicantUid,
          groupId,
          DBD_U_ROLE_TYPES.mpt_waiting_list_applicant,
          createdByUid
        )
      
      let lang='en';
      const attempts = <ITestAttempt[]>await this.app
        .service('db/read/test-attempts')
        .find({ query: { uid: applicantUid, test_session_id: testSessionId, $limit: 1 }, paginate: false })
      if (attempts.length > 0) {  
        const attempt = attempts[0];
        lang=attempt.booking_lang
      }

      const bookedApplicant = bookedApplicants.data[0];
      let domain = params.query.domain ? normalizeDomain(params.query.domain) : normalizeDomain(params.headers.referer);

      const applicant_email              = bookedApplicant.contact_email;
      const userFirstName                = bookedApplicant.first_name;
      const testSessionCampusBuilding    = ts[0].campus_building;
      const testSessionRoom              = ts[0].room;
      const testSessionStartTime         = renderDateTime(ts[0].date_time_start);
      const langCode                     = lang; //NEED TO SET THIS
      const link                         = `${domain}#/${lang}/test-taker/dashboard`
      const subject =  await this.app.service('public/translation').getOneBySlug('subject_email_booking_success', langCode);
      const emailTemplate = await this.app.service('public/translation').getOneBySlug('email_waitlist_to_pending', langCode);
      // const emailTemplate = `Hello {{userFirstName}},

      // A spot in the follow test session is now available.
      // If you would like to confirm the acceptance of this spot, use the following [link]({{link}})
      // You have 24 hours to confirm.

      // Campus Building:{{testSessionCampusBuilding}} 
      // Room:{{testSessionRoom}}            
      // Start Time:{{testSessionStartTime}}       
      // Session ID:{{testSessionId}}              

      // This is an automated email message. Please do not reply.`//await this.app.service('public/translation').getOneBySlug('email_waitlist_upgrade', langCode);

      return  this.app
        .service('mail/core')
        .sendEmail({
          subject: subject,
          emailTemplate: emailTemplate,
          emailAddress: applicant_email,
          parameterMapping: {
            userFirstName,
            testSessionId,
            testSessionCampusBuilding,
            testSessionRoom,
            testSessionStartTime,
            link
          },
          whitelabel    : params && params.headers ? params.headers.host : undefined
        });
    }
  }

  async cancelPendingBooking(test_session_id:number, uid:number, params: any){
    const testSessionRecord = await this.app
      .service('db/read/test-sessions')
      .db()
      .where({id: test_session_id})
      .limit(1);
        
    await this.app
      .service('db/write/user-roles')
      .db()
      .where('role_type', DBD_U_ROLE_TYPES.mpt_pending_booking_applicant)
      .where('uid', uid)
      .where('group_id', testSessionRecord[0].test_session_group_id)
      .update({
        is_revoked: 1,
        revoked_on: dbDateNow(this.app),
        revoked_by_uid: uid
      })
    
    const nextApplicant = await this.app
      .service('public/test-taker/test-sessions/waitlist')
      .getApplicantFrontOfWaitingList(testSessionRecord[0].test_session_group_id)
    
    if (nextApplicant.length > 0) await this.upgradeNextPersonOnWaitlist(test_session_id, testSessionRecord[0].test_session_group_id, uid, nextApplicant[0].uid, params);
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    const test_session_id = <number> id;
    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    const uid = <number> userInfo.uid;

    const creditsEnabled: any = await this.app
      .service('public/credits/credit-system')
      .find()
    if (creditsEnabled.isEnabled) {
      this.cancelPendingBooking(test_session_id, uid, params);
    } else throw new Errors.Forbidden('CREDITS_SYSTEM_NOT_ENABLED');
    return {}
  }
}
