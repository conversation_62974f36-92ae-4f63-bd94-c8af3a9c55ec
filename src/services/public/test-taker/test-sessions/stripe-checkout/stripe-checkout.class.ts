import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import StripeAP<PERSON> from 'stripe';
import { getSysConstString } from '../../../../../util/sys-const-string';

interface Data {}

interface ServiceOptions {}


export class StripeCheckout implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.NotImplemented();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  private convertPriceForStripe(amount: number): number | undefined {
    return amount * 100;
  }

  public async createStripeSessionForAppeals(type: string, statusCode: number, lang: string, attemptId: number, params?: Params) {
    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    const uid = <number> userInfo.uid;

    const statusCodeRecord = await this.app.service('db/read/mapping-mpt-status-codes')
      .db()
      .where('mpt_status_code', statusCode)
      .limit(1);

    let amount = await getSysConstString(this.app, 'MPT_SESSION_BOOKING_FEE');

    if (statusCodeRecord[0]?.amount) amount = <number>statusCodeRecord[0].amount;

    const domain = params && params.headers ? params.headers.referer : '';
    const successUrl = `${domain}#/${lang}/test-taker/file-appeal/attempt/${attemptId}`;
    const cancelUrl = `${domain}#/${lang}/test-taker/dashboard`;

    const stripeAPI = this.app.get('stripe_api');

    const stripe = new StripeAPI(stripeAPI.secretKey, {
      apiVersion: stripeAPI.apiVersion,
    })
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'cad',
            product_data: {
              name: 'MPT Appeals',
            },
            unit_amount: this.convertPriceForStripe(amount),
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: successUrl, // need to change
      cancel_url: cancelUrl, // need to change
      metadata: {uid: uid, type: type}
    });

    return { url: session.url, paymentId: session.payment_intent };
  }

  public async createStripeSession(test_session_id: number, type: string, statusCode: number, lang: string, isAcomm: boolean, params?: Params): Promise<Data> {

    let MPT_SESSION_BOOKING_FEE_RECORD
    
    try {
      MPT_SESSION_BOOKING_FEE_RECORD = await getSysConstString(this.app, 'MPT_SESSION_BOOKING_FEE');
    } catch (error) {
      throw new Errors.BadRequest('NO_BOOKING_CHARGE_SET');
    }
  
    let productName = await getSysConstString(this.app, 'STRIPE_PRODUCT_NAME');

    if(!productName) productName = 'MPT Credit'; // default product name

    let amount = parseInt(MPT_SESSION_BOOKING_FEE_RECORD);

    if (isNaN(amount)) {
      amount = parseInt(MPT_SESSION_BOOKING_FEE_RECORD[0].value, 10);
    }

    let statusCodeRecord = await this.app.service('db/read/mapping-mpt-status-codes')
      .db()
      .where('mpt_status_code', statusCode)
      .limit(1);

    if (statusCodeRecord[0]?.amount) amount = statusCodeRecord[0].amount;

    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    const uid = <number> userInfo.uid;
    const domain = params && params.headers ? params.headers.referer : '';
    const successUrl = isAcomm ? `${domain}#/${lang}/test-taker/book-session/accomm/true` : `${domain}#/${lang}/test-taker/book-session/accomm/false`;
    const cancelUrl = `${domain}#/${lang}/test-taker/dashboard`;

    const stripeAPI = this.app.get('stripe_api');

    const stripe = new StripeAPI(stripeAPI.secretKey, {
      apiVersion: stripeAPI.apiVersion,
    });

    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'cad',
            product_data: {
              name: productName,
            },
            unit_amount: this.convertPriceForStripe(amount),
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: successUrl,
      cancel_url: cancelUrl,
      metadata: {uid: uid, sessionId: test_session_id, type: type}
    });

    return { url: session.url, paymentId: session.payment_intent };
  }

  // public async createStripeRefund(paymentIntent)

  async create (data: Data, params?: Params): Promise<Data> {
    // const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    // const uid = <number> userInfo.uid;
    const uid = 6151
    const {test_session_id, type, lang_code, isAccomm, attemptId} = <any> data;

    let statusCode = -1;
    const statusCodeRecord = await this.app.service('db/read/user-metas')
      .db()
      .where('uid', uid)
      .where('key', 'mpt_status_code')
      .limit(1);
      
      if(statusCodeRecord && statusCodeRecord.length) statusCode = <number> statusCodeRecord[0].value;

    if (type === 'CREDIT_PURCHASE') return await this.createStripeSession(test_session_id, type, statusCode, lang_code, <boolean> isAccomm, params);
    else if (type === 'APPEAL') return await this.createStripeSessionForAppeals(type, statusCode, lang_code, attemptId, params);
    return [];
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }
}
