import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { ITestSessionBookingUser } from '../../../../db/schemas/test_session_booking_users.schema';
import { DBD_U_ROLE_TYPES } from '../../../../../constants/db-extracts';
import { IBookingRecord, ICreateBooking } from '../booking/booking.class';
import { Errors } from '../../../../../errors/general';
import { ITestSessionAvailTT } from '../../../../db/schemas/test-session-avail-tt.schema';
import { TEST_SESSION_REG_BUFFER_DAYS } from '../../../../../constants/db-constants';
import { isDatePast } from '../../../../../util/db-dates';
import { IInstitution } from '../../../../db/schemas/institutions.schema';
import { dbRawRead } from '../../../../../util/db-raw';
import { renderDateTime } from '../../../../../hooks/_util';
import { ITestAttempt } from '../../../../db/schemas/test_attempts.schema';

interface Data {}

const MAX_WAITLIST_COUNT = 4;

interface ServiceOptions {}

export class Waitlist implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  public async retrieveAllWaitlistApplicantRecords () {
    const waitlistApplicantRecords = await this.app
      .service('db/read/user-roles')
      .db()
      .where('is_revoked', 0)
      .where('role_type', 'mpt_waiting_list_applicant');

    return waitlistApplicantRecords;
  }

  public async retrieveWaitlistApplicantRecordsForSession (sessionGroupId: number) {
    const waitlistApplicantRecords = await this.app
    .service('db/read/user-roles')
    .db()
    .where('group_id', sessionGroupId)
    .where('is_revoked', 0)
    .whereIn('role_type', [
      DBD_U_ROLE_TYPES.mpt_waiting_list_applicant
    ])
    .select('uid');
    return waitlistApplicantRecords;
  }

  public async sendWaitlistAutoCancelNotification(uid: number, sessionId: number, params?: any) {
    const waitlistApplicantRecord = await this.app
      .service('db/read/users')
      .db()
      .where('id', uid)
      .limit(1);

    const testSessionRecord = await this.app
      .service('db/read/test-sessions')
      .db()
      .where('id', sessionId)
      .limit(1);

    const ts = testSessionRecord[0];

    let lang='en';
    const attempts = <ITestAttempt[]>await this.app
      .service('db/read/test-attempts')
      .db()
      .where('uid', uid)
      .where('test_session_id', sessionId);
    
    lang = attempts[0].booking_lang;
    
    const applicant_email = <string> waitlistApplicantRecord[0].contact_email;
    const userFirstName = waitlistApplicantRecord[0].first_name;
    const testSessionId = sessionId;
    const langCode = lang;
    const testSessionStartTime = renderDateTime(testSessionRecord[0].date_time_start, undefined, langCode);
    let testSessionCampusBuilding = testSessionRecord[0].campus_building;
    let testSessionRoom;
    if(ts.room === 'REMOTE'){
      testSessionRoom = await this.app.service('public/translation').getOneBySlug('tra_remote', langCode);
    }else{
      testSessionCampusBuilding = ts.campus_building;
      testSessionRoom = ts.room
    }
    const emailTemplate = await this.app.service('public/translation').getOneBySlug('email_waitlist_auto_cancel', langCode);
    const subject = await this.app.service('public/translation').getOneBySlug('email_waitlist_auto_cancel_subjectline', langCode);

    await this.app
      .service('mail/core')
      .sendEmail({
        subject: subject,
        emailTemplate: emailTemplate,
        emailAddress: applicant_email,
        parameterMapping: {
          userFirstName,
          testSessionId,
          testSessionCampusBuilding,
          testSessionRoom,
          testSessionStartTime
        },
        whitelabel    : params && params.headers ? params.headers.host : undefined
      });

  }

  async getApplicantFrontOfWaitingList(groupId: number) {
    return this.app
      .service('db/read/user-roles')
      .db()
      .where('group_id', groupId)
      .where('is_revoked', 0)
      .where('role_type', DBD_U_ROLE_TYPES.mpt_waiting_list_applicant)
      .orderBy('created_on', 'asc')
      .limit(1);
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create (data: ICreateBooking, params?: Params): Promise<Data> {

    const {
      test_session_id,
      access_code,
      lang
    } = data;

    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    const uid = <number> userInfo.uid;
    
    // verify available booking slots in session (using summary view)
    const ts:ITestSessionAvailTT = await this.app.service('public/test-taker/test-sessions/booking').getValidateTestSession(test_session_id, access_code, true);
    const test_window_id = ts.test_window_id;
    const group_id = <number> ts.test_session_group_id

    await this.app.service('public/test-taker/test-sessions/booking').validateDoubleBooking(uid, test_session_id, test_window_id);

   const waitlistRecords = await dbRawRead(this.app, [test_session_id], `
   SELECT * FROM mpt_dev.test_session_waitlist_users where test_session_id = ?
   ;`);
  if(waitlistRecords.length >= MAX_WAITLIST_COUNT){
    throw new Errors.Forbidden('MAXIMUM_WAITLIST_REACHED') 
  }

  const creditsEnabled: any = await this.app
      .service('public/credits/credit-system')
      .find();

  // check if they are already booked into another session (for now, don't care if the test was completed, but filter to test window)
  const activeBookingRecords = <IBookingRecord> await this.app
      .service('public/test-taker/test-sessions/booking')
      .getActiveBooking(uid, [test_window_id], creditsEnabled.isEnabled);
    // if (activeBookingRecords.booking){ 
    //   if(activeBookingRecords.booking.is_closed) {
    //     throw new Errors.Forbidden('TEST_COMPLETED');
    //   } else {
    //     throw new Errors.Forbidden('EXISTING_BOOKING'); 
    //   }
    // }
    // if (activeBookingRecords.waitlist){ 
    //  await this.app.service('public/test-taker/test-sessions/invitation').remove(<number> activeBookingRecords.waitlist.test_session_id);
    // }

    // COMMITTED
    // create new role
    const newRole =  await this.app
      .service('auth/user-role-actions')
      .assignUserRoleToGroup({
        uid,
        role_type : DBD_U_ROLE_TYPES.mpt_waiting_list_applicant,
        group_id,
        created_by_uid : uid,
      })

      //Ensure attempt is created for checklist 24-hour alert
      this.app.service('public/test-taker/invigilation/test-attempt').getCurrentAttempt(uid,test_session_id,{
        isCreateNewIfEmpty: true,
        isTestTaker: true,
        isPresent: false,
        setBookingLang: lang
      });

    // return payload
    const waitlist:Partial<ITestSessionBookingUser> = {
      uid,
      group_id,
      test_session_id,
      created_on: newRole.created_on,
      test_window_id
    };
    return {
      waitlist
    };
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async cancelWaitlist(test_session_id:number, uid:number){
    const ts = <ITestSessionAvailTT> await this.app
      .service('db/read/test-session-avail-tt')
      .get(test_session_id);
    if (!ts){
      throw new Errors.Forbidden('TEST_SESSION_NOT_AVAIL');
    }

    this.app
      .service('auth/user-role-actions')
      .revokeUserFromGroup(
        uid,
        ts.test_session_group_id,
        uid
      )

  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    const test_session_id = <number> id;
    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    const uid = <number> userInfo.uid;
    this.cancelWaitlist(test_session_id, uid);
    await this.app.service('public/test-taker/accommodations/pending-req').revokeAccommReqForTestSession(uid, test_session_id);
    return {}
  }
}
