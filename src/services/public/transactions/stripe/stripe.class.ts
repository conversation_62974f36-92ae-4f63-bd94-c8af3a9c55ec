import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import Stripe<PERSON><PERSON> from 'stripe';
import BodyParser from 'body-parser';
import logger from '../../../../logger';
import { getSysConstNumeric } from '../../../../util/sys-const-numeric';
import { ICredits } from '../../../db/schemas/credits.schema';
import { getSysConstString } from '../../../../util/sys-const-string';
import { BadRequest } from '@feathersjs/errors';

interface Data {}

interface ServiceOptions {}

export interface ITransaction {
  stripe_id?: string
  id?: number,
  date_time_transaction?: Date
}

export class Stripe implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.NotImplemented();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  public async completePartialRefund(uid: number, stripeId: string, amount = 10) {
    const stripeAPI = this.app.get('stripe_api');

    const stripe = new StripeAPI(stripeAPI.secretKey, {
      apiVersion: stripeAPI.apiVersion,
    })

    const refund = await stripe.refunds.create({
      payment_intent: stripeId,
      amount: this.convertPriceForStripe(amount)
    });

    return refund;
  }

  // MPT implementation
  private async completeCreditPurchase(paymentEvent: any) {
    const createdOn = new Date();

    const uid = paymentEvent.metadata.uid;

    const statusCodeRecord = await this.app
    .service('db/read/user-metas')
    .db()
    .where('uid', uid)
    .where('key', 'mpt_status_code');

    // credits allocated per payment - SMCS wants to assign 2 credits / one payment
    const STRIPE_SUCCESSFUL_CREDIT_ALLOTMENT:number = +(await getSysConstString(this.app, 'STRIPE_SUCCESSFUL_CREDIT_ALLOTMENT'));

    const creditRecords:ICredits[] = [];

    for(let i = 0 ; i < STRIPE_SUCCESSFUL_CREDIT_ALLOTMENT ; i++){      
      const newCreditRecord = await this.app
        .service('credits/credits')
        .createCredit(uid, 0, undefined, createdOn, statusCodeRecord[0] ? statusCodeRecord[0].value : undefined);      
        
      creditRecords.push(newCreditRecord);
    }

    const newTransaction: ITransaction = await this.app
      .service('public/transactions/transactions')
      .create({stripeId: paymentEvent.payment_intent});

    const totalPaymentValue = await getSysConstString(this.app, 'MPT_SESSION_BOOKING_FEE');  

    let value = +totalPaymentValue;
    if(creditRecords.length > 1) {
      value = value / creditRecords.length ;
    }
    
    for (let i = 0; i < creditRecords.length; i++ ){
      const newCredit = creditRecords[i];

      await this.app
        .service('credits/credit-transactions')
        .createAcquiredTransaction(newCredit.id, createdOn, uid, null, newTransaction.id, value);
    }  

  }

  private async updateStripeMetadata(stripe: StripeAPI, purchaseInvoice: any) {
    const paymentIntent = await stripe.paymentIntents.retrieve(purchaseInvoice.stripe_id);
    
    return stripe.paymentIntents.update(paymentIntent.id, {
      metadata: {
        "Vretta Invoice ID": purchaseInvoice.id,
      }
    });
  }

  private convertPriceForStripe(amount: number): number | undefined {
    return amount * 100;
  }

  async create (data: Data, params?: Params): Promise<Data> {
    const stripeAPI = this.app.get('stripe_api');

    const endpointSecret = stripeAPI.endpointSecret;
    const stripe = new StripeAPI(stripeAPI.secretKey, {
      apiVersion: stripeAPI.apiVersion,
    })
    let sig;
    sig = params!.headers!['stripe-signature'];

    let event;

    if(params) {
      event = stripe.webhooks.constructEvent(params.bodyRaw, sig, endpointSecret);
      const eventData: any = event.data.object;

      // Payment has been unsuccessful
      if (event.type === 'payment_intent.payment_failed') {
        logger.silly('Stripe payment is unsuccessful.');

        this.app.service('public/school-admin/session-purchase').processPayment(false, eventData);
      }
      // Customer is created
      if (event.type === 'customer.created') {
        // Create an invoice as a draft
        // const session = await stripe.invoices.create({
        //   customer: eventData.id
        // })

      }

      // Checkout session has been completed
      if (event.type === 'checkout.session.completed') {
        logger.silly('Stripe checkout session completed!');

        const purchaseInvoice = await this.app.service('public/school-admin/session-purchase').processPayment(true, eventData);
        if(purchaseInvoice.length === 0) {
          throw new BadRequest('No purchase invoice found');
        }
        this.updateStripeMetadata(stripe, purchaseInvoice);
        return {purchaseInvoice};
      }
    }
    
    return [];
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }
}
