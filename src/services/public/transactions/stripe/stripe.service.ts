// Initializes the `auth/user-role-actions` service on path `/auth/user-role-actions`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Stripe } from './stripe.class';
import hooks from './stripe.hooks';
import { IRequestRawBody } from '../../../../app';
import { IncomingMessage } from 'http';
interface IFeathersReq extends IncomingMessage {
  feathers:any
}

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/transactions/stripe': Stripe & ServiceAddons<any>;
  }
}

export default function (app: Application) {
  const options = {
    paginate: app.get('paginate')
  };

  app.use('/public/transactions/stripe', (req:IRequestRawBody & IFeathersReq, res, next) => {
    if (!req.feathers) {
      next();
      return;
    }
    const { bodyRaw, bodyEncoding } = req;
    Object.assign(req.feathers, { bodyRaw, bodyEncoding });
    next();
  })

  // Initialize our service with any options it requires
  app.use('/public/transactions/stripe', new Stripe(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/transactions/stripe');

  service.hooks(hooks);
}
