import { Id, NullableId, Pa<PERSON>ated, Params, ServiceMethods, Query } from '@feathersjs/feathers';
import { Application } from '../../../declarations';
import { ITranslation } from '../../db/schemas/translations.schema';
import * as Errors from '@feathersjs/errors';
import { DB_SELECT_ERROR } from '../../../errors/db';
import { currentUid } from '../../../util/uid';

interface Data {}

interface ServiceOptions {}

const fs = require('fs');
let TRA_DB:any;
fs.readFile('translation_data/translations.json', (error:any, data:any) => {
  if (error) {
    console.log('TRANSLATION FILE READ FAIL', error);
  } else {
    try {
      TRA_DB = JSON.parse(data);
    } catch (error) {
      console.log(error);
    }
  }
});


export class Translation implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  private async recordLog(newRecord:ITranslation, uid:number){
    const recordCopy = {
      ... newRecord,
      _id: newRecord.id,
      created_by_uid: uid,
    }
    delete recordCopy.id;
    return this.app
      .service('db/write/translations-log')
      .create(recordCopy)
  }

  async getOneBySlug (slug:string, langCode:string, defaultLang:string = 'en') : Promise<string> {
    //read translation locally
    if(!langCode){
      langCode = defaultLang; //This a temporary fix for attempts with no booking language 
    }

    let trans = TRA_DB[langCode][slug] || TRA_DB[defaultLang][slug];
    if(trans){
      return trans;
    }
    const transFieldQuery:Partial<ITranslation> = {slug};
    return await this.app
      .service('db/read/translations')
      .find({
        $limit: 1,
        query: transFieldQuery
      })
      .then((res) => {
        const transRecords = <Paginated<any>> res;
        if(!transRecords.total){
          throw new Errors.BadRequest('MISSING_TRANSLATION_RECORD');
        }
        const translationRecord = <ITranslation>transRecords.data[0]
        const translations = <any>transRecords.data[0];
        return translations[langCode] ||
              translations[defaultLang] ||
              translationRecord.slug
      })
  }

  /** Replace placeholders with values (same as what happens directly in lang.tra() on client) */
  fillProps(str:string, props:{[key:string]: string | number} ): string{
    str = str.replace(/{{([^}}]+)?}}/g, ($1, $2) => 
    $2.split('.').reduce((p: any, c: any) => p ? p[c] : '', props));
    return str;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    const queryParams = <Query> (<Params>params).query;
    const $skip = parseInt(queryParams.$skip);
    let $limit = parseInt(queryParams.$limit);
    if ($limit == 6000){
      $limit = 500000
    }
    const query:any = {
      $limit,
      $skip,
    };
    const props = [
      'slug',
      'en',
      'fr',
      'notes',
    ];
    props.forEach(prop => {
      if (queryParams[prop]){
        query[prop] = queryParams[prop]
      }
    })
    return await this.app
      .service('db/read/translations')
      .find({
        query
      });
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create (data: Data, params?: Params): Promise<Data> {
    if (params){
      const uid = await currentUid(this.app, params)
      const newRecord = <ITranslation> await this.app.service('db/write/translations').create(data);
      await this.recordLog(newRecord, uid);
      return newRecord; // {id: newRecord.id};
    }
    throw new Errors.BadRequest()
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if (params){
      const uid = await currentUid(this.app, params)
      const newRecord = <ITranslation> await this.app.service('db/write/translations').patch(id, data);
      await this.recordLog(newRecord, uid);
      return newRecord;
    }
    throw new Errors.BadRequest()
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

}
