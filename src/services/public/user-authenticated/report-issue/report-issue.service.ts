// Initializes the `public/user-authenticated/report-issue` service on path `/public/user-authenticated/report-issue`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { ReportIssue } from './report-issue.class';
import hooks from './report-issue.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/user-authenticated/report-issue': ReportIssue & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/user-authenticated/report-issue', new ReportIssue(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/user-authenticated/report-issue');

  service.hooks(hooks);
}
