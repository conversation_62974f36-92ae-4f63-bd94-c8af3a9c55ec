import { STUDENT_ROLE_TYPES } from "../services/public/educator/walk-in-students/walk-in-students.class"

export const SQL_SC_FROM_AC = () => `
  select sc.id
       , sc.schl_group_id
       , sc.key
       , sc.group_id
       , sc.semester_id
  from school_classes sc
  where sc.access_code = :accessCode
    and sc.is_active = 1
`
export const SQL_USERMETA_AND_ROLES_BY_STUNUM = () => `
  select um.uid
  from user_metas um
  join user_roles ur
    on ur.uid = um.uid
    and ur.group_id = :school_group_id
    and ur.is_revoked = 0
  where um.key_namespace in ('abed_sdc', 'abed_course')
    and um.key in (:user_meta_keys)
    and um.value = :studentNumber
`
export const SQL_USERMETA_BY_STUNUM = () => `
  select um.uid
  from user_metas um
  join user_roles ur on ur.uid = um.uid and ur.is_revoked = 0
  join schools s on s.group_id = ur.group_id 
  where um.key_namespace in ('abed_sdc', 'abed_course')
    and um.key in (:user_meta_keys)
    and um.value = :studentNumber
`
export const SQL_ROLETYPES_BY_UID = () => `
  select ur.role_type account_type
       , ug.group_type group_type
       , s.name s_name
       , s.foreign_id s_foreign_id
       , sd.name sd_name
       , sd.foreign_id sd_foreign_id
  from user_roles ur
  join users u
    on ur.uid = u.id
    and ur.is_revoked = 0
  join u_groups ug
    on ug.id  = ur.group_id
  left join schools s
    on s.group_id = ur.group_id
  left join school_districts sd
    on sd.group_id = ur.group_id
  where uid = :uid
`
export const SQL_USERMETA_LANG_BY_UID = () => `
  select um.value
  from user_metas um
  where um.key_namespace = 'abed_dyn'
    and um.key = 'lang'
    and um.uid = :uid
`
export const SQL_STU_SC_BY_AC = (forWalkIn: boolean = false) => `
  SELECT sc.id
       , sc.group_id
       , sc.access_code
       , sc.group_type
       , urt.uid
  FROM school_classes sc
  JOIN user_roles urt
    ON urt.group_id = sc.group_id
    AND urt.role_type = '${forWalkIn ? STUDENT_ROLE_TYPES.walk_in_student_role : STUDENT_ROLE_TYPES.regular_student_role}'
    AND ${forWalkIn ? 'urt.is_removed != 1' : 'urt.is_revoked != 1' /* todo: no difference? */}
  WHERE urt.uid = :uid
    AND sc.access_code like :accessCodeToCheck
    AND sc.is_active = 1
;`

export const SQL_DIP_SC_BY_SC_AC = () => ` -- SQL_DIP_SC_BY_SC_AC
  SELECT ac.course_code_foreign AS asmt_type_slug
      , tw.pasi_exam_period
  FROM school_classes sc
  JOIN school_class_test_sessions scts 
      ON scts.school_class_id = sc.id
  JOIN test_sessions ts 
      ON ts.id = scts.test_session_id
  JOIN test_window_td_alloc_rules twtdar 
      ON twtdar.slug = scts.slug
  JOIN test_window_td_types twtdt 
      ON twtdt.type_slug = twtdar.slug 
      and twtdt.test_window_id is null
      and twtdt.is_revoked = 0
  JOIN test_windows tw 
      ON tw.id = twtdt.test_window_id
  JOIN assessment_courses ac 
      ON ac.course_code = twtdt.course_code
  WHERE sc.access_code = :accessCode 
      AND sc.group_type = :group_type 
      AND sc.is_active = 1 
      AND ts.is_closed = 0
`;

export const SQL_STU_SC_BY_AC_GUEST = () => `
  SELECT sc.id
       , sc.group_id
       , sc.access_code
       , sc.group_type
       , urt.uid
  FROM
    mpt_dev.school_classes sc
  JOIN
    mpt_dev.school_classes_guest scg
    ON scg.invig_sc_group_id = sc.group_id
    AND scg.is_revoked = 0
  JOIN
    mpt_dev.user_roles urt
    ON urt.group_id = scg.guest_sc_group_id
    AND urt.role_type = '${STUDENT_ROLE_TYPES.regular_student_role}'
    AND urt.is_revoked != 1
  WHERE urt.uid = :uid
    AND sc.access_code like :accessCodeToCheck
    AND sc.is_active = 1
;`

export const SQL_SC_ACCESS_CODE = () => `
  SELECT id
  FROM school_classes
  WHERE access_code LIKE :accessCodeToCheck
    AND is_active = 1;
`

export const SQL_SELECT_USER_DOB = () => `
  SELECT um.value
  FROM user_metas um
  WHERE um.key = 'DateofBirth'
    AND um.uid = :uid
    AND um.key_namespace IN ('abed_sdc', 'abed_course');
`;

export const SQL_STU_ALIVE_CHECK = () => `
  SELECT um.value
  FROM user_metas um
  WHERE um.key_namespace = 'abed_pasi_sync'
    AND um.key = 'IsDeceased'
    AND um.uid = :uid;
`;

export const SQL_STU_DEACTIVE_CHECK = () => `
  SELECT value
  FROM user_metas AS um
  WHERE um.key_namespace = 'abed_pasi_sync'
    AND um.key = 'IsDeactivated'
    AND um.uid = :uid
`





export const SQL_STU_TW_UM_DIP = () => `
  SELECT tum.asmt_type_slug
       , tum.meta
  FROM tw_user_metas tum
  WHERE tum.key_namespace = 'abed_pasi_sync'
    AND tum.key = 'StudentDiplomaExamInfo'
    AND tum.uid = :uid;
`

export const SQL_PASI_EXAM_PERIOD = () => `
  SELECT pd_de.ExamPeriod
  FROM pasi_data.diploma_exams pd_de
  WHERE pd_de.RefId = :examRefId
`