import { knex, Knex } from 'knex';
import { Application } from '../declarations';
import logger from '../logger';

export default function veaDbConn (config: any, dbConnName: string):Knex {
  const { client, connection, debug, pool, logQueryTiming} = config;
  config.pool.afterCreate = function (conn: any, done: any) {
    logger.debug('knex_%s_pool_conn_acquired', dbConnName, {
      connectionId: conn.connectionId,
      __knexUid: conn.__knexUid
    });
    done();
  };

  const db = knex({
    client,
    connection,
    pool,
    debug,
    log: {
      warn(message) {
        logger.warn('knex_%s_log', dbConnName, {knexMsg: message})
      },
      error(message) {
        logger.error('knex_%s_log', dbConnName, {knexMsg: message})
      },
      deprecate(message) {
        logger.warn('knex_%s_log', dbConnName, {isDeprecationMsg: true, knexMsg: message})
      },
      debug(message) {
        logger.silly('knex_%s_log', dbConnName, {knexMsg: message})
      }
    }
  });

  const {
    propagateCreateError,
    min,
    max,
    acquireTimeoutMillis,
    createRetryIntervalMillis,
    createTimeoutMillis,
    idleTimeoutMillis,
    reapIntervalMillis
  } = db.client.pool
  logger.info('knex_%s_pool_initial', dbConnName, {
    propagateCreateError,
    min,
    max,
    acquireTimeoutMillis,
    createRetryIntervalMillis,
    createTimeoutMillis,
    idleTimeoutMillis,
    reapIntervalMillis
  });
  setInterval(() => {
    const numDestroyed = Array.isArray(db.client.pool.destroyed)
      ? db.client.pool.destroyed.length
      : db.client.pool.destroyed;
    logger.debug('knex_%s_pool_status', dbConnName, {
      numDestroyed,
      numFree: db.client.pool.free.length,
      numUsed: db.client.pool.used.length,
      numTotalHeld: db.client.pool.free.length + db.client.pool.used.length,
      numPendingAcquires: db.client.pool.pendingAcquires.length,
      numPendingCreates: db.client.pool.pendingCreates.length
    });
  }, 300000);

  const veaQueryTiming = new Map<string, number>()
  db.on('query', function(obj) {
    if (!obj?.__knexQueryUid) {
      return;
    }
    veaQueryTiming.set(obj.__knexQueryUid, Date.now());
  });
  db.on('query-response', function(response, obj, builder) {
    let queryStart
    if (!obj || !(queryStart = veaQueryTiming.get(obj.__knexQueryUid))) {
      return;
    }
    if (logQueryTiming) {
      logger.debug('knex_%s_query_success', dbConnName, {
        duration: Date.now() - queryStart,
        sql: obj.sql,
        bindings: obj.bindings
      });
    }
  });
  db.on('query-error', function(err, obj) {
    let queryStart
    if (!obj || !(queryStart = veaQueryTiming.get(obj.__knexQueryUid))) {
      return;
    }
    logger.error('knex_%s_query_error', dbConnName, err, {
      duration: Date.now() - queryStart,
      sql: obj.sql,
      bindings: obj.bindings
    });
  });

  return db;
}
