export enum VEA_CONTEXT {
  IS_BCED = 'isBCED',
  IS_EQAO = 'isEQAO',
  IS_ABED = 'isABED',
  IS_NBED = 'isNBED',
}

export interface IWebsocketConfig {
  authoring: {
    url: string;
    region: string;
  },
  invigilation: {
    url: string;
    region: string;
    secretPass: string;
    gracePeriod: number;
  }
}

export interface IRespondusConfig {
  secret_index: string;
  secret_1: string;
  secret_2: string;
  secret_iv: string;
  secret_version: number;
  cipher: string;
}