// ancillary dev:
// - UI: 
// - data model: connect the reporting profile to the twtar/tw profile
// - data model: ability to have multiple reporting profiles in a tet window might come in handy (this should be supported out of the box as long as we attach th)
// - warning: twtar in same tw assigned to overlapping domain
// - warning: domain schema contains domains that are not contained in  (also editing error)
// - warning: cut score profile is referencing category slugs that are not present in the category schema


// system level enums
enum EScoreBasis {
    RAW_TOTAL = 'RAW_TOTAL',
    RAW_PERCENT = 'RAW_PERCENT',
    IRT = 'IRT',
}

enum EDomainConstraintTypes {
    REGISTER_SCORE_ITEM = 'REGISTER_SCORE_ITEM',
    REGISTER_NUM_ITEMS = 'REGISTER_NUM_ITEMS',
    REGISTER_SCORE_TOTAL = 'REGISTER_SCORE_TOTAL',
}
enum EDomainConstraintValueComparison {
    EQUAL= 'EQUAL',
    MIN= 'MIN',
    MAX= 'MAX',
}

enum ECutScoreTypes {
    NORM_CUTS = 'NORM_CUTS',
    CRIT_CUTS = 'CRIT_CUTS',
}

// Reflects the work done on cut scores between May 2024 and Dc 2024 (prior to deep review and refactoring)
interface ICutScoreDefConfig_v1 {
    [lang: string]: {
        [item_domain_slug: string]: {
            short: string, // category slug
            long: string, // category caption
            cut_score: number, // 
            order: number, // order of interpretation
            color: string, // colour to use for the category for reporting purposes (repetitive)
        }
    }
}

// Reconsidering repetition and relevant data stores

interface IReportingProfile {
    id: number,
    authoring_group_id: number, // int
    slug: string,
    created_on: string, // datetime
    created_by_uid: number, // int
    is_revoked: boolean, // boolint
    revoked_on: string, // datetime
    revoked_by_uid: number, // int
    // List of Domains
    domain_schema_id: number,
    // Weight Scaling Rules
    domain_score_scaling_factor_profile_id?: number,
    // List of Cut Score Categories
    category_schema_id: number,
    // Set of Rules for Computing Cut Scores
    cut_score_schema_id: number, // defines the method of the cuts (very important for quantile based reporting). this should change the least frequently
    // Default Cut Score Values
    default_cut_score_profile_id: number, // defines the cannonical slug used to pull up cut values (this will tend to get shuffled out frequently or get overridden by school district specific profiles)
    config: {
        scoreBasis: EScoreBasis,
        isScaling?: boolean, // assume false

    }
    // overrides_config: {
    //     domain_schema_: Partial<IReportingDomainSchema>
    //     domain_score_scaling_factor_profile: Partial<IDomainScoreScalingFactorProfile>,
    //     category_schema: Partial<ICategorySchema>,
    // }
}

interface IReportingDomainSchema {
    id: number, // int
    authoring_group_id: number, // int
    created_on: string,  // datetime
    created_by_uid: number, // int
    is_revoked: boolean, // boolint
    revoked_on: string, // datetime
    revoked_by_uid: number, // int
    config: {
        domain_types: {
            type_slug: string,
            caption: string,
            item_register_field: string,
            description: string,
        }[],
        domains: {
            type_slug: string,
            slug: string,
            caption: string
            description: string,
            translations?: {
                lang: string,
                caption: string,
                description?: string,
            }[]
        }[],
        domainRules?: {
            type_slug: string,
            slug: string,
            constraintType: EDomainConstraintTypes,
            constraintComparison: EDomainConstraintValueComparison,
            constraintValue: number | string | string[] | number[]
        }[],

    }
}

interface IDomainScoreScalingFactorProfile {
    id: number, // int
    reporting_profile_id: number, // int
    source_export_id?: number, // int
    created_on: string, // datetime
    created_by_uid: number, // int
    is_revoked: boolean, // boolint
    revoked_on: string, // datetime
    revoked_by_uid: number, // int
    config: {
        scalings: {
            langs: string[],
            domain_slug: string,
            weight_reference?: number, // float (but usually int)
            weight_current: number, // float (but usually int) 
            weight_scaling: number, // float
            scaling_factor: number, // float
        }[]
    }
}
interface ICategorySchema {
    id: number, // int
    authoring_group_id: number, // int
    created_on: string, // datetime
    created_by_uid: string, // int
    is_revoked: boolean, // boolint
    revoked_on: string, // datetime
    revoked_by_uid: number, // int
    config: {
        description?: string,
        categoryScales: {
            slug: string, // reading, writing, math
        }[],
        categoryScaleRules?: {
        }[],
        categories: {
            category_scale_slug: string,
            slug?: string, // defaults to category_scale_slug
            caption: string,
            category_value: number,
            color?: string, // color hex pattern, e.g.,  "#808080"
            description?: string,
            meta?: {
                [prop:string]: number | string,
            }
            translations?: {
                lang: string,
                caption: string,
                description?: string,
            }[]
        }[]
    }
}

/*

For example:
...
categories: [  
    { 
        order: 0,
        slug: 'NA',
        caption: 'N/A',
        color: "#808080",
    },
    { 
        order: 1,
        slug: 'RAS',
        caption: 'Requiring Additional Support',
        color: "#FF0000",
    },
    { 
        order: 2,
        slug: 'NRAS',
        caption: 'Not Requiring Additional Support',
        color: "#008000",
    },
]

*/



interface ICutScoreSchema {
    id: number, // int
    authoring_group_id: number, // int
    created_on: string, // datetime
    created_by_uid: string, // int
    is_revoked: boolean, // boolint
    revoked_on: string, // datetime
    revoked_by_uid: number, // int
    source_export_id: number, // int
    config: {
        method: string,
        canonicalSlug: string,
        methodConfig: {
            num_decimals: number, // int
            [prop:string]: string | string[] | number | number[] | boolean , // not sure what t ofill into this
        },
        cutDefinitions: {
            langs: string[],
            scopeType: 'DEFAULT' | 'OVERALL' | 'DOMAIN' , // I am not sure if this is going to make sense 
            isDefault: boolean,
            isOverall: boolean, // What does this really mean if we ahve reporting profiles that span across
            domainSlug: string,
            categorySlug: string,
            cutScoreCeil: number,
        }[]
    }
}


// we need to separate the actual cut points from the method of determining the cut points, otherwise we will be rolling over the rule set over and over
interface ICutScoreProfile {
    id: number, // int
    reporting_profile_id: number, // int
    source_export_id?: number, // int
    created_on: string, // datetime
    created_by_uid: number, // int
    is_revoked: boolean, // boolint
    revoked_on: string, // datetime
    revoked_by_uid: number, // int
    config: {
        canonicalSlug: string,
        cutValues: { // flattened version of cutDefinitions
            lang: string,
            isOverall: boolean, // What does this really mean if we ahve reporting profiles that span across
            domainSlug?: string, // only nullable if isOverall==true
            categorySlug: string,
            cutScoreCeil: number,
        }[]
    }
}

// (I am going to define one score scaling factor profile per test window type) 
const scalingFactors : Partial<IDomainScoreScalingFactorProfile> = {
}

