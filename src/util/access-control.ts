
import { Application } from "@feathersjs/express";
import { Params } from "@feathersjs/feathers";
import { Errors } from "../errors/general";


export const ensureReqQueryAuthGroupIds = (app: Application, params?:Params) => {
    const { query } = params || {};
    if (!query?.authoring_group_ids) {
        throw new Errors.BadRequest('MISSING:authoring_group_ids')
    }
    // Validate the input is an array
    if (!Array.isArray(query.authoring_group_ids)) {
        throw new Errors.BadRequest('MALFORMED:authoring_group_ids');
    }
    const authoring_group_ids = query.authoring_group_ids // .join();
    return authoring_group_ids;
}
