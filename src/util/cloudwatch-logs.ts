import { GetQueryResultsRequest, GetQueryResultsResponse, StartQueryRequest } from "aws-sdk/clients/cloudwatchlogs";
import { Errors } from "../errors/general";
import AWS from 'aws-sdk';
import logger from "../logger";

export interface AWSCredentials {
  accessKeyId: string,
  secretAccessKey: string
}
export const CLOUDWATCH_LOGS_ABED = 'vea-abed-api-logs'
export const CLOUDWATCH_LOGS_CAEC = 'vea-caec-api-logs'
export const getCloudwatchLogs = async (logGroupName: string, queryString: string, startTime: Date, endTime: Date, awsCredentials: AWSCredentials): Promise<GetQueryResultsResponse> =>  {
    const callHash = new Date().getTime();

    AWS.config.update({
      region: 'ca-central-1',
    });
    const creds = new AWS.Credentials(awsCredentials.accessKeyId, awsCredentials.secretAccessKey);
    AWS.config.credentials = creds;

    const cloudWatchLogs = new AWS.CloudWatchLogs({apiVersion: '2018-11-29'});

    logger.info('CLOUDWATCH_LOGS_ABED', {logGroupName, queryString, startTime, endTime, callHash});

    const queryParams: StartQueryRequest = {
      logGroupName, /* required */
      endTime: endTime.getTime(),
      startTime: startTime.getTime(),
      queryString
    };

    try {
        const data = await cloudWatchLogs.startQuery(queryParams).promise();
        const queryId = data.queryId;
        if (!queryId) {
          throw new Errors.GeneralError('MISSING_QUERY_ID');
        }
        // Poll for query results
        const params: GetQueryResultsRequest = { queryId: queryId };
        let response: GetQueryResultsResponse | undefined = undefined

        while (!response || (response.status != 'Complete' && response.status != 'Failed' && response.status != "Cancelled")) {
          let pollTime = new Date().getTime();
          logger.info('CLOUDWATCH_LOGS_POLLING', {callHash, pollTime, logGroupName, queryString, startTime, endTime});

          await new Promise(resolve => setTimeout(resolve, 500));
          response = await cloudWatchLogs.getQueryResults(params).promise()
        }
        return response;
    } catch (err: any) {
        if(err.code && err.code == 'ThrottlingException') {
            logger.info('CLOOUDWATCH_LOGS_THROTTLED', {callHash, error: err, logGroupName, queryString, startTime, endTime});
            throw new Errors.TooManyRequests('AWS_REQUEST_RATE_EXCEEDED')
        }
        logger.info('CLOUDWATCH_LOGS_ERROR', {callHash, error: err, logGroupName, queryString, startTime, endTime});
        throw new Errors.GeneralError('ERROR')
    }
}

export const parseLogs = (response: GetQueryResultsResponse) => {
    return response.results?.map((log) => {
        const parsedLog: any = {};
        log.forEach((field) => {
            if (!field.field || !field.value) {
                return;
            }
            const propertyName = field.field.replace('@','_');
            parsedLog[propertyName] = field.value;
        })
        return parsedLog;
    })
}