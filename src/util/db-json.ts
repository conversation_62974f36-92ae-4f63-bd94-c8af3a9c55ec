export const applyDbRecordJsonParse = (records:any[], fields:string[]) => {
    let nFailed = 0
    const n = records.length;
    for (let record of records){
        for (let prop of fields){
            try {
                record[prop] = JSON.parse(record[prop]);
            }
            catch (e){
                nFailed ++ 
            }
        }
    }
    return {n, nFailed}
}

export const reverseDbRecordJsonParse = (records:any[], fields:string[]) => {
    let nFailed = 0
    const n = records.length;
    for (let record of records){
        for (let prop of fields){
            if (record[prop]){
                try {
                    record[prop] = JSON.stringify(record[prop]);
                }
                catch (e){
                    nFailed ++ 
                }
            }
        }
    }
    return {n, nFailed}
}
