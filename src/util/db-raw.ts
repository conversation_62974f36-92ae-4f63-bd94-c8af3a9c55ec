import { Application } from '@feathersjs/feathers';
import { Knex } from 'knex';
import logger from '../logger';
var SqlString = require('sqlstring');

// for benchmarking sequences 


export const dbRawRead = async (app:Application, props:any[] | {} , query:string) => {
  const db:Knex = app.get('knexClientRead');
  const res = await db.raw(query, props);
  return <any[]> res[0];
}
export const dbRawReadReporting = async (app:Application, props:any[] | {}, query:string) => {
  const db:Knex = app.get('knexClientReadReporting');
  const res = await db.raw(query, props);
  return <any[]> res[0];
}
export const dbRawReadSingle = async (app:Application, props:any[] | {}, query:string) => {
  const records = await dbRawRead(app, props, query);
  return records[0];
}

export const dbRawReadSingleReporting = async (app:Application, props:any[], query:string) => {
  const records = await dbRawReadReporting(app, props, query);
  return records[0];
}

export const dbEscapeString = async (str:string, isWildcardWrapper:boolean = false, isQuoteless:boolean = false) => {
  let wrappedString = SqlString.escape(str);
  if (isWildcardWrapper){
    wrappedString = `'%${wrappedString.substr(1, wrappedString.length-2)}%'`
  }
  else if (isQuoteless){
    return wrappedString.substr(1, wrappedString.length-2);
  }
  return wrappedString
}

export const dbEscapeNum = async (str:string | number) => {
  return SqlString.escape(str);
}



export const dbRawReadCount = async (app:Application, query:string, props:any[] = []) => {
  const records = await dbRawRead(app, props, `
    select count(0) as count
    from (
      ${query}
    ) t_count
  ;`)
  return records[0].count;
}

export const dbRawReadCountReporting = async (app:Application, query:string, props:any[] = []) => {
  const records = await dbRawReadReporting(app, props, `
    select count(0) as count
    from (
      ${query}
    ) t_count
  ;`)
  return records[0].count;
}

export const dbRawWrite = async (app:Application, props:any[]| {}, query:string) => {
  const db:Knex = app.get('knexClientWrite');
  const res = await db.raw(query, props);
  return <any[]> res[0];
}

export const dbRawWriteMulti = async <T>(app:Application, query:string, source:T[], propExtractor: (entry:T) => any[]) => {
  if(!source || source.length === 0) {
    return;
  }
  const wQueries:string[] = [];
  let wProps:any[] = [];
  source.forEach(entry => {
    wQueries.push(query);
    wProps.splice(wProps.length, 0, ...propExtractor(entry))
  })
  return dbRawWrite(app, wProps, wQueries.join(';') + ";")
}

export const AND_MATCH_ANY = async (query:any, prop:string, isString=true, val?:number|string, isExact?:boolean) => {
  let filterValue = val || query[prop];
  if (!filterValue){ return ''; }
  filterValue = (''+filterValue).trim();
  if (!filterValue){ return ''; }
  const filterOptions = filterValue.split('\n')
  const clauseElements:string[] = [];
  for (let i=0; i<filterOptions.length; i++){
    let filterOption:string = filterOptions[i];
    if (filterOption && filterOption.trim()){
      if (isString){
        if (isExact){
          clauseElements.push(`(${prop} = ${await dbEscapeString(filterOption)})`)
        }
        else {
          clauseElements.push(`(${prop} LIKE ${await dbEscapeString(filterOption, true)})`)
        }
      }
      else{
        clauseElements.push(`(${prop} = ${await dbEscapeNum(filterOption)})`)
      }
    }
  }
  if (filterOptions.length === 0){
    return '';
  }
  return `AND (${ clauseElements.join(' OR ') }) `
}

/**
 * This method will mutate the state of the array that is passed in.
 * @param app
 * @param table the name of the table to insert into
 * @param rows an array of object to insert into the table
 * @param idCol optional, if supplied, the supplied rows will be updated by setting their new
 * @returns the new auto-increment id of the first row inserted
 */
export async function dbBulkInsert (app:Application, table:string, rows:any[], idCol?:string) { 
  if (idCol) {
    const rowsWithIds = rows.filter(row => row[idCol] !== null && row[idCol] !== undefined);
    if (rowsWithIds.length) {
      logger.silly('insert rows with disallowed ids', rowsWithIds);
      throw new Error('if idCol is supplied, row[idCol] must be null or undefined for all rows');
    }
  }
  const db:Knex = app.get('knexClientWrite');
  const firstInsertId:number = await db(table).insert(rows);
  if (idCol && firstInsertId) {
    let currentId = firstInsertId;
    rows.forEach(row => row[idCol] = currentId++) // assuming that they are consecutive (suppposed to be "guaranteed" as per Zach)
  }
  return firstInsertId;
}

interface IQueryPerf { 
  slug:string,
  order: number,
  duration:number,
  numRecords?:number,
}
interface IRawSeqReadOptions { 
  isAvoidReplica?:boolean
}
export class DbRawSeq {
  private queryPerformances:IQueryPerf[] = [];
  constructor( 
    private app:Application,
    private seqSlug:string ,
  ){ }
  async read(query:string, props:any[] | {}, options?:IRawSeqReadOptions){
    options = options || {};
    const slug = query.split('\n')[0] // assuming first line has identifier
    const timestampStart = (+new Date())
    let records:any[] = [];
    if (options.isAvoidReplica){
      records = await dbRawRead(this.app, props, query);
    }
    else {
      records = await dbRawReadReporting(this.app, props, query);
    }
    const timestampEnd = (+new Date())
    this.queryPerformances.push({
      slug,
      order: this.queryPerformances.length +1, 
      duration: (timestampEnd - timestampStart) / 1000,
      numRecords: records.length,
    })
    return records
  }
  getBenchmarks(){
    return this.queryPerformances;
  }
}