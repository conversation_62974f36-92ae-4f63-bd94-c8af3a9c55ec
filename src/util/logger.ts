const _ = require('lodash');

interface IRequestLogEntry {
    slug:string, 
    data?:any,
    timestamp?: number,
    from_start_ms?: number,
    from_last_ms?: number,
}

export class RequestBreakdown {
    
    log:IRequestLogEntry[] = []

    constructor(){
        this.stamp('start')
    }

    stamp(slug:string, data?:any){
        const timestamp = +(new Date());
        let from_start_ms = 0;
        let from_last_ms = 0;
        // time since last log
        const lastLog = this.log[this.log.length-1];
        if (lastLog && lastLog.timestamp){
            from_last_ms = timestamp - lastLog.timestamp
        }
        // time since first log
        const firstLog = this.log[0];
        if (firstLog && firstLog.timestamp){
            from_start_ms = timestamp - firstLog.timestamp
        }
        // store
        this.log.push({slug, data, timestamp, from_start_ms, from_last_ms})
    }

    getLog(){
        return _.orderBy(this.log, ['from_last_ms'], ['desc']);
    }
}