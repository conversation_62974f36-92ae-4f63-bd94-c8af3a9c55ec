const _ = require('lodash');

export const getPropVals = (data:any, props:string[], allowNull:boolean=true) => {
  const extraction:any = {};
  props.forEach(prop => {
    const val = data[prop];
    const isNull = (val === undefined || val === null);
    if (!isNull || allowNull){
      extraction[prop] = val;
    }
  });
  return extraction
}

export const arrPropPick = <T>(arr:T[], props:string[]) => {
  return arr.map( el => _.pick(el, props) )
}

export const boolQueryParam = (val:string) => {
  if (!val){
    return false;
  }
  else {
    return !!JSON.parse(val);
  }
}

export const arrUnique = (a:any[]) => [...new Set(a)];

export const arrToAggrMap = <T>(arr:T[], prop:string) => {
  const map:Map<number, T[]> = new Map();
  arr.forEach(r => {
    const key = +(<any>r)[prop]
    let aggr:T[] | undefined = map.get(key);
    if (!aggr){
      aggr = [];
      map.set(key, aggr);
    }
    aggr.push(r);
  });
  return map;
};


export const arrToMap = <T>(arr:T[], prop:string | string[], options?: {isKeyNonNumeric?:boolean, reduceToProp?:string, keyJoinChar?:string}) => {
  const {isKeyNonNumeric, reduceToProp, keyJoinChar} = (options || {})
  const isMultiProp = Array.isArray(prop)
  const map:Map<number|string, T> = new Map();
  arr.forEach((r:any) => {
    let val = (reduceToProp) ? r[reduceToProp] : r;
    let key;
    if (isMultiProp){
      const keyParts = (<string[]>prop).map(propIter => r[propIter])
      key = keyParts.join(keyJoinChar || ';');
    }
    else{
     key = r[prop];
     if (!isKeyNonNumeric){ key = +key; }
    }
    map.set(key, val)
  });
  return map;
};


export const arrToStringMap = <T>(arr:T[], prop:string) => {
  const map:Map<string, T> = new Map();
  arr.forEach(r => map.set(''+(<any>r)[prop], r));
  return map;
};

export const numAsBool = (value:number) : boolean => ((''+<any>value) === '1')
export const boolAsNum = (value:boolean) : number => value ? 1 : 0;

export const objToArr = (obj:any, propName='key') => {
  return Object.entries(obj).map((pair:any) => {
    const [key, val] = pair
    return {
      ... val,
      [propName]: key,
    }
  })
}