import { Application, Paginated } from "@feathersjs/feathers";
import { Errors } from "../errors/general";

export const getSysConstMdTranslation = async (app:Application, FLAG_KEY:string) => {
    const records = <Paginated<any>> await app
      .service('db/read/sys-constants-md-translations')
      .find({query: {key: FLAG_KEY} })
    if (!records.data[0]){
      throw new Errors.BadGateway('INVALID_CONSTANT');
    }
    return {
      en: records.data[0].value_en,
      fr: records.data[0].value_fr
    }
}