import { Application, Paginated } from "@feathersjs/feathers";
import { Errors } from "../errors/general";

export const getSysConstNumeric = async (app:Application, FLAG_KEY:string, getValue:boolean=false) => {
    const records = <Paginated<any>> await app
      .service('db/read/sys-constants-numeric')
      .find({query: {key: FLAG_KEY} })
    if (!records.data[0]){
      throw new Errors.BadGateway('INVALID_CONSTANT');
    }
    if (getValue){
      return records.data[0].value;
    }
    else{
      return (records.data[0].value === 1)
    }
}