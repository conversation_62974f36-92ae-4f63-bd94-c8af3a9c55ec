import { Application, Paginated } from "@feathersjs/feathers";
import { Errors } from "../errors/general";

export const getSysConstString = async (app:Application, FLAG_KEY:string) => {
    const records = <Paginated<any>> await app
      .service('db/read/sys-constants-string')
      .find({query: {key: FLAG_KEY} })
    if (!records.data[0]){
      throw new Errors.BadGateway('INVALID_CONSTANT');
    }
    return records.data[0].value;
}