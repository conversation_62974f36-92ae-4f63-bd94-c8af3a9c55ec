import { Application } from "../declarations";
import { Params } from "@feathersjs/feathers";
import { JWTStrategy } from "@feathersjs/authentication/lib";
import { dbRawRead } from "./db-raw";
import { Errors } from "../errors/general";

export const currentUid = async (app:Application, params:Params) => {
  const authPayload = await app.defaultAuthentication().verifyAccessToken(params.authentication.accessToken);
  return <number> authPayload.uid;
  // console.log(authPayload)
  // const userInfo = await app
  //   .service('public/auth/user-info-core')
  //   .parseUserinfoJWT(params);
  // return <number> userInfo.uid;
}

export const ensureNewRole = async (app:Application, uid:number, group_id:number, role_type:string) => { 
  const user_roles = await dbRawRead(app, {uid, group_id, role_type}, `
    select ur.id
    from user_roles ur 
    where uid = :uid 
      and group_id = :group_id
      and is_revoked = 0 
      and role_type = :role_type
  ;`);
  if(user_roles.length > 0){
    throw new Errors.BadRequest("ACCOUNT_ALREADY_EXISTED");
  }
}

export const getExistingUidFromAuthOrInvite = async (app:Application, email:string) => { 
  //1. Email already exist in auth  or Email does not exist in auth but exist u_invite
  let authRecords: any[] = [] ;
  let inviteRecords: any = [];
  let existingUid

  if(email){
    authRecords  =  await dbRawRead(app, {email}, `
      SELECT a.uid
           , a.email
      FROM auths a
      where a.email = :email
    ;`);
  }

  if (authRecords.length > 0){
    existingUid = authRecords[0].uid
  }

  if(email && authRecords.length == 0){
    inviteRecords  =  await dbRawRead(app, {email}, `
      SELECT * 
      FROM u_invites ui
      where ui.invit_email = :email
        and is_revoked = 0 
        and used_by_uid is null;
    ;`);
  }

  if(inviteRecords.length > 0){
    existingUid = inviteRecords[0].uid
  }

  return {existingUid, authRecords, inviteRecords};
}

export const fetchGroupUsersContextInfo = async(app: Application, group_id:any, role_type:string, uid?:number, ) => {
  const userExtRecords = await dbRawRead(app, {group_id, uid, role_type}, `
     select u.id as uid
          , concat(u.first_name," ",u.last_name) as name
          , u.first_name as firstName
          , u.last_name as lastName
          , u.contact_email as email
          , (case 
              when au.id is not null 
              then 1
              else 0
            end) isConfirmed
          , ui.invit_email as invit_email
          , ui.id as invit_id
          , ui.secret_key as secret_key
          , ui.expire_on as expire_on
          , ur.expires_on as endTime
          , ui.created_on as created_on
       from user_roles ur
       join users u on u.id = ur.uid
  left join u_invites ui on ui.uid = u.id and ui.is_revoked != 1
  left join auths au on au.uid = u.id
      where ur.is_revoked = 0
        and ur.group_id = :group_id
        and ur.role_type = :role_type
        ${ uid ? 'and ur.uid = :uid' : ''}
   group by ur.uid   
  ;`);
  return userExtRecords;
}

export const listMergeUserInfo = async (app:Application, arr:any[], uidProp:string = 'uid', userInfoProp:string = 'user_info' ) => {
  const uids = arr.map((entry: any) => entry[uidProp]);
  const users = <any[]> await app
    .service('db/read/users')
    .db()
    .whereIn('id', uids);
  const userRef = new Map();
  users.forEach(user => {
    userRef.set(user.id, {
      last_name: user.last_name,
      first_name: user.first_name,
      contact_email: user.contact_email,
    })
  })
  arr.forEach(entry => {
    const uid = entry[uidProp];
    entry[userInfoProp] = userRef.get(uid);
  })
}
