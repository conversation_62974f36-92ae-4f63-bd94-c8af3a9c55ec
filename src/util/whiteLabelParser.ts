export enum FLAGS 
{
  ABED = "ABED",
  NBED = "NBED",
  EQAO = "EQAO",
  BCED = "BCED",
  MBED = "MBED"
}

// note: for all of the below functions,
// whiteLabelSlug should come from the web-client, from whiteLabelService.getWhitelabelFlag()
export const isABED = (whiteLabelSlug: FLAGS | null): boolean => 
{
    return whiteLabelSlug === FLAGS.ABED;
}

export const isNBED = (whiteLabelSlug: FLAGS | null): boolean => 
{
    return whiteLabelSlug === FLAGS.NBED;
}

export const isEQAO = (whiteLabelSlug: FLAGS | null): boolean => 
{
    return whiteLabelSlug === FLAGS.EQAO;
}

export const isBCED = (whiteLabelSlug: FLAGS | null): boolean => 
{
    return whiteLabelSlug === FLAGS.BCED;
}

export const isMBED = (whiteLabelSlug: FLAGS | null): boolean => 
{
    return whiteLabelSlug === FLAGS.MBED;
}




