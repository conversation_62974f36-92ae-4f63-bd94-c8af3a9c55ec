import assert from 'assert';
import fs from 'fs';

import { aggregate } from '../../../../../../src/services/public/data-exporter/data-export-queries/transforms/aggregate'

describe('data-exporter | data-export-queries | transforms :: aggregate', () => {
  // Empty DataFrame {{{
  it('works for empty input DataFrame', () => {
    const df_input = [];
    const expected = []; 

    const agg = [
      { col_new: "total_score", agg_type: "sum", col_target: "score" }
    ];

    let result = aggregate(df_input, agg);
    assert.deepStrictEqual(result, expected, "Works when input DataFrame is empty");
  });
  // }}}

  // Aggregations {{{
  it('works -- sum aggregation on one column', () => {
    const df_input = [
      { uid: 1, taqr_id: 1, score: 10 },
      { uid: 1, taqr_id: 2, score: 20 },
      { uid: 1, taqr_id: 3, score: 30 }
    ];

    const expected = [
      { total_score: 60 }
    ];

    const agg = [
      { col_new: "total_score", agg_type: "sum", col_target: "score" }
    ];

    let result = aggregate(df_input, agg);
    assert.deepStrictEqual(result, expected, "Works when calculating sum on a single column");
  });
  it('works -- multiple aggregations on one column', () => {
    const df_input = [
      { uid: 1, taqr_id: 1, score: 1 },
      { uid: 1, taqr_id: 2, score: 2 },
      { uid: 1, taqr_id: 3, score: 3 }
    ];

    const expected = [
      { total_score: 6, average_score: 2 }
    ];

    const agg = [
      { col_new: "total_score", agg_type: "sum", col_target: "score" },
      { col_new: "average_score", agg_type: "mean", col_target: "score" }
    ];

    let result = aggregate(df_input, agg);
    assert.deepStrictEqual(result, expected, "Works when performing multiple aggregations on a single column");
  });
  it('works -- aggregations with different target columns', () => {
    const df_input = [
      { uid: 1, taqr_id: 1, score: 1 },
      { uid: 1, taqr_id: 2, score: 2 },
      { uid: 1, taqr_id: 3, score: 3 }
    ];

    const expected = [
      { total_score: 6, unique_taqrs: 3 }
    ];

    const agg = [
      { col_new: "total_score", agg_type: "sum", col_target: "score" },
      { col_new: "unique_taqrs", agg_type: "nunique", col_target: "taqr_id" }
    ];

    let result = aggregate(df_input, agg);
    assert.deepStrictEqual(result, expected, "Works when performing aggregations on multiple target columns");
  });
  // }}}
});