import assert from 'assert';

import { concatDataFrames } from '../../../../../../src/services/public/data-exporter/data-export-queries/transforms/concat'

describe('data-exporter | data-export-queries | transforms :: concat', () => {
  // Empty DataFrame or argument {{{
  it('works when df_inputs is empty', () => {
    const df_inputs = [];

    const expected = [];

    let result = concatDataFrames(df_inputs)
    assert.deepStrictEqual(result, expected, "Works when input dataframe is empty")
  });

  it('works with a single dataframe', () => {
    const df_input = [
      { item_id: 1, score: 1, formatted_response: 'A', weight: 1 },
      { item_id: 2, score: 0, formatted_response: 'A', weight: 1 },
      { item_id: 3, score: 0, formatted_response: 'B', weight: 1 },
      { item_id: 4, score: 1, formatted_response: 'A', weight: 1 },
    ];

    const expected = [
      { item_id: 1, score: 1, formatted_response: 'A', weight: 1 },
      { item_id: 2, score: 0, formatted_response: 'A', weight: 1 },
      { item_id: 3, score: 0, formatted_response: 'B', weight: 1 },
      { item_id: 4, score: 1, formatted_response: 'A', weight: 1 },
    ];

    let result = concatDataFrames([df_input])
    assert.deepStrictEqual(result, expected, "Works with a single dataframe")
  });

  it('works with multiple dataframes', () => {
    const df_input1 = [
      { item_id: 1, score: 1, formatted_response: 'A', weight: 1 },
      { item_id: 2, score: 0, formatted_response: 'A', weight: 1 },
      { item_id: 3, score: 0, formatted_response: 'B', weight: 1 },
      { item_id: 4, score: 1, formatted_response: 'A', weight: 1 },
    ];
    const df_input2 = [
      { item_id: 5, score: 1, formatted_response: 'A', weight: 1 },
      { item_id: 6, score: 0, formatted_response: 'A', weight: 1 },
      { item_id: 7, score: 0, formatted_response: 'B', weight: 1 },
      { item_id: 8, score: 1, formatted_response: 'A', weight: 1 },
    ];

    const expected = [
      { item_id: 1, score: 1, formatted_response: 'A', weight: 1 },
      { item_id: 2, score: 0, formatted_response: 'A', weight: 1 },
      { item_id: 3, score: 0, formatted_response: 'B', weight: 1 },
      { item_id: 4, score: 1, formatted_response: 'A', weight: 1 },
      { item_id: 5, score: 1, formatted_response: 'A', weight: 1 },
      { item_id: 6, score: 0, formatted_response: 'A', weight: 1 },
      { item_id: 7, score: 0, formatted_response: 'B', weight: 1 },
      { item_id: 8, score: 1, formatted_response: 'A', weight: 1 },
    ];

    let result = concatDataFrames([df_input1, df_input2])
    assert.deepStrictEqual(result, expected, "Works with multiple dataframes")
    });

});
