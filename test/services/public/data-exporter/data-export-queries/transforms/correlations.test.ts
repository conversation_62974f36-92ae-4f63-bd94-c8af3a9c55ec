import assert from 'assert';
import fs from 'fs';

import { pointBiserial } from '../../../../../../src/services/public/data-exporter/data-export-queries/transforms/correlations'

function approxEqual(a: number, b: number, msg?: string) {
  const diff = Math.abs(a - b);
  if (diff > 1e-10) {
    throw new assert.AssertionError({ actual: a, expected: b, message: msg, operator: 'approxEqual' });
  }
}

function containsSubset(parent: string[], subset: string[]) {
  let set = new Set(parent);
  for (let s of subset) {
    if (!set.has(s)) {
      return false;
    }
  }
  return true;
}

describe('data-exporter | data-export-queries | transforms :: correlations (rpb, crpb)', () => {
  it('works on empty input', () => {
    const responses = []
    const result = pointBiserial(responses, undefined);

    assert.strictEqual(result.length, 0, "Resulting table is empty");
  });

  it('gives null values if not enough data', () => {
    const responses = [
      { asmt_code: "test", form_code: 1, test_design_id: 100, item_id: 5, is_correct: 1, total_score: 50, score_max: 1 },
    ];
    let expected = [
      { asmt_code: "test", form_code: 1, test_design_id: 100, item_id: 5, rpb: null, crpb: null, p: 1, iri: null },
    ]
    let result = pointBiserial(responses, undefined);
    assert.deepStrictEqual(result, expected, "values should be null if 1 record")

    responses.push(
      { asmt_code: "test", form_code: 1, test_design_id: 100, item_id: 5, is_correct: 0, total_score: 45, score_max: 1 },
    )
    // Known value for this simple case (fully correlated)
    const expected2 = [
      { asmt_code: "test", form_code: 1, test_design_id: 100, item_id: 5, rpb: 1, crpb: 1, p: 0.5, iri: 0.5 },
    ]

    result = pointBiserial(responses, undefined);
    assert.notStrictEqual(result[0].rpb, null, "values should not be null if 2 records")
    assert.notStrictEqual(result[0].crpb, null, "values should not be null if 2 records")
    assert.deepStrictEqual(result, expected2, "values should match the known output for 2 records")
  });

  it('defaults to keeping asmt_code, form_code, test_design_id', () => {
    const responses = [
      { asmt_code: "test", form_code: 1, test_design_id: 100, item_id: 5, is_correct: 1, total_score: 50, score_max: 1 },
    ];
    let expected = [
      { asmt_code: "test", form_code: 1, test_design_id: 100, item_id: 5, rpb: null, crpb: null, p: 1, iri: null },
    ]
    let result = pointBiserial(responses, undefined);
    assert.deepStrictEqual(result, expected, "values should be null if 1 record")

  });

  it('retains different columns based on keep_col argument', () => {
    const responses = [
      { asmt_code: "test", form_code: 1, test_design_id: 100, item_id: 5, is_correct: 1, total_score: 50, score_max: 1 },
    ];

    let keep_cols = ['asmt_code', 'form_code', 'test_design_id'];
    let result = pointBiserial(responses, keep_cols);
    assert(containsSubset(Object.keys(result[0]), keep_cols), "should keep same as default")

    keep_cols = ['asmt_code', 'form_code'];
    result = pointBiserial(responses, keep_cols);
    assert(containsSubset(Object.keys(result[0]), keep_cols), "should keep asmt_code, form_code")

    keep_cols = ['form_code'];
    result = pointBiserial(responses, keep_cols);
    assert(containsSubset(Object.keys(result[0]), keep_cols), "should keep just form code")

    keep_cols = [];
    result = pointBiserial(responses, keep_cols);
    assert(containsSubset(['item_id', 'rpb', 'crpb', 'iri', 'p'], Object.keys(result[0])), "should only have item_id, rpb, crpb")

    responses.push(
      { asmt_code: "test", form_code: 1, test_design_id: 100, item_id: 5, is_correct: 0, total_score: 45, score_max: 1 },
    )
  });

  it('gives one record per item', () => {
    const responses = [
      { asmt_code: "test", form_code: 1, test_design_id: 100, item_id: 5, is_correct: 1, total_score: 50, score_max: 1 },
      { asmt_code: "test", form_code: 1, test_design_id: 100, item_id: 6, is_correct: 1, total_score: 50, score_max: 1 },
      { asmt_code: "test", form_code: 1, test_design_id: 100, item_id: 5, is_correct: 0, total_score: 30, score_max: 1 },
      { asmt_code: "test", form_code: 1, test_design_id: 100, item_id: 5, is_correct: 1, total_score: 50, score_max: 1 },
      { asmt_code: "test", form_code: 1, test_design_id: 100, item_id: 7, is_correct: 1, total_score: 50, score_max: 1 },
      { asmt_code: "test", form_code: 1, test_design_id: 100, item_id: 7, is_correct: 1, total_score: 50, score_max: 1 },
    ];
    let keep_cols = ['asmt_code', 'form_code', 'test_design_id'];
    let result = pointBiserial(responses, keep_cols);
    assert.strictEqual(result.length, 3, "Should have 3 records")
    let items_out = [... new Set(result.map(r => r.item_id))]
    items_out.sort()
    assert.deepStrictEqual(items_out, [5, 6, 7], "Should have exactly these 3 items")
  });




  // TODO: generate more test data with python, and use it here
  it.skip("Matches output of scypy.stats.pointbiserialr (via data files)", () => {
    const responses = JSON.parse(fs.readFileSync('test-data/TODO.json', 'utf8'))

    let result = pointBiserial(responses, undefined);
    // TODO: compare results
    assert(result)
  });

  it('works on synthetic data', () => {
    const responses = [
        // Completely random
        { td_id: 1234, item_id: 456, is_correct: 0, total_score:  7, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score: 30, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score:  0, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 0, total_score: 21, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 0, total_score: 49, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score: 34, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score: 30, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score:  6, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score:  9, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score: 41, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score: 26, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score: 16, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 0, total_score: 13, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 0, total_score:  5, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score: 23, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score: 40, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score: 43, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score: 42, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score: 28, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score: 34, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score: 24, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 0, total_score: 25, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score: 36, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 0, total_score: 23, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 0, total_score: 46, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 0, total_score: 41, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score:  3, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 0, total_score: 32, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score: 34, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 0, total_score: 43, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score: 14, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score: 48, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score: 49, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score: 49, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 0, total_score: 12, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score: 15, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score:  7, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 0, total_score: 11, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 0, total_score:  5, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 0, total_score: 41, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 0, total_score:  8, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score: 34, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 0, total_score:  6, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score: 20, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score: 15, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score: 14, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 1, total_score:  0, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 0, total_score: 15, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 0, total_score: 35, score_max: 2 },
        { td_id: 1234, item_id: 456, is_correct: 0, total_score: 47, score_max: 2 },

        // Strongly correlated: all passing got it correct
        { td_id: 1234, item_id: 457, is_correct: 0, total_score:  7, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 1, total_score: 30, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 0, total_score:  0, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 0, total_score: 21, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 1, total_score: 49, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 1, total_score: 34, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 1, total_score: 30, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 0, total_score:  6, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 0, total_score:  9, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 1, total_score: 41, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 1, total_score: 26, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 0, total_score: 16, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 0, total_score: 13, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 0, total_score:  5, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 0, total_score: 23, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 1, total_score: 40, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 1, total_score: 43, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 1, total_score: 42, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 1, total_score: 28, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 1, total_score: 34, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 0, total_score: 24, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 0, total_score: 25, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 1, total_score: 36, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 0, total_score: 23, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 1, total_score: 46, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 1, total_score: 41, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 0, total_score:  3, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 1, total_score: 32, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 1, total_score: 34, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 1, total_score: 43, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 0, total_score: 14, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 1, total_score: 48, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 1, total_score: 49, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 1, total_score: 49, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 0, total_score: 12, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 0, total_score: 15, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 0, total_score:  7, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 0, total_score: 11, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 0, total_score:  5, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 1, total_score: 41, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 0, total_score:  8, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 1, total_score: 34, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 0, total_score:  6, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 0, total_score: 20, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 0, total_score: 15, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 0, total_score: 14, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 0, total_score:  0, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 0, total_score: 15, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 1, total_score: 35, score_max: 1 },
        { td_id: 1234, item_id: 457, is_correct: 1, total_score: 47, score_max: 1 },

        // Strongly anti-correlated: all passing got it wrong
        { td_id: 1234, item_id: 458, is_correct: 1, total_score:  7, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 0, total_score: 30, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 1, total_score:  0, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 1, total_score: 21, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 0, total_score: 49, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 0, total_score: 34, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 0, total_score: 30, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 1, total_score:  6, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 1, total_score:  9, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 0, total_score: 41, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 0, total_score: 26, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 1, total_score: 16, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 1, total_score: 13, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 1, total_score:  5, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 1, total_score: 23, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 0, total_score: 40, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 0, total_score: 43, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 0, total_score: 42, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 0, total_score: 28, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 0, total_score: 34, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 1, total_score: 24, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 0, total_score: 25, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 0, total_score: 36, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 1, total_score: 23, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 0, total_score: 46, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 0, total_score: 41, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 1, total_score:  3, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 0, total_score: 32, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 0, total_score: 34, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 0, total_score: 43, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 1, total_score: 14, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 0, total_score: 48, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 0, total_score: 49, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 0, total_score: 49, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 1, total_score: 12, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 1, total_score: 15, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 1, total_score:  7, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 1, total_score: 11, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 1, total_score:  5, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 0, total_score: 41, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 1, total_score:  8, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 0, total_score: 34, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 1, total_score:  6, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 1, total_score: 20, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 1, total_score: 15, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 1, total_score: 14, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 1, total_score:  0, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 1, total_score: 15, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 0, total_score: 35, score_max: 3 },
        { td_id: 1234, item_id: 458, is_correct: 0, total_score: 47, score_max: 3 },
    ]

    const expected = [
        { td_id: 1234, item_id: 456, rpb: 0.039479108738555924, crpb: -0.02542966152762919 },
        { td_id: 1234, item_id: 457, rpb: 0.8815832704944022, crpb: 0.8738755340354976 },
        { td_id: 1234, item_id: 458, rpb: -0.8809307098588236, crpb: -0.9005499046041582 },
    ]

    let keep_cols = ['td_id'];
    let result = pointBiserial(responses, keep_cols);
    result.sort((a, b) => <number> a.item_id - <number> b.item_id);

    approxEqual(<number> result[0].rpb, <number> expected[0].rpb, "rpb not correct for first item");
    approxEqual(<number> result[1].rpb, <number> expected[1].rpb, "rpb not correct for first item");
    approxEqual(<number> result[2].rpb, <number> expected[2].rpb, "rpb not correct for first item");
    approxEqual(<number> result[0].crpb, <number> expected[0].crpb, "crpb not correct for first item");
    approxEqual(<number> result[1].crpb, <number> expected[1].crpb, "crpb not correct for first item");
    approxEqual(<number> result[2].crpb, <number> expected[2].crpb, "crpb not correct for first item");

    // does not respect floating point errors
    // assert.deepStrictEqual(result, expected, "should calculate correct values from synthetic test data");
  });

});
