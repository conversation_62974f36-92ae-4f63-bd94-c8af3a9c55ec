import assert from 'assert';

import { cutoffs } from '../../../../../../src/services/public/data-exporter/data-export-queries/transforms/cutoffs'

describe('data-exporter | data-export-queries | transforms :: cutoffs', () => {
  it('works for empty input Dataframe', () => {
    const df_input = [];
    const expected = [];

    // Input args
    const group_by = ["test_design_id"]
    const cutoffDefs = [
      { label: "low", percentile: 27, inclusive: true },
      { label: "mid", percentile: 73, inclusive: false },
      { label: "high", percentile: 100, inclusive: true }
    ];

    let result = cutoffs(df_input, group_by, "score", cutoffDefs)
    assert.deepStrictEqual(result, expected, "Works when input dataframe is empty");
  });

  // Works while calculating 3 label cutoffs {{{
  it('works for 3 label cutoffs', () => {
    const df_input = [
      { test_design_id: 1, score: 50 },
      { test_design_id: 1, score: 70 },
      { test_design_id: 1, score: 80 },
      { test_design_id: 1, score: 90 },
      { test_design_id: 2, score: 55 },
      { test_design_id: 2, score: 65 },
      { test_design_id: 2, score: 75 },
    ];

    const expected = [
      { test_design_id: 1, cutoff: 70, label: "low", inclusive: true },
      { test_design_id: 1, cutoff: 80, label: "mid", inclusive: false },
      { test_design_id: 1, cutoff: 90, label: "high", inclusive: true },
      { test_design_id: 2, cutoff: 55, label: "low", inclusive: true },
      { test_design_id: 2, cutoff: 75, label: "mid", inclusive: false },
      { test_design_id: 2, cutoff: 75, label: "high", inclusive: true },
    ];

    // Input args
    const group_by = ["test_design_id"]
    const cutoffDefs = [
      { label: "low", percentile: 27, inclusive: true },
      { label: "mid", percentile: 73, inclusive: false },
      { label: "high", percentile: 100, inclusive: true }
    ];

    let result = cutoffs(df_input, group_by, "score", cutoffDefs)
    assert.deepStrictEqual(result, expected, "Applies 3 labelled cutoffs");
  });
  // }}}
});


describe('data-exporter | data-export-queries | transforms :: cutoffs :: applied', () => {
  it('handles inclusive/exclusive bounds (27% high/low)', () => {
    //  200 random scores between 0 and 25
    const input = [ 0,  0,  0,  0,  1,  1,  1,  1,  1,  2,  2,  2,  2,  2,  2,  2,  3,
        3,  3,  3,  3,  3,  3,  3,  3,  3,  4,  4,  4,  4,  4,  4,  4,  4,
        4,  5,  5,  5,  5,  5,  5,  6,  6,  6,  6,  6,  6,  7,  7,  7,  7,
        7,  7,  7,  7,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,
        8,  8,  9,  9,  9,  9,  9, 10, 10, 10, 10, 10, 10, 10, 10, 10, 11,
       11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 12, 12, 12, 12,
       12, 12, 12, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13,
       13, 14, 14, 14, 14, 14, 14, 14, 15, 15, 15, 15, 16, 16, 16, 16, 16,
       16, 16, 16, 17, 17, 17, 17, 17, 17, 18, 18, 18, 18, 18, 18, 18, 19,
       19, 19, 20, 20, 20, 21, 21, 21, 21, 21, 21, 22, 22, 22, 22, 22, 22,
       22, 22, 22, 22, 22, 23, 23, 23, 23, 23, 23, 23, 23, 24, 24, 24, 24,
       24, 24, 24, 24, 24, 25, 25, 25, 25, 25, 25, 25, 25];

    const df_input = input.map((score) => ({ test_design_id: 5, score: score }));

    // Input args
    const group_by = ["test_design_id"]
    // NOTE: these are the ABED cutoffs where the high should have at least 27%
    const p = 27;
    const cutoffDefs = [
      { label: "low", percentile: p, inclusive: true },
      { label: "mid", percentile: 100-p, inclusive: false },
      { label: "high", percentile: 100, inclusive: true }
    ];

    let cpoints = cutoffs(df_input, group_by, "score", cutoffDefs)
    let gcounts = {low: 0, mid: 0, high: 0, total: 0};

    for (let record of df_input) {
      gcounts.total++;
      for (let cutoff of cpoints) {
        if (!cutoff.inclusive && record.score < cutoff.cutoff) {
          gcounts[cutoff.label]++;
          break;
        }
        else if (cutoff.inclusive && record.score <= cutoff.cutoff) {
          gcounts[cutoff.label]++;
          break;
        }
      }
    }
    assert(gcounts.low / gcounts.total >= 27 / 100, "low cutoff value should give at least 27% of the data");
    assert(gcounts.high / gcounts.total >= 27 / 100, "high cutoff value should give at least 27% of the data");
  });

  it('applies inclusive/exclusive bounds (all possible)', () => {
    //  200 random scores between 0 and 25
    const input = [ 0,  0,  0,  0,  1,  1,  1,  1,  1,  2,  2,  2,  2,  2,  2,  2,  3,
        3,  3,  3,  3,  3,  3,  3,  3,  3,  4,  4,  4,  4,  4,  4,  4,  4,
        4,  5,  5,  5,  5,  5,  5,  6,  6,  6,  6,  6,  6,  7,  7,  7,  7,
        7,  7,  7,  7,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,  8,
        8,  8,  9,  9,  9,  9,  9, 10, 10, 10, 10, 10, 10, 10, 10, 10, 11,
       11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 12, 12, 12, 12,
       12, 12, 12, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13,
       13, 14, 14, 14, 14, 14, 14, 14, 15, 15, 15, 15, 16, 16, 16, 16, 16,
       16, 16, 16, 17, 17, 17, 17, 17, 17, 18, 18, 18, 18, 18, 18, 18, 19,
       19, 19, 20, 20, 20, 21, 21, 21, 21, 21, 21, 22, 22, 22, 22, 22, 22,
       22, 22, 22, 22, 22, 23, 23, 23, 23, 23, 23, 23, 23, 24, 24, 24, 24,
       24, 24, 24, 24, 24, 25, 25, 25, 25, 25, 25, 25, 25];

    const df_input = input.map((score) => ({ test_design_id: 5, score: score }));

    // Input args
    const group_by = ["test_design_id"]

    for (let p = 1; p <= 45; p++) {
      const cutoffDefs = [
        { label: "low", percentile: p, inclusive: true },
        { label: "mid", percentile: 100-p, inclusive: false },
        { label: "high", percentile: 100, inclusive: true }
      ];

      let cpoints = cutoffs(df_input, group_by, "score", cutoffDefs)
      let gcounts = {low: 0, mid: 0, high: 0, total: 0};

      for (let record of df_input) {
        gcounts.total++;
        for (let cutoff of cpoints) {
          if (!cutoff.inclusive && record.score < cutoff.cutoff) {
            gcounts[cutoff.label]++;
            break;
          }
          else if (cutoff.inclusive && record.score <= cutoff.cutoff) {
            gcounts[cutoff.label]++;
            break;
          }
        }
      }
      assert(gcounts.low / gcounts.total >= p / 100, `low cutoff value should give at least ${p}% of the data: ${gcounts}`);
      assert(gcounts.high / gcounts.total >= p / 100, `high cutoff value should give at least ${p}% of the data: ${gcounts}`);
    }
  });

});
