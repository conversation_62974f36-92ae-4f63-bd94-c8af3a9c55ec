
import assert from 'assert';

import { drop_duplicates } from '../../../../../../src/services/public/data-exporter/data-export-queries/transforms/drop-duplicates'

describe('data-exporter | data-export-queries | transforms :: drop_duplicates', () => {
  // Empty DataFrame {{{
  it('works -- input dataframe is empty', () => {
    const df_input = [];

    const expected = [];

    const subset = ["item_id", "type_slug"];

    let result = drop_duplicates(df_input, subset);
    assert.deepStrictEqual(result, expected, "Works when input dataframe is empty");
  });
  // }}}

  // No Duplicates {{{
  it('works -- no duplicates in the input', () => {
    const df_input = [
      { item_id: 1, type_slug: 'SLUG_A', score: 1 },
      { item_id: 2, type_slug: 'SLUG_B', score: 0 },
      { item_id: 3, type_slug: 'SLUG_C', score: 1 }
    ];

    const expected = [
      { item_id: 1, type_slug: 'SLUG_A', score: 1 },
      { item_id: 2, type_slug: 'SLUG_B', score: 0 },
      { item_id: 3, type_slug: 'SLUG_C', score: 1 }
    ];

    const subset = ["item_id", "type_slug"];

    let result = drop_duplicates(df_input, subset);
    assert.deepStrictEqual(result, expected, "Works with no duplicates in the input");
  });

  it('works -- single row in input', () => {
    const df_input = [
      { item_id: 1, type_slug: 'A', score: 1 }
    ];

    const expected = [
      { item_id: 1, type_slug: 'A', score: 1 }
    ];

    const subset = ["item_id", "type_slug"];

    let result = drop_duplicates(df_input, subset);
    assert.deepStrictEqual(result, expected, "Works when input dataframe contains a single row");
  });
  // }}}

  // Dropping duplicates {{{
  it('works when there are duplicates in the input', () => {
    const df_input = [
      { item_id: 1, type_slug: 'SLUG_A', score: 1 },
      { item_id: 1, type_slug: 'SLUG_A', score: 0 },
      { item_id: 2, type_slug: 'SLUG_B', score: 0 },
      { item_id: 2, type_slug: 'SLUG_B', score: 1 },
      { item_id: 3, type_slug: 'SLUG_C', score: 1 }
    ];

    const expected = [
      { item_id: 1, type_slug: 'SLUG_A', score: 1 },
      { item_id: 2, type_slug: 'SLUG_B', score: 0 },
      { item_id: 3, type_slug: 'SLUG_C', score: 1 }
    ];

    const subset = ["item_id", "type_slug"];

    let result = drop_duplicates(df_input, subset);
    assert.deepStrictEqual(result, expected, "Works with duplicates in the input");
  });

  it('works -- all duplicate rows', () => {
    const df_input = [
      { item_id: 1, type_slug: 'SLUG_A', score: 1 },
      { item_id: 1, type_slug: 'SLUG_A', score: 1 },
      { item_id: 1, type_slug: 'SLUG_A', score: 1 }
    ];

    const expected = [
      { item_id: 1, type_slug: 'SLUG_A', score: 1 }
    ];

    const subset = ["item_id", "type_slug"];

    let result = drop_duplicates(df_input, subset);
    assert.deepStrictEqual(result, expected, "Works when all rows are duplicates");
  });

  it('works -- no column specified in the subset param', () => {
    const df_input = [
      { item_id: 1, type_slug: 'SLUG_A', score: 1 },
      { item_id: 1, type_slug: 'SLUG_A', score: 0 },
      { item_id: 2, type_slug: 'SLUG_B', score: 1 },
      { item_id: 2, type_slug: 'SLUG_B', score: 1 },
      { item_id: 3, type_slug: 'SLUG_C', score: 1 }
    ];

    const expected = [
      { item_id: 1, type_slug: 'SLUG_A', score: 1 },
      { item_id: 1, type_slug: 'SLUG_A', score: 0 },
      { item_id: 2, type_slug: 'SLUG_B', score: 1 },
      { item_id: 3, type_slug: 'SLUG_C', score: 1 }
    ];

    const subset = [];

    let result = drop_duplicates(df_input, subset);
    assert.deepStrictEqual(result, expected, "Works with no column specified in the subset param");
  });
  // }}}
});
