import assert from 'assert';

import { fillNa<PERSON>ulti, fillNaMultiInplace } from '../../../../../../src/services/public/data-exporter/data-export-queries/transforms/fill-na'

describe('data-exporter | data-export-queries | transforms :: fill-na', () => {
  // Empty DataFrame or argument {{{
  it('works when df_inputs is empty', () => {
    const df_inputs = [];

    const expected = [];

    let result = fillNaMulti(df_inputs, {})
    assert.deepStrictEqual(result, expected, "Works when input dataframe is empty")
  });

  it('fills null values, leaving input dataframe unchanged', () => {
    const df_input = [
      { item_id: 1, score: 1, formatted_response: 'A', weight: 1 },
      { item_id: 2, score: null, formatted_response: 'A', weight: 1 },
      { item_id: 3, score: 0, formatted_response: null, weight: 1 },
      { item_id: 4, score: null, formatted_response: 'A', weight: 1 },
    ];
    const unchanged = structuredClone(df_input);

    const expected = [
      { item_id: 1, score: 1, formatted_response: 'A', weight: 1 },
      { item_id: 2, score: 0, formatted_response: 'A', weight: 1 },
      { item_id: 3, score: 0, formatted_response: '', weight: 1 },
      { item_id: 4, score: 0, formatted_response: 'A', weight: 1 },
    ];

    let result = fillNaMulti(df_input, {"score": 0, "formatted_response": ""})
    assert.deepStrictEqual(result, expected, "fills na values");
    assert.deepStrictEqual(unchanged, df_input, "Input dataframe unchanged");
  });

  // TODO: more unit tests, like if columns are missing

});

describe('data-exporter | data-export-queries | transforms :: fill-na inplace', () => {
  // Empty DataFrame or argument {{{
  it('works when df_inputs is empty', () => {
    const df_inputs = [];

    const expected = [];

    let result = fillNaMultiInplace(df_inputs, {})
    assert.deepStrictEqual(result, expected, "Works when input dataframe is empty")
  });

  it('fills null values, leaving input dataframe unchanged', () => {
    const df_input = [
      { item_id: 1, score: 1, formatted_response: 'A', weight: 1 },
      { item_id: 2, score: null, formatted_response: 'A', weight: 1 },
      { item_id: 3, score: 0, formatted_response: null, weight: 1 },
      { item_id: 4, score: null, formatted_response: 'A', weight: 1 },
    ];
    const unchanged = structuredClone(df_input);

    const expected = [
      { item_id: 1, score: 1, formatted_response: 'A', weight: 1 },
      { item_id: 2, score: 0, formatted_response: 'A', weight: 1 },
      { item_id: 3, score: 0, formatted_response: '', weight: 1 },
      { item_id: 4, score: 0, formatted_response: 'A', weight: 1 },
    ];

    let result = fillNaMultiInplace(df_input, {"score": 0, "formatted_response": ""})
    assert.deepStrictEqual(result, expected, "fills na values");
    assert.deepStrictEqual(result, df_input, "Input dataframe changed in place");
    assert.notDeepStrictEqual(unchanged, df_input, "Input did actually change");
  });

  // TODO: more unit tests, like if columns are missing

});
