import assert from 'assert';
import fs from 'fs';

import { filter_col } from '../../../../../../src/services/public/data-exporter/data-export-queries/transforms/filter-col'

describe('data-exporter | data-export-queries | transforms :: filter_col', () => {
  // Empty DataFrame {{{
  it('works when input dataframe is empty', () => {
    const df_input = [];
 
    const expected = [];

    let result = filter_col(df_input, "score", "equals", 10)
    assert.deepStrictEqual(result, expected, "Works when input dataframe is empty")
  });
  // }}}

  // Equals and not-equal comparisons {{{
  it('works for comparison where column value equals argument', () => {
    const df_input = [
      { item_id: 1, score: 5 },
      { item_id: 2, score: 10 },
      { item_id: 3, score: 10 },
      { item_id: 4, score: 20 }
    ];
  
    const expected = [
      { item_id: 2, score: 10 },
      { item_id: 3, score: 10 }
    ]

    let result = filter_col(df_input, "score", "equals", 10)
    assert.deepStrictEqual(result, expected, "Works for filtering records where specified column value is greater than argument")
  });

  it('works -- comparison where column value equals argument; all columns in input have the same value', () => {
    const df_input = [
      { item_id: 1, score: 5 },
      { item_id: 2, score: 5 },
      { item_id: 3, score: 5 },
      { item_id: 4, score: 5 }
    ];
  
    const expected = [
      { item_id: 1, score: 5 },
      { item_id: 2, score: 5 },
      { item_id: 3, score: 5 },
      { item_id: 4, score: 5 }
    ]

    let result = filter_col(df_input, "score", "equals", 5)
    assert.deepStrictEqual(result, expected, "Works for filtering records where specified column value is greater than argument; all columns in input have the same value")
  });
  
  it('works for comparison where column value does not equal argument', () => {
    const df_input = [
      { item_id: 1, score: 5 },
      { item_id: 2, score: 10 },
      { item_id: 3, score: 10 },
      { item_id: 4, score: 20 }
    ];
  
    const expected = [
      { item_id: 1, score: 5 },
      { item_id: 4, score: 20 }
    ];
  
    let result = filter_col(df_input, "score", "not-equal", 10)
    assert.deepStrictEqual(result, expected, "Works for filtering records where specified column value is greater than argument")
  });
  // }}}
  
  // Greater and less than comparisons {{{
  it('works for comparison where column value is greater than argument', () => {
    const df_input = [
      { item_id: 1, score: 5 },
      { item_id: 2, score: 10 },
      { item_id: 3, score: 50 },
      { item_id: 4, score: 100 }
    ];
  
    const expected = [
      { item_id: 3, score: 50 },
      { item_id: 4, score: 100 }
    ];
  
    let result = filter_col(df_input, "score", "greater-than", 20)
    assert.deepStrictEqual(result, expected, "Works for filtering records where specified column value is greater than argument")
  });
  
  it('works for comparison where column value is less than argument', () => {
    const df_input = [
      { item_id: 1, score: 5 },
      { item_id: 2, score: 10 },
      { item_id: 3, score: 50 },
      { item_id: 4, score: 100 }
    ];
  
    const expected = [
      { item_id: 1, score: 5 },
      { item_id: 2, score: 10 }
    ];
  
    let result = filter_col(df_input, "score", "less-than", 20)
    assert.deepStrictEqual(result, expected, "Works for filtering records where specified column value is less than argument")
  });
  // }}}
    
  // Null and non-null comparisons {{{
  it('works for non-null comparison', () => {
    const df_input = [
      { item_id: 1, score: 5 },
      { item_id: 2, score: null },
      { item_id: 3, score: 50 },
      { item_id: 4, score: null }
    ];
  
    const expected = [
      { item_id: 1, score: 5 },
      { item_id: 3, score: 50 }
    ];
  
    let result = filter_col(df_input, "score", "non-null")
    assert.deepStrictEqual(result, expected, "Works for filtering records non-null records for specified column")
  });
  
  it('works for null comparison', () => {
    const df_input = [
      { item_id: 1, score: 5 },
      { item_id: 2, score: null },
      { item_id: 3, score: 50 },
      { item_id: 4, score: null }
    ];
  
    const expected = [
      { item_id: 2, score: null },
      { item_id: 4, score: null }
    ];
  
    let result = filter_col(df_input, "score", "null")
    assert.deepStrictEqual(result, expected, "Works for filtering records null records for specified column")
  });
  // }}}

  // Invalid Inputs {{{
  it('throws an error when specified column is not present in input', () => {
    // TODO: Confirm implementation - if we skip or throw an exception
    const df_input = [
      { item_id: 1, score: 5 },
      { item_id: 2, score: 10 },
      { item_id: 3, score: 50 },
      { item_id: 4, score: 100 }
    ];
  
    assert.throws(() => {
      filter_col(df_input, "weight", "equals", 3)
    }, Error, "Error thrown for column not found");
  });
  // }}}

  // TODO: Add test for column value in []
});