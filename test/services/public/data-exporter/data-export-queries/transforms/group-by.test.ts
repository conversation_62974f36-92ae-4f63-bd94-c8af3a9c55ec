import assert from 'assert';
import fs from 'fs';

import { group_by } from '../../../../../../src/services/public/data-exporter/data-export-queries/transforms/group-by'

describe('data-exporter | data-export-queries | transforms :: group_by', () => {
  // Empty DataFrame {{{
  it('works for empty input Dataframe', () => {
    const df_input = [];
    const expected = [];

    // Input args
    const group_by_cols = ["school_class_id", "type_slug"]
    const agg = [
      { col_new: "average_score", agg_type: "mean", col_target: "score" }
    ];
    
    let result = group_by(df_input, group_by_cols, agg)
    assert.deepStrictEqual(result, expected, "Works when input dataframe is empty");
  });
  // }}}

  // Aggregations {{{
  it('works -- grouped by 1 column, aggregating mean', () => {
    const df_input = [
      { taqr_id: 1, item_id: 1, score: 1, formatted_response: 'A', formatted_response_strict: null },
      { taqr_id: 2, item_id: 1, score: 1, formatted_response: 'B', formatted_response_strict: null },
      { taqr_id: 3, item_id: 1, score: 1, formatted_response: 'C', formatted_response_strict: null },
      { taqr_id: 1, item_id: 2, score: 1, formatted_response: 'A', formatted_response_strict: null },
      { taqr_id: 2, item_id: 2, score: 0, formatted_response: 'B', formatted_response_strict: null }
    ];

    const expected = [
      { item_id: 1, average_score: 1 },
      { item_id: 2, average_score: 0.5 }
    ];

    // Input args
    const group_by_cols = ["item_id"]
    const agg = [
      { col_new: "average_score", agg_type: "mean", col_target: "score" }
    ];
    
    let result = group_by(df_input, group_by_cols, agg)
    assert.deepStrictEqual(result, expected, "Works when calculating mean while grouping by 1 column");
  });

  it('works -- grouped by 2 columns, aggregating count', () => {
    const df_input = [
      { taqr_id: 1, item_id: 1, score: 1, formatted_response: 'A', formatted_response_strict: null },
      { taqr_id: 2, item_id: 1, score: 1, formatted_response: 'A', formatted_response_strict: null },
      { taqr_id: 3, item_id: 1, score: 1, formatted_response: 'C', formatted_response_strict: null },
      { taqr_id: 1, item_id: 2, score: 1, formatted_response: 'A', formatted_response_strict: null },
      { taqr_id: 2, item_id: 2, score: 0, formatted_response: 'B', formatted_response_strict: null }
    ];

    const expected = [
      { item_id: 1, formatted_response: 'A', occurences: 2 },
      { item_id: 1, formatted_response: 'C', occurences: 1 },
      { item_id: 2, formatted_response: 'A', occurences: 1 },
      { item_id: 2, formatted_response: 'B', occurences: 1 }
    ];

    // Input args
    const group_by_cols = ["item_id", "formatted_response"]
    const agg = [
      { col_new: "occurences", agg_type: "count" }
    ];
    
    let result = group_by(df_input, group_by_cols, agg)
    assert.deepStrictEqual(result, expected, "Works when calculating count while grouping by 2 columns");
  });

  it('works -- grouped by 2 columns, aggregating sum on score', () => {
    const df_input = [
      { taqr_id: 1, item_id: 1, score: 1, formatted_response: 'A', formatted_response_strict: null },
      { taqr_id: 2, item_id: 1, score: 1, formatted_response: 'A', formatted_response_strict: null },
      { taqr_id: 3, item_id: 1, score: 1, formatted_response: 'C', formatted_response_strict: null },
      { taqr_id: 1, item_id: 2, score: 1, formatted_response: 'A', formatted_response_strict: null },
      { taqr_id: 2, item_id: 2, score: 0, formatted_response: 'B', formatted_response_strict: null }
    ];

    const expected = [
      { item_id: 1, taqr_id: 1, total_score: 1 },
      { item_id: 1, taqr_id: 2, total_score: 1 },
      { item_id: 1, taqr_id: 3, total_score: 1 },
      { item_id: 2, taqr_id: 1, total_score: 1 },
      { item_id: 2, taqr_id: 2, total_score: 0 } 
    ];

    // Input args
    const group_by_cols = ["item_id", "taqr_id"]
    const agg = [
      { col_new: "total_score", agg_type: "sum", col_target: "score" },
    ];
    
    let result = group_by(df_input, group_by_cols, agg)
    assert.deepStrictEqual(result, expected, "Works when calculating sum while grouping by 2 columns");
  });

  it('works -- grouping by 2 columns, aggregating nunique', () => {
    const df_input = [
      { taqr_id: 1, item_id: 1, score: 1, formatted_response: 'A', formatted_response_strict: null },
      { taqr_id: 2, item_id: 1, score: 1, formatted_response: 'A', formatted_response_strict: null },
      { taqr_id: 3, item_id: 1, score: 1, formatted_response: 'C', formatted_response_strict: null },
      { taqr_id: 1, item_id: 2, score: 1, formatted_response: 'A', formatted_response_strict: null },
      { taqr_id: 2, item_id: 2, score: 0, formatted_response: 'B', formatted_response_strict: null }
    ];
  
    const expected = [
      { item_id: 1, formatted_response: 'A', n_taqrs: 2 },
      { item_id: 1, formatted_response: 'C', n_taqrs: 1 },
      { item_id: 2, formatted_response: 'A', n_taqrs: 1 },
      { item_id: 2, formatted_response: 'B', n_taqrs: 1 } 
    ];
  
    const group_by_cols = ["item_id", "formatted_response"];
    const agg = [
      { col_new: "n_taqrs", agg_type: "nunique", col_target: "taqr_id" }
    ];
  
    let result = group_by(df_input, group_by_cols, agg);
    assert.deepStrictEqual(result, expected, "Works when calculating nunique aggregation while grouping by 2 columns");
  });
  
  it('works -- grouping by 2 columns, 2 aggregations: nunique and mean', () => {
    const df_input = [
      { taqr_id: 1, item_id: 1, score: 1, formatted_response: 'A', formatted_response_strict: null },
      { taqr_id: 2, item_id: 1, score: 1, formatted_response: 'A', formatted_response_strict: null },
      { taqr_id: 3, item_id: 1, score: 1, formatted_response: 'C', formatted_response_strict: null },
      { taqr_id: 1, item_id: 2, score: 1, formatted_response: 'A', formatted_response_strict: null },
      { taqr_id: 2, item_id: 2, score: 0, formatted_response: 'B', formatted_response_strict: null }
    ];
  
    const expected = [
      { item_id: 1, formatted_response: 'A', n_taqrs: 2, average_score: 1 },
      { item_id: 1, formatted_response: 'C', n_taqrs: 1, average_score: 1 },
      { item_id: 2, formatted_response: 'A', n_taqrs: 1, average_score: 1 },
      { item_id: 2, formatted_response: 'B', n_taqrs: 1, average_score: 0 } 
    ];
  
    const group_by_cols = ["item_id", "formatted_response"];
    const agg = [
      { col_new: "n_taqrs", agg_type: "nunique", col_target: "taqr_id" },
      { col_new: "average_score", agg_type: "mean", col_target: "score" }
    ];
  
    let result = group_by(df_input, group_by_cols, agg);
    assert.deepStrictEqual(result, expected, "Works when calculating 2 aggregations while grouping by 2 columns");
  });

  it('works -- with null values and aggregates correctly', () => {
    const df_input = [
      { taqr_id: 1, item_id: 1, score: 0, formatted_response: 'A', formatted_response_strict: null },
      { taqr_id: 2, item_id: 1, score: null, formatted_response: 'A', formatted_response_strict: null },
      { taqr_id: 3, item_id: 1, score: 0, formatted_response: 'C', formatted_response_strict: null },
      { taqr_id: 1, item_id: 2, score: 1, formatted_response: 'A', formatted_response_strict: null },
      { taqr_id: 2, item_id: 2, score: 0, formatted_response: 'B', formatted_response_strict: null }
    ];
  
    const expected = [
      { item_id: 1, average_score: 0 },  
      { item_id: 2, average_score: 0.5 } 
    ];
  
    const group_by_cols = ["item_id"];
    const agg = [
      { col_new: "average_score", agg_type: "mean", col_target: "score" }
    ];
  
    let result = group_by(df_input, group_by_cols, agg);
    assert.deepStrictEqual(result, expected, "Works with null values and aggregates correctly");
  });

  it('works -- aggreagating min', () => {
    const df_input = [
      { taqr_id: 1, item_id: 1, score: 0, formatted_response: 'A', formatted_response_strict: null },
      { taqr_id: 2, item_id: 1, score: 1, formatted_response: 'A', formatted_response_strict: null },
      { taqr_id: 3, item_id: 1, score: 0, formatted_response: 'C', formatted_response_strict: null },
      { taqr_id: 1, item_id: 2, score: 1, formatted_response: 'A', formatted_response_strict: null },
      { taqr_id: 2, item_id: 2, score: 1, formatted_response: 'B', formatted_response_strict: null }
    ];

    const expected = [
      { taqr_id: 1, min_score: 0 },
      { taqr_id: 2, min_score: 1 },
      { taqr_id: 3, min_score: 0 }
    ];

    const group_by_cols = ["taqr_id"];
    const agg = [
      { col_new: "min_score", agg_type: "min", col_target: "score" }
    ];
  
    let result = group_by(df_input, group_by_cols, agg);
    assert.deepStrictEqual(result, expected, "Works correctly while aggregating min");
  });

  it('works -- aggreagating max', () => {
    const df_input = [
      { taqr_id: 1, item_id: 1, score: 0, formatted_response: 'A', formatted_response_strict: null },
      { taqr_id: 2, item_id: 1, score: 1, formatted_response: 'A', formatted_response_strict: null },
      { taqr_id: 3, item_id: 1, score: 0, formatted_response: 'C', formatted_response_strict: null },
      { taqr_id: 1, item_id: 2, score: 1, formatted_response: 'A', formatted_response_strict: null },
      { taqr_id: 2, item_id: 2, score: 1, formatted_response: 'B', formatted_response_strict: null }
    ];

    const expected = [
      { taqr_id: 1, max_score: 1 },
      { taqr_id: 2, max_score: 1 },
      { taqr_id: 3, max_score: 0 }
    ];

    const group_by_cols = ["taqr_id"];
    const agg = [
      { col_new: "max_score", agg_type: "max", col_target: "score" }
    ];
  
    let result = group_by(df_input, group_by_cols, agg);
    assert.deepStrictEqual(result, expected, "Works correctly while aggregating min");
  });
  // }}}
});
