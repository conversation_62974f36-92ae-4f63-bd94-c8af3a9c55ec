import assert from 'assert';
import { left_join } from '../../../../../../src/services/public/data-exporter/data-export-queries/transforms/merge'

type Value = number | string | boolean;
type IRow = { [key: string]: Value };

// TODO: these were generated with chat GPT, and we need more tests
describe('data-exporter | data-export-queries | transforms :: left_join', () => {

    it('should handle empty left and right tables', () => {
        const left: IRow[] = [];
        const right: IRow[] = [];

        const left_on = ['id'];
        const right_on = ['id'];

        const result = left_join(left, right, left_on, right_on);

        const expected: IRow[] = [];

        assert.deepStrictEqual(result, expected);
    });

    it('should handle empty left table, non-empty right table', () => {
        const left: IRow[] = [];
        const right: IRow[] = [
            { id: 1, age: 30 }
        ];

        const left_on = ['id'];
        const right_on = ['id'];

        const result = left_join(left, right, left_on, right_on);

        const expected: IRow[] = [];

        assert.deepStrictEqual(result, expected);
    });

    it('should join matching rows and sort lexicographically', () => {
        const left: IRow[] = [
            { id: 1, name: 'Alice' },
            { id: 2, name: 'Bob' }
        ];

        const right: IRow[] = [
            { id: 1, age: 30 },
            { id: 2, age: 25 }
        ];

        const left_on = ['id'];
        const right_on = ['id'];

        const result = left_join(left, right, left_on, right_on);

        const expected = [
            { id: 1, name: 'Alice', age: 30 },
            { id: 2, name: 'Bob', age: 25 }
        ];

        assert.deepStrictEqual(result, expected);
    });

    it('should handle non-matching rows from the right table', () => {
        const left: IRow[] = [
            { id: 1, name: 'Alice' },
            { id: 2, name: 'Bob' }
        ];

        const right: IRow[] = [
            { id: 1, age: 30 }
        ];

        const left_on = ['id'];
        const right_on = ['id'];

        const result = left_join(left, right, left_on, right_on);

        const expected = [
            { id: 1, name: 'Alice', age: 30 },
            { id: 2, name: 'Bob', age: null } // Bob does not have a match in `right`
        ];

        assert.deepStrictEqual(result, expected);
    });

    it('should handle non-matching rows from the left table', () => {
        const left: IRow[] = [
            { id: 1, name: 'Alice' }
        ];

        const right: IRow[] = [
            { id: 2, age: 25 }
        ];

        const left_on = ['id'];
        const right_on = ['id'];

        const result = left_join(left, right, left_on, right_on);

        const expected = [
            { id: 1, name: 'Alice', age: null } // No match from `right`
        ];

        assert.deepStrictEqual(result, expected);
    });

    it('should correctly handle cases with multiple matching rows and sorting', () => {
        const left: IRow[] = [
            { id: 1, name: 'Alice' },
            { id: 2, name: 'Bob' },
            { id: 1, name: 'Charlie' }
        ];

        const right: IRow[] = [
            { id: 1, age: 30 },
            { id: 2, age: 25 }
        ];

        const left_on = ['id'];
        const right_on = ['id'];

        const result = left_join(left, right, left_on, right_on);

        const expected = [
            { id: 1, name: 'Alice', age: 30 },
            { id: 1, name: 'Charlie', age: 30 },
            { id: 2, name: 'Bob', age: 25 }
        ];

        assert.deepStrictEqual(result, expected);
    });

    it('should drop the right_on fields in the output', () => {
        const left: IRow[] = [
            { id: 1, name: 'Alice' }
        ];

        const right: IRow[] = [
            { uid: 1, age: 30 }
        ];

        const left_on = ['id'];
        const right_on = ['uid'];

        const result = left_join(left, right, left_on, right_on);

        const expected = [
            { id: 1, name: 'Alice', age: 30 }
        ];

        assert.deepStrictEqual(result, expected);
    });

  // TODO: add test cases with multi-field keys
  it('should handle multi-field keys', () => {
    const left: IRow[] = [
      { id: 1, name: 'Alice', score: 95 },
      { id: 2, name: 'Bob', score: 85 },
      { id: 1, name: 'Charlie', score: 90 },
    ];
    const right: IRow[] = [
      { id: 1, name: 'Alice', age: 30 },
      { id: 2, name: 'Bob', age: 25 },
    ];
    const expected = [
      { id: 1, name: 'Alice', score: 95, age: 30 },
      { id: 1, name: 'Charlie', score: 90, age: null},
      { id: 2, name: 'Bob', score: 85, age: 25 },
    ];

    const left_on = ['id', 'name'];
    const right_on = ['id', 'name'];
    const result = left_join(left, right, left_on, right_on);
    assert.deepStrictEqual(result, expected, "Could not join using multiple keys");
  });

});
