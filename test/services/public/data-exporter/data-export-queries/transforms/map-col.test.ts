import assert from 'assert';
import fs from 'fs';

import { map_col } from '../../../../../../src/services/public/data-exporter/data-export-queries/transforms/map-col'

describe('data-exporter | data-export-queries | transforms :: map_col', () => {
  // Empty DataFrame {{{
    it('works when input dataframe is empty', () => {
      const df_input = [];
   
      const expected = [];
  
      let result = map_col(df_input, "outcome", ["raw_score", "cut_score"], "less-than", "In Focus", "Not In Focus")
      assert.deepStrictEqual(result, expected, "Works when input dataframe is empty")
    });
  // }}}

  // Comparison functions {{{
    it('works for comparison type is less-than', () => {
      const df_input = [
        { uid: 1, item_domain: "N", raw_score: 5, cut_score: 10 },
        { uid: 2, item_domain: "N", raw_score: 15, cut_score: 10 },
        { uid: 1, item_domain: "E", raw_score: 5, cut_score: 7 },
        { uid: 2, item_domain: "E", raw_score: 7, cut_score: 7 },
        { uid: 3, item_domain: "N", raw_score: 5, cut_score: 10 }
      ];
    
      const expected = [
        { uid: 1, item_domain: "N", raw_score: 5, cut_score: 10, outcome: "In Focus" },
        { uid: 2, item_domain: "N", raw_score: 15, cut_score: 10, outcome: "Not In Focus" },
        { uid: 1, item_domain: "E", raw_score: 5, cut_score: 7, outcome: "In Focus" },
        { uid: 2, item_domain: "E", raw_score: 7, cut_score: 7, outcome: "Not In Focus" },
        { uid: 3, item_domain: "N", raw_score: 5, cut_score: 10, outcome: "In Focus" }
      ];
  
      let result = map_col(df_input, "outcome", ["raw_score", "cut_score"], "less-than", "In Focus", "Not In Focus")
      assert.deepStrictEqual(result, expected, "Works for creating column based on comparing two column values and using less-than comparison type")
    });
  // }}}
});