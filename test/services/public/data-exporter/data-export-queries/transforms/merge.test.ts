import assert from 'assert';
import fs from 'fs';

import { inner_join } from '../../../../../../src/services/public/data-exporter/data-export-queries/transforms/merge'

describe('data-exporter | data-export-queries | transforms :: inner_join', () => {
    // Empty Tables {{{
    it('works on both tables empty', () => {
      const left = [];
      const right = [];
      const result = inner_join(left, right, ["left_on"], ["right_on"]);

      assert.strictEqual(result.length, 0, "Resulting table is empty");
    });
    it('works on one table empty', () => {
      const left = [];
      const right = [{id: 1, value: "123"}, {id: 2, value: "five"}];
      let result = inner_join(left, right, ["left_on"], ["id"]);
      assert.strictEqual(result.length, 0, "Resulting table is empty if left empty");

      result = inner_join(right, left, ["id"], ["right_on"]);
      assert.strictEqual(result.length, 0, "Resulting table is empty if right empty");
    });
    // }}}

    // Interface {{{
    it('supports single value for "on" -- shared column name', () => {
      const left = [{id: 1, left_value: "7"}, {id: 2, left_value: "5"}];
      const right = [{id: 1, right_value: "seven"}, {id: 2, right_value: "five"}];
      const expected = [
        {id: 1, left_value: "7", right_value: "seven"},
        {id: 2, left_value: "5", right_value: "five"},
      ];
      let result = inner_join(left, right, ["id"])
      assert.deepStrictEqual(result, expected, "Join works with same column name for both")
    });
    // }}}

    // Simple joins with unique keys {{{
    it('works -- unique keys, same values', () => {
      const left = [{id: 1, left_value: "7"}, {id: 2, left_value: "5"}];
      const right = [{id: 1, right_value: "seven"}, {id: 2, right_value: "five"}];
      const expected = [
        {id: 1, left_value: "7", right_value: "seven"},
        {id: 2, left_value: "5", right_value: "five"},
      ];
      let result = inner_join(left, right, ["id"], ["id"])
      assert.deepStrictEqual(result, expected, "Join works when both have same set of ids")
    });

    it('works -- unique keys, same values - different type for keys, keeps key from left table', () => {
      const left = [{id: 1, left_value: "7"}, {id: 2, left_value: "5"}];
      const right = [{id: "1", right_value: "seven"}, {id: "2", right_value: "five"}];
      const expected = [
        {id: 1, left_value: "7", right_value: "seven"},
        {id: 2, left_value: "5", right_value: "five"},
      ];
      let result = inner_join(left, right, ["id"], ["id"])
      assert.deepStrictEqual(result, expected, "Join works when both have same set of ids")
    });

    it('works -- unique keys, extra right values', () => {
      const left = [{id: 1, left_value: "7"}, {id: 2, left_value: "5"}];
      const right = [{id: 1, right_value: "seven"}, {id: 2, right_value: "five"}];
      const expected = [
        {id: 1, left_value: "7", right_value: "seven"},
        {id: 2, left_value: "5", right_value: "five"},
      ];

      const left_on = ["id"];
      const right_on = ["id"];
      right.push({id: 3, right_value: "ten"})
      let result = inner_join(left, right, left_on, right_on)
      assert.deepStrictEqual(result, expected, "Extra rows in right not included")
    });
    it('works -- unique keys, extra left values', () => {
      const left = [{id: 1, left_value: "7"}, {id: 2, left_value: "5"}];
      const right = [{id: 1, right_value: "seven"}, {id: 2, right_value: "five"}];
      const expected = [
        {id: 1, left_value: "7", right_value: "seven"},
        {id: 2, left_value: "5", right_value: "five"},
      ];

      const left_on = ["id"];
      left.push({id: 4, left_value: '6'})
      let result = inner_join(left, right, left_on, left_on)
      assert.deepStrictEqual(result, expected, "Extra rows in both not included")
    });
    it('works -- unique keys, unmatched values', () => {
      const left = [{id: 1, left_value: "7"}, {id: 2, left_value: "5"}];
      const right = [{id: 1, right_value: "seven"}, {id: 2, right_value: "five"}];
      const expected = [
        {id: 1, left_value: "7", right_value: "seven"},
        {id: 2, left_value: "5", right_value: "five"},
      ];
      right.push({id: 3, right_value: "ten"})
      left.push({id: 4, left_value: '6'})

      const left_on = ["id"];
      let result = inner_join(left, right, left_on, left_on);
      assert.deepStrictEqual(result, expected, "Extra rows in both not included")
    });
    // }}}

    it('works -- unique keys, mixed sorting', () => {
      // WARNING: This test enforces the order outputed by sort-merge join
      //  - it may not work properly with other algorithms
      //  - in that case, compare sorted expected and results
      const left = [
        {id: 1, left_value: "7"},
        {id: 4, left_value: '6'},
        {id: 2, left_value: "5"},
      ];
      const right = [
        {id: 2, right_value: "five"},
        {id: 1, right_value: "seven"},
        {id: 3, right_value: "ten"},
      ];
      const expected = [
        {id: 1, left_value: "7", right_value: "seven"},
        {id: 2, left_value: "5", right_value: "five"},
      ];

      const left_on = ["id"];
      let result = inner_join(left, right, left_on, left_on)
      assert.deepStrictEqual(result, expected, "Extra rows in both not included")
    });

    // Non-unique joins
    it('works -- duplicate keys on right', () => {
      // WARNING: This test enforces the order outputed by sort-merge join
      //  - it may not work properly with other algorithms
      //  - in that case, compare sorted expected and results
      const left = [
        {id: 1, left_value: "7"},
        {id: 2, left_value: "5"},
        {id: 4, left_value: '6'},
      ];
      const right = [
        {id: 1, right_value: "seven"},
        {id: 1, right_value: "eight minus 1"},
        {id: 2, right_value: "five"},
        {id: 3, right_value: "ten"},
      ];
      const expected = [
        {id: 1, left_value: "7", right_value: "seven"},
        {id: 1, left_value: "7", right_value: "eight minus 1"},
        {id: 2, left_value: "5", right_value: "five"},
      ];

      const left_on = ["id"];
      let result = inner_join(left, right, left_on, left_on)
      assert.deepStrictEqual(result, expected, "Extra rows in both not included")
    });
    it('works -- duplicate keys on left', () => {
      // WARNING: This test enforces the order outputed by sort-merge join
      //  - it may not work properly with other algorithms
      //  - in that case, compare sorted expected and results
      const left = [
        {id: 1, left_value: "7"},
        {id: 2, left_value: "5"},
        {id: 2, left_value: "-5"},
        {id: 2, left_value: "+5"},
        {id: 4, left_value: '6'},
      ];
      const right = [
        {id: 1, right_value: "seven"},
        {id: 2, right_value: "five"},
        {id: 3, right_value: "ten"},
      ];
      const expected = [
        {id: 1, left_value: "7", right_value: "seven"},
        {id: 2, left_value: "5", right_value: "five"},
        {id: 2, left_value: "-5", right_value: "five"},
        {id: 2, left_value: "+5", right_value: "five"},
      ];
      const left_on = ["id"];
      let result = inner_join(left, right, left_on, left_on);
      assert.deepStrictEqual(result, expected, "Extra rows in both not included")
    });
    it('works -- duplicate keys on both (distinct)', () => {
      // WARNING: This test enforces the order outputed by sort-merge join
      //  - it may not work properly with other algorithms
      //  - in that case, compare sorted expected and results
      const left = [
        {id: 1, left_value: "7"},
        {id: 2, left_value: "5"},
        {id: 2, left_value: "-5"},
        {id: 2, left_value: "+5"},
        {id: 4, left_value: '6'},
      ];
      const right = [
        {id: 1, right_value: "seven"},
        {id: 1, right_value: "eight minus 1"},
        {id: 2, right_value: "five"},
        {id: 3, right_value: "ten"},
      ];
      const expected = [
        {id: 1, left_value: "7", right_value: "seven"},
        {id: 1, left_value: "7", right_value: "eight minus 1"},
        {id: 2, left_value: "5", right_value: "five"},
        {id: 2, left_value: "-5", right_value: "five"},
        {id: 2, left_value: "+5", right_value: "five"},
      ];
      const left_on = ["id"];
      let result = inner_join(left, right, left_on, left_on);
      assert.deepStrictEqual(result, expected, "Extra rows in both not included")
    });
    it('works -- duplicate keys on both (shared)', () => {
      // WARNING: This test enforces the order outputed by sort-merge join
      //  - it may not work properly with other algorithms
      //  - in that case, compare sorted expected and results
      const left = [
        {id: 1, left_value: "7"},
        {id: 1, left_value: "+7"},
        {id: 1, left_value: "-7"},
        {id: 2, left_value: "5"},
        {id: 2, left_value: "-5"},
        {id: 2, left_value: "+5"},
        {id: 4, left_value: '6'},
      ];
      const right = [
        {id: 1, right_value: "seven"},
        {id: 1, right_value: "eight minus 1"},
        {id: 2, right_value: "five"},
        {id: 3, right_value: "ten"},
      ];
      const expected = [
        {id: 1, left_value: "7", right_value: "seven"},
        {id: 1, left_value: "+7", right_value: "seven"},
        {id: 1, left_value: "-7", right_value: "seven"},
        {id: 1, left_value: "7", right_value: "eight minus 1"},
        {id: 1, left_value: "+7", right_value: "eight minus 1"},
        {id: 1, left_value: "-7", right_value: "eight minus 1"},
        {id: 2, left_value: "5", right_value: "five"},
        {id: 2, left_value: "-5", right_value: "five"},
        {id: 2, left_value: "+5", right_value: "five"},
      ];
      const left_on = ["id"];
      let result = inner_join(left, right, left_on, left_on);
      assert.deepStrictEqual(result, expected, "Extra rows in both not included")
    });

    it('works -- duplicate keys on both (shared, shuffled)', () => {
      // WARNING: This test enforces the order outputed by sort-merge join
      //  - it may not work properly with other algorithms
      //  - in that case, compare sorted expected and results
      const left = [
        {id: 4, left_value: '6'},
        {id: 1, left_value: "7"},
        {id: 2, left_value: "5"},
        {id: 1, left_value: "-7"},
        {id: 2, left_value: "-5"},
        {id: 1, left_value: "+7"},
        {id: 2, left_value: "+5"},
      ];
      const right = [
        {id: 1, right_value: "seven"},
        {id: 2, right_value: "five"},
        {id: 3, right_value: "ten"},
        {id: 1, right_value: "eight minus 1"},
      ];
      const expected = [
        {id: 1, left_value: "7", right_value: "seven"},
        {id: 1, left_value: "-7", right_value: "seven"},
        {id: 1, left_value: "+7", right_value: "seven"},
        {id: 1, left_value: "7", right_value: "eight minus 1"},
        {id: 1, left_value: "-7", right_value: "eight minus 1"},
        {id: 1, left_value: "+7", right_value: "eight minus 1"},
        {id: 2, left_value: "5", right_value: "five"},
        {id: 2, left_value: "-5", right_value: "five"},
        {id: 2, left_value: "+5", right_value: "five"},
      ];

      const left_on = ["id"];
      let result = inner_join(left, right, left_on, left_on);
      assert.deepStrictEqual(result, expected, "Extra rows in both not included")
    });

    it('works -- duplicate keys on both (shared, shuffled), different keys drops right key', () => {
      // WARNING: This test enforces the order outputed by sort-merge join
      //  - it may not work properly with other algorithms
      //  - in that case, compare sorted expected and results
      const left = [
        {id: 4, left_value: '6'},
        {id: 1, left_value: "7"},
        {id: 2, left_value: "5"},
        {id: 1, left_value: "-7"},
        {id: 2, left_value: "-5"},
        {id: 1, left_value: "+7"},
        {id: 2, left_value: "+5"},
      ];
      const right = [
        {right_id: 1, right_value: "seven"},
        {right_id: 2, right_value: "five"},
        {right_id: 3, right_value: "ten"},
        {right_id: 1, right_value: "eight minus 1"},
      ];
      const expected = [
        {id: 1, left_value: "7", right_value: "seven"},
        {id: 1, left_value: "-7", right_value: "seven"},
        {id: 1, left_value: "+7", right_value: "seven"},
        {id: 1, left_value: "7", right_value: "eight minus 1"},
        {id: 1, left_value: "-7", right_value: "eight minus 1"},
        {id: 1, left_value: "+7", right_value: "eight minus 1"},
        {id: 2, left_value: "5", right_value: "five"},
        {id: 2, left_value: "-5", right_value: "five"},
        {id: 2, left_value: "+5", right_value: "five"},
      ];

      const left_on = ["id"];
      const right_on = ["right_id"];
      let result = inner_join(left, right, left_on, right_on);
      assert.deepStrictEqual(result, expected, "Extra rows in both not included")
    });


    // File-based run to validate on real assets
    it.skip("works with taqr and formatted response", () => {
      const taqr = JSON.parse(fs.readFileSync('test-data/load_taqr.json', 'utf8'))
      const fmt_resp = JSON.parse(fs.readFileSync('test-data/load_formatted_responses.json', 'utf8'))

      const left_on = ["taqr_id"];
      const right_on = ["response_id"];
      let result = inner_join(taqr, fmt_resp, left_on, right_on);
      fs.writeFileSync('test-data/trfm_item_responses.json', JSON.stringify(result))
    });

  // TODO: Add tests for multi-field keys
  it('works -- multi-field keys, single match', () => {
    const left = [
      { id: 1, name: 'Alice', score: 95 },
      { id: 2, name: 'Bob', score: 85 },
      { id: 1, name: 'Charlie', score: 90 },
    ];
    const right = [
      { id: 1, name: 'Alice', age: 30 },
      { id: 2, name: 'Bob', age: 25 },
    ];
    const expected = [
      { id: 1, name: 'Alice', score: 95, age: 30 },
      { id: 2, name: 'Bob', score: 85, age: 25 },
    ];

    const left_on = ['id', 'name'];
    const right_on = ['id', 'name'];
    const result = inner_join(left, right, left_on, right_on);
    assert.deepStrictEqual(result, expected, "Could not join using multiple keys");
  });
  it('works -- multi-field keys, multi-match', () => {
    const left = [
      { id: 1, name: 'Alice', age: 30},
      { id: 2, name: 'Bob', age: 25},
      { id: 1, name: 'Charlie', age: 36},
    ];
    const right = [
      { id: 1, name: 'Alice', attempt_id: 12, score: 95 },
      { id: 1, name: 'Alice', attempt_id: 14, score: 90 },
      { id: 2, name: 'Bob', attempt_id: 13, score: 85 },
      { id: 1, name: 'Charlie', attempt_id: 15, score: 80 },
    ];
    const expected = [
      { id: 1, name: 'Alice', attempt_id: 12, score: 95, age: 30 },
      { id: 1, name: 'Alice', attempt_id: 14, score: 90, age: 30 },
      { id: 1, name: 'Charlie', attempt_id: 15, score: 80, age: 36 },
      { id: 2, name: 'Bob', attempt_id: 13, score: 85, age: 25 },
    ];

    const left_on = ['id', 'name'];
    const right_on = ['id', 'name'];
    const result = inner_join(left, right, left_on, right_on);
    assert.deepStrictEqual(result, expected, "Could not join using multiple keys");
  });
});
