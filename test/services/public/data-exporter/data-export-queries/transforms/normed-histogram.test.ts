import assert from 'assert';

import { normed_histogram } from '../../../../../../src/services/public/data-exporter/data-export-queries/transforms/normed-histogram'

describe('data-exporter | data-export-queries | transforms :: normed_histogram', () => {
  it('works - correct number of bins in output', () => {
    const result = normed_histogram(100, 0, 1, 5);
    assert.deepStrictEqual(result.length, 5, "works with the number of bins specification") // Expect 5 bins
  });

  // TODO: add tests
});