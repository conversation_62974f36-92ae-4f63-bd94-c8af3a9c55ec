import assert from 'assert';
import fs from 'fs';

import { pivot, aggregators } from '../../../../../../src/services/public/data-exporter/data-export-queries/transforms/pivot'

describe('data-exporter | data-export-queries | transforms :: pivot ', () => {
  // Empty DataFrame {{{
  it('works when input dataframe is empty', () => {
    const df_input = [];

    const expected = [];

    let result = pivot(df_input, ["ta_id"], "item_id", "score", aggregators.max)
    assert.deepStrictEqual(result, expected, "Works when input dataframe is empty")
  });
  // }}}
  //
  it('works on simple input (single group, no duplicates for columns)', () => {
    const df_input = [
      {'ta_id': 12345, 'item_id': 1111, 'score': 0},
      {'ta_id': 12345, 'item_id': 2222, 'score': 1},
      {'ta_id': 12345, 'item_id': 3333, 'score': 1},
    ];

    const expected = [
      {'ta_id': 12345, '1111': 0, '2222': 1, '3333': 1},
    ];

    let result = pivot(df_input, ["ta_id"], "item_id", "score", aggregators.first)
    assert.deepStrictEqual(result, expected, "Works with 'first'")
    result = pivot(df_input, ["ta_id"], "item_id", "score", aggregators.last)
    assert.deepStrictEqual(result, expected, "Works with 'last'")
    result = pivot(df_input, ["ta_id"], "item_id", "score", aggregators.mean)
    assert.deepStrictEqual(result, expected, "Works with 'mean'")
    result = pivot(df_input, ["ta_id"], "item_id", "score", aggregators.max)
    assert.deepStrictEqual(result, expected, "Works with 'max'")
    result = pivot(df_input, ["ta_id"], "item_id", "score", aggregators.min)
    assert.deepStrictEqual(result, expected, "Works with 'min'")
  });
  it('works on less simple input (two groups, no duplicates for columns)', () => {
    const df_input = [
      {'ta_id': 12345, 'item_id': 1111, 'score': 0},
      {'ta_id': 12345, 'item_id': 2222, 'score': 1},
      {'ta_id': 12345, 'item_id': 3333, 'score': 1},
      {'ta_id': 98765, 'item_id': 1111, 'score': 1},
      {'ta_id': 98765, 'item_id': 2222, 'score': 5},
      {'ta_id': 98765, 'item_id': 3333, 'score': 4},
    ];

    const expected = [
      {'ta_id': 12345, '1111': 0, '2222': 1, '3333': 1},
      {'ta_id': 98765, '1111': 1, '2222': 5, '3333': 4},
    ];

    let result = pivot(df_input, ["ta_id"], "item_id", "score", aggregators.first)
    assert.deepStrictEqual(result, expected, "Works with 'first'")
    result = pivot(df_input, ["ta_id"], "item_id", "score", aggregators.last)
    assert.deepStrictEqual(result, expected, "Works with 'last'")
    result = pivot(df_input, ["ta_id"], "item_id", "score", aggregators.mean)
    assert.deepStrictEqual(result, expected, "Works with 'mean'")
    result = pivot(df_input, ["ta_id"], "item_id", "score", aggregators.max)
    assert.deepStrictEqual(result, expected, "Works with 'max'")
    result = pivot(df_input, ["ta_id"], "item_id", "score", aggregators.min)
    assert.deepStrictEqual(result, expected, "Works with 'min'")
  });
  it('fills in default values on less simple input (two groups, no duplicates for columns)', () => {
    const df_input = [
      {'ta_id': 12345, 'item_id': 1111, 'score': 0},
      {'ta_id': 12345, 'item_id': 2222, 'score': 1},
      {'ta_id': 12345, 'item_id': 3333, 'score': 1},
      {'ta_id': 98765, 'item_id': 4444, 'score': 1},
      {'ta_id': 98765, 'item_id': 2222, 'score': 5},
      {'ta_id': 98765, 'item_id': 5555, 'score': 4},
    ];

    const expected = [
      {'ta_id': 12345, '1111': 0, '2222': 1, '3333': 1, '4444': -1, '5555': -1},
      {'ta_id': 98765, '1111': -1, '2222': 5, '3333': -1, '4444': 1, '5555': 4},
    ];

    let result = pivot(df_input, ["ta_id"], "item_id", "score", aggregators.first, -1)
    assert.deepStrictEqual(result, expected, "Works with 'first'")
    result = pivot(df_input, ["ta_id"], "item_id", "score", aggregators.last, -1)
    assert.deepStrictEqual(result, expected, "Works with 'last'")
    result = pivot(df_input, ["ta_id"], "item_id", "score", aggregators.mean, -1)
    assert.deepStrictEqual(result, expected, "Works with 'mean'")
    result = pivot(df_input, ["ta_id"], "item_id", "score", aggregators.max, -1)
    assert.deepStrictEqual(result, expected, "Works with 'max'")
    result = pivot(df_input, ["ta_id"], "item_id", "score", aggregators.min, -1)
    assert.deepStrictEqual(result, expected, "Works with 'min'")
  });

  // File-based run to validate on real assets
  it.skip("works on data from file", () => {
    const taqr = JSON.parse(fs.readFileSync('test-data/partby_ta_ids-1000500.json', 'utf8'))
    let result = pivot(taqr, ["ta_id"], "item_id", "score", aggregators.max)
    fs.writeFileSync('test-data/parted_pivot.json', JSON.stringify(result))
  });
});
