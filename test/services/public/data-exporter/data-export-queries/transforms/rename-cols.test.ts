import assert from 'assert';

import { renameCols, renameColsInplace } from '../../../../../../src/services/public/data-exporter/data-export-queries/transforms/rename-cols'

describe('data-exporter | data-export-queries | transforms :: rename-cols', () => {
  // Empty DataFrame or argument {{{
  it('works when input dataframe is empty', () => {
    const df_input = [];

    const expected = [];

    let result = renameCols(df_input, {"question_id": "item_id", "weight": "score_max"})
    assert.deepStrictEqual(result, expected, "Works when input dataframe is empty")
  });

  it('renames columns, leaving input dataframe unchanged', () => {
    const df_input = [
      { item_id: 1, score: 1, formatted_response: 'A', weight: 1 },
      { item_id: 2, score: 0, formatted_response: 'A', weight: 1 },
      { item_id: 3, score: 0, formatted_response: 'B', weight: 1 },
      { item_id: 4, score: 1, formatted_response: 'A', weight: 1 },
    ];
    const unchanged = structuredClone(df_input);

    const expected = [
      { question_id: 1, score: 1, formatted_response: 'A', score_max: 1 },
      { question_id: 2, score: 0, formatted_response: 'A', score_max: 1 },
      { question_id: 3, score: 0, formatted_response: 'B', score_max: 1 },
      { question_id: 4, score: 1, formatted_response: 'A', score_max: 1 },
    ];

    let result = renameCols(df_input, {"item_id": "question_id", "weight": "score_max"})
    assert.deepStrictEqual(result, expected, "Renames columns");
    assert.deepStrictEqual(unchanged, df_input, "Input dataframe unchanged");
  });

  // TODO: more unit tests, like if columns are missing

});

describe('data-exporter | data-export-queries | transforms :: rename-cols inplace', () => {
  // Empty DataFrame or argument {{{
  it('works when input dataframe is empty', () => {
    const df_input = [];

    const expected = [];

    let result = renameColsInplace(df_input, {"question_id": "item_id", "weight": "score_max"})
    assert.deepStrictEqual(result, expected, "Works when input dataframe is empty")
  });

  it('renames columns, changing original dataframe', () => {
    const df_input = [
      { item_id: 1, score: 1, formatted_response: 'A', weight: 1 },
      { item_id: 2, score: 0, formatted_response: 'A', weight: 1 },
      { item_id: 3, score: 0, formatted_response: 'B', weight: 1 },
      { item_id: 4, score: 1, formatted_response: 'A', weight: 1 },
    ];
    const unchanged = structuredClone(df_input);

    const expected = [
      { question_id: 1, score: 1, formatted_response: 'A', score_max: 1 },
      { question_id: 2, score: 0, formatted_response: 'A', score_max: 1 },
      { question_id: 3, score: 0, formatted_response: 'B', score_max: 1 },
      { question_id: 4, score: 1, formatted_response: 'A', score_max: 1 },
    ];

    let result = renameColsInplace(df_input, {"item_id": "question_id", "weight": "score_max"})
    assert.deepStrictEqual(result, expected, "Renames columns")
    assert.deepStrictEqual(result, df_input, "Input dataframe changed in place")
    assert.notDeepStrictEqual(unchanged, df_input, "Input datafram did actually change")
  });

  // TODO: more unit tests, like if columns are missing

});
