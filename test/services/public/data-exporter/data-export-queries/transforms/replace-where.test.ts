import assert from 'assert';
import { replace } from 'lodash';

import replaceWhere from '../../../../../../src/services/public/data-exporter/data-export-queries/transforms/replace-where'

describe('data-exporter | data-export-queries | transforms :: replace_where', () => {
  // Empty DataFrame {{{
  it('works when target dataframe is empty', () => {
    const df_target = [];
    const df_source = [{"foo": "bar"}];

    const expected = [];

    let result = replaceWhere(df_target, df_source, ["foo"], ["foo"], "foo", "foo")
    assert.deepStrictEqual(result, expected, "Works when input dataframe is empty")
  });

  it('works when source dataframe is empty', () => {
    const df_target = [{"foo": "bar"}];
    const df_source = [];

    const expected = [{"foo": "bar"}];

    let result = replaceWhere(df_target, df_source, ["foo"], ["foo"], "foo", "foo")
    assert.deepStrictEqual(result, expected, "Works when input dataframe is empty")
  });
  // }}}

  // single part key, same names {{{

  it('works -- single part key, no imputed column', () => {
    const df_target = [
      { item_id: 1, score: 5 },
      { item_id: 2, score: 5 },
      { item_id: 3, score: 5 },
      { item_id: 4, score: 5 }
    ];
    const df_source = [
      { item_id: 2, score: 0 },
    ];

    const expected = [
      { item_id: 1, score: 5 },
      { item_id: 2, score: 0 },
      { item_id: 3, score: 5 },
      { item_id: 4, score: 5 }
    ]

    let result = replaceWhere(df_target, df_source, ["item_id"], ["item_id"], "score", "score")
    assert.deepStrictEqual(result, expected, "Works")
  });

  it('works -- single part key, no matches, no imputed column', () => {
    const df_target = [
      { item_id: 1, score: 5 },
      { item_id: 2, score: 5 },
      { item_id: 3, score: 5 },
      { item_id: 4, score: 5 }
    ];
    const df_source = [
      { item_id: 5, score: 0 },
    ];

    const expected = [
      { item_id: 1, score: 5 },
      { item_id: 2, score: 5 },
      { item_id: 3, score: 5 },
      { item_id: 4, score: 5 }
    ]

    let result = replaceWhere(df_target, df_source, ["item_id"], ["item_id"], "score", "score")
    assert.deepStrictEqual(result, expected, "Works")
  });

  it('works -- single part key, multiple matches, no imputed column', () => {
    const df_target = [
      { item_id: 1, score: 5 },
      { item_id: 2, score: 5 },
      { item_id: 3, score: 5 },
      { item_id: 4, score: 5 },
      { item_id: 2, score: 3 },
      { item_id: 2, score: 2 }
    ];
    const df_source = [
      { item_id: 2, score: 0 },
    ];
    const expected = [
      { item_id: 1, score: 5 },
      { item_id: 2, score: 0 },
      { item_id: 3, score: 5 },
      { item_id: 4, score: 5 },
      { item_id: 2, score: 0 },
      { item_id: 2, score: 0 }
    ]
    let result = replaceWhere(df_target, df_source, ["item_id"], ["item_id"], "score", "score")
    assert.deepStrictEqual(result, expected, "Works")
  });

  it('works -- single part key, multiple matches, imputed column', () => {
    const df_target = [
      { item_id: 1, score: 5 },
      { item_id: 2, score: 5 },
      { item_id: 3, score: 5 },
      { item_id: 4, score: 5 },
      { item_id: 2, score: 3 },
      { item_id: 2, score: 2 }
    ];
    const df_source = [
      { item_id: 2, score: 0 },
    ];
    const expected = [
      { item_id: 1, score: 5, is_imputed: 0 },
      { item_id: 2, score: 0, is_imputed: 1 },
      { item_id: 3, score: 5, is_imputed: 0 },
      { item_id: 4, score: 5, is_imputed: 0 },
      { item_id: 2, score: 0, is_imputed: 1 },
      { item_id: 2, score: 0, is_imputed: 1 }
    ]
    let result = replaceWhere(df_target, df_source, ["item_id"], ["item_id"], "score", "score", "is_imputed")
    assert.deepStrictEqual(result, expected, "Works")
  });

  // }}}

  // single part key, different names {{{

  it('works -- single part key, different names, no imputed column', () => {
    const df_target = [
      { item_id: 1, score: 5 },
      { item_id: 2, score: 5 },
      { item_id: 3, score: 5 },
      { item_id: 4, score: 5 },
      { item_id: 2, score: 3 },
      { item_id: 2, score: 2 }
    ];
    const df_source = [
      { question_id: 2, score_override: 0 },
    ];
    const expected = [
      { item_id: 1, score: 5 },
      { item_id: 2, score: 0 },
      { item_id: 3, score: 5 },
      { item_id: 4, score: 5 },
      { item_id: 2, score: 0 },
      { item_id: 2, score: 0 }
    ]
    let result = replaceWhere(df_target, df_source, ["item_id"], ["question_id"], "score", "score_override")
    assert.deepStrictEqual(result, expected, "Works")
  });

  // }}}

  // multi-part key {{{

  it('works -- multi-part key, different names, no imputed column', () => {
    const df_target = [
      { uid: 101, item_id: 1, score: 5 },
      { uid: 101, item_id: 2, score: 5 },
      { uid: 101, item_id: 3, score: 5 },
      { uid: 101, item_id: 4, score: 5 },
      { uid: 105, item_id: 2, score: 3 },
      { uid: 106, item_id: 2, score: 2 }
    ];
    const df_source = [
      { uid: 105, question_id: 2, score_override: 0 },
      { uid: 101, question_id: 2, score_override: 0 },
    ];
    const expected = [
      { uid: 101, item_id: 1, score: 5 },
      { uid: 101, item_id: 2, score: 0 },
      { uid: 101, item_id: 3, score: 5 },
      { uid: 101, item_id: 4, score: 5 },
      { uid: 105, item_id: 2, score: 0 },
      { uid: 106, item_id: 2, score: 2 }
    ];
    let result = replaceWhere(df_target, df_source, ["uid", "item_id"], ["uid", "question_id"], "score", "score_override")
    assert.deepStrictEqual(result, expected, "Works")
  });

  // }}}

  it('works -- multi-part key, different names, imputed column', () => {
    const df_target = [
      { uid: 101, item_id: 1, score: 5 },
      { uid: 101, item_id: 2, score: 5 },
      { uid: 101, item_id: 3, score: 5 },
      { uid: 101, item_id: 4, score: 5 },
      { uid: 105, item_id: 2, score: 3 },
      { uid: 106, item_id: 2, score: 2 }
    ];
    const df_source = [
      { uid: 105, question_id: 2, score_override: 0 },
      { uid: 101, question_id: 2, score_override: 0 },
    ];
    const expected = [
      { uid: 101, item_id: 1, score: 5, is_imputed: 0 },
      { uid: 101, item_id: 2, score: 0, is_imputed: 1 },
      { uid: 101, item_id: 3, score: 5, is_imputed: 0 },
      { uid: 101, item_id: 4, score: 5, is_imputed: 0 },
      { uid: 105, item_id: 2, score: 0, is_imputed: 1 },
      { uid: 106, item_id: 2, score: 2, is_imputed: 0 }
    ];
    let result = replaceWhere(df_target, df_source, ["uid", "item_id"], ["uid", "question_id"], "score", "score_override", "is_imputed")
    assert.deepStrictEqual(result, expected, "Works")
  });
});
