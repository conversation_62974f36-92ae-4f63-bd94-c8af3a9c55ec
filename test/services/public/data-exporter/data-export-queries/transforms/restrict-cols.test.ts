import assert from 'assert';
import fs from 'fs';

import { restrict_cols } from '../../../../../../src/services/public/data-exporter/data-export-queries/transforms/restrict-cols'

describe('data-exporter | data-export-queries | transforms :: restrict_cols', () => {
  // Empty DataFrame or argument {{{
  it('works when input dataframe is empty', () => {
    const df_input = [];
 
    const expected = [];

    let result = restrict_cols(df_input, ["item_id", "score"])
    assert.deepStrictEqual(result, expected, "Works when input dataframe is empty")
  });

  it('works for empty list of columns as argument', () => {
    const df_input = [
      { item_id: 1, score: 1, formatted_response: 'A', weight: 1 },
      { item_id: 2, score: 0, formatted_response: 'A', weight: 1 },
      { item_id: 3, score: 0, formatted_response: 'B', weight: 1 },
      { item_id: 4, score: 1, formatted_response: 'A', weight: 1 }
    ];
 
    const expected = []

    let result = restrict_cols(df_input, [])
    assert.deepStrictEqual(result, expected, "Works for restricting columns in output where specified column list is empty")
  });
  // }}}

  // Invalid Inputs {{{
  it('throws an error when specified column is not present in input', () => {
    // TODO: Confirm implementation - if we skip or throw an exception
    const df_input = [
      { item_id: 1, score: 1, formatted_response: 'A', weight: 1 },
      { item_id: 2, score: 0, formatted_response: 'A', weight: 1 }
    ];
  
    assert.throws(() => {
      restrict_cols(df_input, ["item_id", "formatted_response", "formatted_response_strict"]);
    }, Error, "Error thrown for missing column");
  });
  // }}}

  /// Restricting Columns {{{
  it('works for restricting columns from the input dataframe', () => {
    const df_input = [
      { item_id: 1, score: 1, formatted_response: 'A', weight: 1 },
      { item_id: 2, score: 0, formatted_response: 'A', weight: 1 },
      { item_id: 3, score: 0, formatted_response: 'B', weight: 1 },
      { item_id: 4, score: 1, formatted_response: 'A', weight: 1 }
    ];
 
    const expected = [
      { item_id: 1, score: 1 },
      { item_id: 2, score: 0 },
      { item_id: 3, score: 0 },
      { item_id: 4, score: 1 }
    ]

    let result = restrict_cols(df_input, ["item_id", "score"])
    assert.deepStrictEqual(result, expected, "Works for restricting columns from the input dataframe based on specified column list")
  });

  it('works when all columns in the input match the restricted columns', () => {
    const df_input = [
      { item_id: 1, score: 1, formatted_response: 'A', weight: 1 },
      { item_id: 2, score: 0, formatted_response: 'A', weight: 1 },
      { item_id: 3, score: 0, formatted_response: 'B', weight: 1 },
      { item_id: 4, score: 1, formatted_response: 'A', weight: 1 }
    ];
 
    const expected = [
      { item_id: 1, score: 1, formatted_response: 'A', weight: 1 },
      { item_id: 2, score: 0, formatted_response: 'A', weight: 1 },
      { item_id: 3, score: 0, formatted_response: 'B', weight: 1 },
      { item_id: 4, score: 1, formatted_response: 'A', weight: 1 }
    ];

    let result = restrict_cols(df_input, ["item_id", "score", "formatted_response", "weight"])
    assert.deepStrictEqual(result, expected, "Works when all columns in the input match the restricted columns")
  });

  it('works when returning only one column', () => {
    const df_input = [
      { item_id: 1, score: 1, formatted_response: 'A', weight: 1 },
      { item_id: 2, score: 0, formatted_response: 'A', weight: 1 },
      { item_id: 3, score: 0, formatted_response: 'B', weight: 1 },
      { item_id: 4, score: 1, formatted_response: 'A', weight: 1 }
    ];
 
    const expected = [
      { item_id: 1},
      { item_id: 2},
      { item_id: 3},
      { item_id: 4 }
    ];

    let result = restrict_cols(df_input, ["item_id"])
    assert.deepStrictEqual(result, expected, "Works when returning only one column")
  });
  /// }}}
});
