import assert from 'assert';

import { setWhere, setWhereInplace } from '../../../../../../src/services/public/data-exporter/data-export-queries/transforms/set-where'

describe('data-exporter | data-export-queries | transforms :: set-where', () => {
  // Empty DataFrame or argument {{{
  it('works when input dataframe is empty', () => {
    const df_input = [];

    const expected = [];

    const condition = {
      "comparison": "equals",
      "column": "question_id",
      "value": 1
    }
    const values = {
      "score": 0
    }

    let result = setWhere(df_input, condition, values)
    assert.deepStrictEqual(result, expected, "Works when input dataframe is empty")
  });

  it('replaces single value where condition is met', () => {
    const df_input = [
      { taqr_id: 1, formatted_response: 'A', score: 1 },
      { taqr_id: 2, formatted_response: 'B', score: 1 },
      { taqr_id: 3, formatted_response: 'C', score: 1 },
      { taqr_id: 4, formatted_response: 'A', score: 1 },
      { taqr_id: 5, formatted_response: 'B', score: 1 },
      { taqr_id: 6, formatted_response: 'C', score: 1 },
    ];
    const unchanged = structuredClone(df_input);

    const expected = [
      { taqr_id: 1, formatted_response: 'A', score: 0 },
      { taqr_id: 2, formatted_response: 'B', score: 1 },
      { taqr_id: 3, formatted_response: 'C', score: 1 },
      { taqr_id: 4, formatted_response: 'A', score: 0 },
      { taqr_id: 5, formatted_response: 'B', score: 1 },
      { taqr_id: 6, formatted_response: 'C', score: 1 },
    ];

    const condition = {
      "comparison": "equals",
      "col": "formatted_response",
      "value": 'A'
    }
    const values = {
      "score": 0
    }

    let result = setWhere(df_input, condition, values)
    assert.deepStrictEqual(expected, result, "did not replace values properly")
    assert.deepStrictEqual(unchanged, df_input, "Input dataframe was changed");
  });

  it('replaces multiple values where condition is met', () => {
    const df_input = [
      { taqr_id: 1, formatted_response: 'A', score: 1 },
      { taqr_id: 2, formatted_response: 'B', score: 1 },
      { taqr_id: 3, formatted_response: 'C', score: 1 },
      { taqr_id: 4, formatted_response: 'A', score: 1 },
      { taqr_id: 5, formatted_response: 'B', score: 1 },
      { taqr_id: 6, formatted_response: 'C', score: 1 },
    ];
    const unchanged = structuredClone(df_input);

    const expected = [
      { taqr_id: 1, formatted_response: 'A', score: 0, is_correct: false },
      { taqr_id: 2, formatted_response: 'B', score: 1 },
      { taqr_id: 3, formatted_response: 'C', score: 1 },
      { taqr_id: 4, formatted_response: 'A', score: 0, is_correct: false },
      { taqr_id: 5, formatted_response: 'B', score: 1 },
      { taqr_id: 6, formatted_response: 'C', score: 1 },
    ];

    const condition = {
      "comparison": "equals",
      "col": "formatted_response",
      "value": 'A'
    }
    const values = {
      "score": 0,
      "is_correct": false
    }

    let result = setWhere(df_input, condition, values)
    assert.deepStrictEqual(result, expected, "did not replace values properly")
    assert.deepStrictEqual(unchanged, df_input, "Input dataframe was changed");
  });
  // TODO: more unit tests, like if columns are missing

});

describe('data-exporter | data-export-queries | transforms :: set-where inplace', () => {
  // Empty DataFrame or argument {{{
  it('works when input dataframe is empty', () => {
    const df_input = [];

    const expected = [];

    const condition = {
      "comparison": "equals",
      "col": "question_id",
      "value": 1
    }
    const values = {
      "score": 0
    }

    let result = setWhere(df_input, condition, values)
    assert.deepStrictEqual(result, expected, "Works when input dataframe is empty")
  });

  it('replaces single value where condition is met', () => {
    const df_input = [
      { taqr_id: 1, formatted_response: 'A', score: 1 },
      { taqr_id: 2, formatted_response: 'B', score: 1 },
      { taqr_id: 3, formatted_response: 'C', score: 1 },
      { taqr_id: 4, formatted_response: 'A', score: 1 },
      { taqr_id: 5, formatted_response: 'B', score: 1 },
      { taqr_id: 6, formatted_response: 'C', score: 1 },
    ];
    const unchanged = structuredClone(df_input);

    const expected = [
      { taqr_id: 1, formatted_response: 'A', score: 0 },
      { taqr_id: 2, formatted_response: 'B', score: 1 },
      { taqr_id: 3, formatted_response: 'C', score: 1 },
      { taqr_id: 4, formatted_response: 'A', score: 0 },
      { taqr_id: 5, formatted_response: 'B', score: 1 },
      { taqr_id: 6, formatted_response: 'C', score: 1 },
    ];

    const condition = {
      "comparison": "equals",
      "col": "formatted_response",
      "value": 'A'
    }
    const values = {
      "score": 0
    }

    let result = setWhereInplace(df_input, condition, values)
    assert.deepStrictEqual(result, expected, "did not replace values properly")
    assert.deepStrictEqual(result, df_input, "Input dataframe was not changed");
    assert.notDeepStrictEqual(unchanged, df_input, "Input dataframe should have changed")
  });

  it('replaces multiple values where condition is met', () => {
    const df_input = [
      { taqr_id: 1, formatted_response: 'A', score: 1 },
      { taqr_id: 2, formatted_response: 'B', score: 1 },
      { taqr_id: 3, formatted_response: 'C', score: 1 },
      { taqr_id: 4, formatted_response: 'A', score: 1 },
      { taqr_id: 5, formatted_response: 'B', score: 1 },
      { taqr_id: 6, formatted_response: 'C', score: 1 },
    ];
    const unchanged = structuredClone(df_input);

    const expected = [
      { taqr_id: 1, formatted_response: 'A', score: 0, is_correct: false },
      { taqr_id: 2, formatted_response: 'B', score: 1 },
      { taqr_id: 3, formatted_response: 'C', score: 1 },
      { taqr_id: 4, formatted_response: 'A', score: 0, is_correct: false },
      { taqr_id: 5, formatted_response: 'B', score: 1 },
      { taqr_id: 6, formatted_response: 'C', score: 1 },
    ];

    const condition = {
      "comparison": "equals",
      "col": "formatted_response",
      "value": 'A'
    }
    const values = {
      "score": 0,
      "is_correct": false
    }

    let result = setWhereInplace(df_input, condition, values)
    assert.deepStrictEqual(result, expected, "did not replace values properly")
    assert.deepStrictEqual(result, df_input, "Input dataframe was not changed");
    assert.notDeepStrictEqual(unchanged, df_input, "Input dataframe should have changed")
  });
  // TODO: more unit tests, like if columns are missing

});
