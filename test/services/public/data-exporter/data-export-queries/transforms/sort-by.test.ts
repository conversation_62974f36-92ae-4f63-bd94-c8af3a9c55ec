import assert from 'assert';
import fs from 'fs';

import { sort_by } from '../../../../../../src/services/public/data-exporter/data-export-queries/transforms/sort-by'

describe('data-exporter | data-export-queries | transforms :: sort_by', () => {
  // Empty DataFrame {{{
  it('works -- input dataframe is empty', () => {
    const df_input = [];

    const expected = [];

    let result = sort_by(df_input, ["item_id"], [true])
    assert.deepStrictEqual(result, expected, "Works when input dataframe is empty")
  });
  // }}}

  // Invalid Inputs {{{
  it('throws an error when specified column is not present in input', () => {
    // TODO: Confirm implementation - if we skip or throw an exception
    const df_input = [
      { item_id: 1, formatted_response: 'A', score: 5 },
      { item_id: 4, formatted_response: 'B', score: 100 },
      { item_id: 2, formatted_response: 'C', score: 10 },
      { item_id: 3, formatted_response: 'A', score: 50 }
    ];

    assert.throws(() => {
      sort_by(df_input, ["taqr_id"], [true])
    }, Error, "Error thrown for column not found");
  });
  // }}}

  // Sorting {{{
  it('works -- sorting on 1 column, ascending', () => {
    const df_input = [
      { item_id: 1, formatted_response: 'A', score: 5 },
      { item_id: 4, formatted_response: 'B', score: 100 },
      { item_id: 2, formatted_response: 'C', score: 10 },
      { item_id: 3, formatted_response: 'A', score: 50 }
    ];

    const expected = [
      { item_id: 1, formatted_response: 'A', score: 5 },
      { item_id: 2, formatted_response: 'C', score: 10 },
      { item_id: 3, formatted_response: 'A', score: 50 },
      { item_id: 4, formatted_response: 'B', score: 100 }
    ];

    let result = sort_by(df_input, ["item_id"], [true])
    assert.deepStrictEqual(result, expected, "Works correctly when sorting on 1 column")
  });

  it('works -- sorting on 2 columns, mixed ascending and descending', () => {
    const df_input = [
      { item_id: 1, formatted_response: 'A', score: 5 },
      { item_id: 4, formatted_response: 'B', score: 100 },
      { item_id: 2, formatted_response: 'C', score: 10 },
      { item_id: 3, formatted_response: 'A', score: 50 },
      { item_id: 1, formatted_response: 'B', score: 50 }
    ];

    const expected = [
      { item_id: 1, formatted_response: 'B', score: 50 },
      { item_id: 1, formatted_response: 'A', score: 5 },
      { item_id: 2, formatted_response: 'C', score: 10 },
      { item_id: 3, formatted_response: 'A', score: 50 },
      { item_id: 4, formatted_response: 'B', score: 100 }
    ];

    let result = sort_by(df_input, ["item_id", "formatted_response"], [true, false])
    assert.deepStrictEqual(result, expected, "Works correctly when sorting on 2 columns")
  });

  it('works -- all rows are the same', () => {
    const df_input = [
      { item_id: 1, formatted_response: 'A', score: 5 },
      { item_id: 1, formatted_response: 'A', score: 5 },
      { item_id: 1, formatted_response: 'A', score: 5 },
      { item_id: 1, formatted_response: 'A', score: 5 }
    ];

    const expected = [
      { item_id: 1, formatted_response: 'A', score: 5 },
      { item_id: 1, formatted_response: 'A', score: 5 },
      { item_id: 1, formatted_response: 'A', score: 5 },
      { item_id: 1, formatted_response: 'A', score: 5 }
    ];

    let result = sort_by(df_input, ["item_id"], [true])
    assert.deepStrictEqual(result, expected, "Works correctly when all rows are the same")
  });

  it('works with single null values in sort column', () => {
    const df_input = [
      { item_id: 1, formatted_response: 'A', score: 5 },
      { item_id: 4, formatted_response: 'B', score: 100 },
      { item_id: 2, formatted_response: 'C', score: null },
      { item_id: 3, formatted_response: 'A', score: 50 },
      { item_id: 1, formatted_response: 'B', score: 70 }
    ];

    const expected = [
      { item_id: 2, formatted_response: 'C', score: null },
      { item_id: 1, formatted_response: 'A', score: 5 },
      { item_id: 3, formatted_response: 'A', score: 50 },
      { item_id: 1, formatted_response: 'B', score: 70 },
      { item_id: 4, formatted_response: 'B', score: 100 }
    ];

    let result = sort_by(df_input, ["score"], [true])
    assert.deepStrictEqual(result, expected, "Works correctly with null values in sort column")
  });

  it('works with multiple null values in sort column', () => {
    const df_input = [
      { item_id: 1, formatted_response: 'A', score: 5 },
      { item_id: 2, formatted_response: 'C', score: null },
      { item_id: 4, formatted_response: 'B', score: null },
      { item_id: 3, formatted_response: 'A', score: 50 },
      { item_id: 1, formatted_response: 'B', score: 70 },
    ];

    const expected = [
      { item_id: 2, formatted_response: 'C', score: null }, // Stable sort
      { item_id: 4, formatted_response: 'B', score: null },
      { item_id: 1, formatted_response: 'A', score: 5 },
      { item_id: 3, formatted_response: 'A', score: 50 },
      { item_id: 1, formatted_response: 'B', score: 70 },
    ];

    let result = sort_by(df_input, ["score"], [true])
    assert.deepStrictEqual(result, expected, "Works correctly with null values in sort column")
  });
  it('works with multiple null values in sort columns', () => {
    const df_input = [
      { item_id: 1, formatted_response: 'A', score: 5 },
      { item_id: 2, formatted_response: 'C', score: null },
      { item_id: 4, formatted_response: 'B', score: null },
      { item_id: 3, formatted_response: 'A', score: 50 },
      { item_id: 1, formatted_response: 'B', score: 70 },
    ];

    const expected = [
      { item_id: 4, formatted_response: 'B', score: null },
      { item_id: 2, formatted_response: 'C', score: null },
      { item_id: 1, formatted_response: 'A', score: 5 },
      { item_id: 3, formatted_response: 'A', score: 50 },
      { item_id: 1, formatted_response: 'B', score: 70 },
    ];

    let result = sort_by(df_input, ["score", "formatted_response"], [true, true])
    assert.deepStrictEqual(result, expected, "Works correctly with null values in sort columns")
  });
  // }}}
});
