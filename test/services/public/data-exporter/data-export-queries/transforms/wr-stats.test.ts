import assert from 'assert';
import fs from 'fs';

import { wr_stats } from '../../../../../../src/services/public/data-exporter/data-export-queries/transforms/wr-stats'

describe('data-exporter | data-export-queries | transforms :: group_by', () => {
  it('works -- with html string with two sentence', () => {
    const df_input = [
      { taqr_id: 1, formatted_response: '<p>This is a sentence.</p> <p>Another sentence here!</p>' },
    ];

    const expected = [
      { taqr_id: 1, formatted_response: '<p>This is a sentence.</p> <p>Another sentence here!</p>', character_count: 34, word_count: 7, sentence_count: 2 },
    ];

    // Input args
    let column = "formatted_response"    
    let result = wr_stats(df_input, column)
    assert.deepStrictEqual(result, expected, "Works for html string with one sentence");
  });

  it('works -- when decoding HTML entities', () => {
    const df_input = [
      { taqr_id: 1, formatted_response: '<div>Some&nbsp;text&#160;here</div>' },
      { taqr_id: 1, formatted_response: '&lt;p&gt;This is a test&lt;/p&gt;' },
    ];

    const expected = [
      { taqr_id: 1, formatted_response: '<div>Some&nbsp;text&#160;here</div>', character_count: 12, word_count: 3, sentence_count: 1 },
      { taqr_id: 1, formatted_response: '&lt;p&gt;This is a test&lt;/p&gt;', character_count: 11, word_count: 4, sentence_count: 1 },
    ];

    // Input args
    let column = "formatted_response"    
    let result = wr_stats(df_input, column)
    assert.deepStrictEqual(result, expected, "Works for html string with one sentence");
  });
});