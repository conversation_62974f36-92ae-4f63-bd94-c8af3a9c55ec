import assert from 'assert';

import { launchPackager } from '../../../../../src/services/public/data-exporter/data-export/storage/_index';

describe('data-exporter | data-packager', () => {
  // Skipped to prevent re-packing the same export over and over.
  it.skip('launches', () => {
    return launchPackager(20250021)
      .then(() => {
        assert(true);
      })
      .catch((err) => {
        assert(false, err);
      });
  });

});
