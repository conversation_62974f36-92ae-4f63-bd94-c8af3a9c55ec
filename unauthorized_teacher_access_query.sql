-- Query to identify teachers who have classes in test windows they should not have access to
-- This query checks for violations of the school assessment access control system

WITH school_allowed_assessments AS (
  -- Get all assessments that each school is allowed to access via school_assessments_map
  SELECT 
    s.id as school_id,
    s.group_id as school_group_id,
    s.name as school_name,
    ad.assessment_slug,
    ad.label as assessment_label
  FROM schools s
  JOIN school_assessments_map sam ON sam.school_id = s.id
  JOIN assessment_def ad ON ad.id = sam.assessment_def_id
  WHERE s.is_active = 1
),



teacher_class_test_windows AS (
  -- Get all teachers and their classes with associated test windows
  SELECT 
    u.id as teacher_uid,
    u.first_name,
    u.last_name,
    u.contact_email,
    sc.id as class_id,
    sc.group_id as class_group_id,
    sc.name as class_name,
    sc.schl_group_id as school_group_id,
    s.id as school_id,
    s.name as school_name,
    ss.test_window_id,
    tw.type_slug as test_window_type_slug,
    tw.title as test_window_title,
    tw.date_start,
    tw.date_end
  FROM user_roles ur
  JOIN users u ON u.id = ur.uid
  JOIN school_classes sc ON sc.group_id = ur.group_id
  JOIN school_semesters ss ON ss.id = sc.semester_id
  JOIN test_windows tw ON tw.id = ss.test_window_id
  JOIN schools s ON s.group_id = sc.schl_group_id
  WHERE ur.role_type = 'schl_teacher'
    AND ur.is_revoked != 1
    AND sc.is_active = 1
    AND tw.is_active = 1
),

unauthorized_access AS (
  -- Find teachers with classes in test windows their school doesn't have access to
  SELECT
    tctw.*,
    CASE
      WHEN sla.school_id IS NOT NULL THEN 'school_assessment_map'
      ELSE 'no_access'
    END as access_status,
    sla.assessment_slug as allowed_assessment_slug
  FROM teacher_class_test_windows tctw
  LEFT JOIN school_allowed_assessments sla ON (
    sla.school_id = tctw.school_id
    AND tctw.test_window_type_slug LIKE CONCAT(sla.assessment_slug, '%')
  )
  WHERE sla.school_id IS NULL
)

-- Final result: Teachers with unauthorized access
SELECT 
  teacher_uid,
  first_name,
  last_name,
  contact_email,
  school_name,
  class_name,
  class_group_id,
  test_window_title,
  test_window_type_slug,
  date_start,
  date_end,
  access_status,
  CONCAT('Teacher ', first_name, ' ', last_name, ' (UID: ', teacher_uid, ') has class "', 
         class_name, '" in test window "', test_window_title, '" (', test_window_type_slug, 
         ') but school "', school_name, '" does not have access to this assessment type.') as violation_description
FROM unauthorized_access
ORDER BY school_name, last_name, first_name, test_window_title;

-- Additional summary query to get counts by school
-- Uncomment the following section to get summary statistics:

/*
SELECT 
  school_name,
  COUNT(DISTINCT teacher_uid) as num_teachers_with_violations,
  COUNT(DISTINCT class_group_id) as num_classes_with_violations,
  COUNT(DISTINCT test_window_id) as num_test_windows_involved,
  COUNT(*) as total_violations
FROM unauthorized_access
GROUP BY school_name, school_id
ORDER BY total_violations DESC;
*/
